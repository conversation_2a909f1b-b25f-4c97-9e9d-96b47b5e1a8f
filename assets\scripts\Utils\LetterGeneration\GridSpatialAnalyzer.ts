import { Logger } from '../Logger';
import { SpatialDistributionManager } from './SpatialDistributionManager';

/**
 * 网格空间分析器
 * 
 * 专门负责分析网格中的空间分布情况，提供空间密度、区域分析等功能。
 * 遵循单一职责原则，只处理空间分析相关的逻辑。
 * 
 * 核心功能：
 * - 空间分布分析
 * - 区域密度计算
 * - 优先级位置生成
 * - 质心计算
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-20
 */
export class GridSpatialAnalyzer {
    
    // 网格常量
    private static readonly GRID_ROWS = 9;
    private static readonly GRID_COLS = 8;

    /**
     * 分析当前空间分布情况
     * @param placedWords 已放置的单词
     * @returns 空间分析结果
     */
    public static analyzeSpatialDistribution(
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): {
        quadrantCounts: Map<number, number>;
        occupiedPositions: Set<number>;
        centerOfMass: {row: number, col: number};
        spreadRadius: number;
    } {
        const quadrantCounts = new Map<number, number>();
        const occupiedPositions = new Set<number>();
        let totalRow = 0, totalCol = 0;

        // 初始化象限计数
        for (let i = 1; i <= 4; i++) {
            quadrantCounts.set(i, 0);
        }

        for (const wordInfo of placedWords) {
            const row = wordInfo.row;
            const col = wordInfo.col;
            
            // 确定象限
            const quadrant = SpatialDistributionManager.getQuadrantForPosition(row, col);
            quadrantCounts.set(quadrant, (quadrantCounts.get(quadrant) || 0) + 1);
            
            // 记录占用位置
            const position = row * this.GRID_COLS + col;
            occupiedPositions.add(position);
            
            // 计算质心
            totalRow += row;
            totalCol += col;
        }

        const centerOfMass = placedWords.length > 0 ? {
            row: totalRow / placedWords.length,
            col: totalCol / placedWords.length
        } : { row: 4, col: 3 };

        // 计算分布半径
        let maxDistance = 0;
        for (const wordInfo of placedWords) {
            const distance = Math.sqrt(
                Math.pow(wordInfo.row - centerOfMass.row, 2) + 
                Math.pow(wordInfo.col - centerOfMass.col, 2)
            );
            maxDistance = Math.max(maxDistance, distance);
        }

        return {
            quadrantCounts,
            occupiedPositions,
            centerOfMass,
            spreadRadius: maxDistance
        };
    }

    /**
     * 分析区域密度
     * @param grid 网格
     * @param placedWords 已放置单词
     * @returns 区域分析结果
     */
    public static analyzeRegionDensity(
        grid: string[][], 
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): {
        densityMap: number[][];
        emptyRegions: number[];
        crowdedRegions: number[];
        preferredRegions: number[];
    } {
        // 创建密度地图
        const densityMap: number[][] = [];
        for (let row = 0; row < this.GRID_ROWS; row++) {
            densityMap[row] = new Array(this.GRID_COLS).fill(0);
        }

        // 计算每个位置的密度（周围3x3区域的单词数量）
        for (const wordInfo of placedWords) {
            for (let dr = -1; dr <= 1; dr++) {
                for (let dc = -1; dc <= 1; dc++) {
                    const newRow = wordInfo.row + dr;
                    const newCol = wordInfo.col + dc;
                    
                    if (newRow >= 0 && newRow < this.GRID_ROWS && 
                        newCol >= 0 && newCol < this.GRID_COLS) {
                        densityMap[newRow][newCol]++;
                    }
                }
            }
        }

        // 分类区域
        const emptyRegions: number[] = [];
        const crowdedRegions: number[] = [];
        const preferredRegions: number[] = [];

        for (let row = 0; row < this.GRID_ROWS; row++) {
            for (let col = 0; col < this.GRID_COLS; col++) {
                const position = row * this.GRID_COLS + col;
                const density = densityMap[row][col];

                if (density === 0) {
                    emptyRegions.push(position);
                } else if (density >= 3) {
                    crowdedRegions.push(position);
                } else {
                    preferredRegions.push(position);
                }
            }
        }

        return {
            densityMap,
            emptyRegions,
            crowdedRegions,
            preferredRegions
        };
    }

    /**
     * 生成优先级位置序列
     * @param spatialAnalysis 空间分析结果
     * @param regionAnalysis 区域分析结果
     * @returns 优先级排序的位置数组
     */
    public static generatePrioritizedPositions(
        spatialAnalysis: {
            quadrantCounts: Map<number, number>;
            occupiedPositions: Set<number>;
            centerOfMass: {row: number, col: number};
            spreadRadius: number;
        },
        regionAnalysis: {
            densityMap: number[][];
            emptyRegions: number[];
            crowdedRegions: number[];
            preferredRegions: number[];
        }
    ): number[] {
        const prioritizedPositions: number[] = [];
        
        // 优先级1：空白区域中的象限平衡位置
        const balancedEmptyPositions = this._getQuadrantBalancedPositions(
            regionAnalysis.emptyRegions, 
            spatialAnalysis.quadrantCounts
        );
        
        // 优先级2：远离质心的空白位置
        const distantEmptyPositions = this._getDistantPositions(
            regionAnalysis.emptyRegions,
            spatialAnalysis.centerOfMass
        );
        
        // 优先级3：适度密度区域的边缘位置
        const edgePreferredPositions = this._getEdgePositions(regionAnalysis.preferredRegions);
        
        // 🔧 优化：使用Set提高查找性能
        const balancedSet = new Set(balancedEmptyPositions);
        const distantSet = new Set(distantEmptyPositions);
        const edgeSet = new Set(edgePreferredPositions);

        // 优先级4：其他空白位置
        const remainingEmptyPositions = regionAnalysis.emptyRegions.filter((pos: number) =>
            !balancedSet.has(pos) && !distantSet.has(pos)
        );

        // 优先级5：适度密度区域
        const remainingPreferredPositions = regionAnalysis.preferredRegions.filter((pos: number) =>
            !edgeSet.has(pos)
        );

        // 组合并随机化各优先级
        this._shuffleArray(balancedEmptyPositions);
        this._shuffleArray(distantEmptyPositions);
        this._shuffleArray(edgePreferredPositions);
        this._shuffleArray(remainingEmptyPositions);
        this._shuffleArray(remainingPreferredPositions);

        // 按优先级顺序添加
        prioritizedPositions.push(...balancedEmptyPositions);
        prioritizedPositions.push(...distantEmptyPositions);
        prioritizedPositions.push(...edgePreferredPositions);
        prioritizedPositions.push(...remainingEmptyPositions);
        prioritizedPositions.push(...remainingPreferredPositions);

        Logger.info('GridSpatialAnalyzer', 
            `📊 优先级分布：平衡空白${balancedEmptyPositions.length}，远距空白${distantEmptyPositions.length}，边缘适度${edgePreferredPositions.length}`);

        return prioritizedPositions;
    }

    /**
     * 获取象限平衡的位置
     * @param emptyPositions 空白位置数组
     * @param quadrantCounts 象限计数
     * @returns 平衡位置数组
     */
    private static _getQuadrantBalancedPositions(
        emptyPositions: number[], 
        quadrantCounts: Map<number, number>
    ): number[] {
        const balancedPositions: number[] = [];
        
        // 找出单词数量最少的象限
        const minCount = Math.min(...Array.from(quadrantCounts.values()));
        const underPopulatedQuadrants = Array.from(quadrantCounts.entries())
            .filter(([_, count]) => count === minCount)
            .map(([quadrant, _]) => quadrant);

        // 优先选择人口不足象限中的空白位置
        for (const position of emptyPositions) {
            const row = Math.floor(position / this.GRID_COLS);
            const col = position % this.GRID_COLS;
            const quadrant = SpatialDistributionManager.getQuadrantForPosition(row, col);
            
            if (underPopulatedQuadrants.includes(quadrant)) {
                balancedPositions.push(position);
            }
        }

        return balancedPositions;
    }

    /**
     * 获取远离质心的位置
     * @param emptyPositions 空白位置数组
     * @param centerOfMass 质心位置
     * @returns 远距离位置数组
     */
    private static _getDistantPositions(
        emptyPositions: number[], 
        centerOfMass: {row: number, col: number}
    ): number[] {
        const positionsWithDistance = emptyPositions.map(position => {
            const row = Math.floor(position / this.GRID_COLS);
            const col = position % this.GRID_COLS;
            const distance = Math.sqrt(
                Math.pow(row - centerOfMass.row, 2) + 
                Math.pow(col - centerOfMass.col, 2)
            );
            return { position, distance };
        });

        // 按距离降序排序，取前50%
        positionsWithDistance.sort((a, b) => b.distance - a.distance);
        const halfCount = Math.ceil(positionsWithDistance.length / 2);
        
        return positionsWithDistance.slice(0, halfCount).map(item => item.position);
    }

    /**
     * 获取边缘位置
     * @param preferredPositions 适度密度位置数组
     * @returns 边缘位置数组
     */
    private static _getEdgePositions(preferredPositions: number[]): number[] {
        return preferredPositions.filter(position => {
            const row = Math.floor(position / this.GRID_COLS);
            const col = position % this.GRID_COLS;
            
            return row === 0 || row === this.GRID_ROWS - 1 || 
                   col === 0 || col === this.GRID_COLS - 1;
        });
    }

    /**
     * 随机打乱数组
     * @param array 要打乱的数组
     */
    private static _shuffleArray<T>(array: T[]): void {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
}
