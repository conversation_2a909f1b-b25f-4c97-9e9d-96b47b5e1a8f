import { _decorator } from 'cc';
import { ILetterData } from '../../Data/WordDatabase';
import { Logger } from '../../Utils/Logger';

const { ccclass } = _decorator;

/**
 * 网格数据管理器 - 专门负责字母网格数据的管理和操作
 * 遵循单一职责原则，专注于数据管理，不涉及UI操作
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-07
 */
@ccclass('GridDataManager')
export class GridDataManager {

    /**
     * 网格尺寸常量
     */
    public static readonly GRID_ROWS = 9;
    public static readonly GRID_COLS = 8;
    public static readonly TOTAL_CELLS = GridDataManager.GRID_ROWS * GridDataManager.GRID_COLS;

    /**
     * 字母网格数据 (9行8列)
     */
    private _letterGrid: ILetterData[][] = [];

    /**
     * 是否已初始化
     */
    private _isInitialized: boolean = false;

    /**
     * 初始化网格数据结构
     */
    public initialize(): void {
        Logger.info('GridDataManager', '初始化网格数据管理器');
        
        this._letterGrid = [];
        
        // 创建9行8列的数据结构
        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            this._letterGrid[row] = [];
            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                this._letterGrid[row][col] = {
                    letter: '',
                    isSelected: false,
                    isAvailable: true
                };
            }
        }
        
        this._isInitialized = true;
        Logger.info('GridDataManager', `网格数据初始化完成 (${GridDataManager.GRID_ROWS}x${GridDataManager.GRID_COLS})`);
    }

    /**
     * 检查是否已初始化
     */
    public isInitialized(): boolean {
        return this._isInitialized;
    }

    /**
     * 设置字母网格数据
     * @param letterGrid 字母网格 (9行8列)
     */
    public setLetterGridData(letterGrid: string[][]): boolean {
        if (!this._isInitialized) {
            Logger.error('GridDataManager', '数据管理器未初始化');
            return false;
        }

        if (!this._validateGridInput(letterGrid)) {
            return false;
        }

        // 清空当前数据
        this.clearGrid();

        // 设置新的字母数据
        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                this._letterGrid[row][col].letter = letterGrid[row][col] || '';
                this._letterGrid[row][col].isSelected = false;
                this._letterGrid[row][col].isAvailable = true;
            }
        }

        Logger.debug('GridDataManager', '字母网格数据设置完成');
        return true;
    }

    /**
     * 直接设置字母数组到网格
     * @param letters 字母数组（应该正好72个）
     */
    public setLettersDirectly(letters: string[]): boolean {
        if (!this._isInitialized) {
            Logger.error('GridDataManager', '数据管理器未初始化');
            return false;
        }

        if (!letters || letters.length !== GridDataManager.TOTAL_CELLS) {
            Logger.error('GridDataManager', `字母数量不匹配！期望${GridDataManager.TOTAL_CELLS}个，实际${letters?.length || 0}个`);
            return false;
        }

        // 清空网格
        this.clearGrid();

        // 直接按顺序设置字母到网格
        let letterIndex = 0;
        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                if (letterIndex < letters.length) {
                    this._letterGrid[row][col].letter = letters[letterIndex];
                    this._letterGrid[row][col].isSelected = false;
                    this._letterGrid[row][col].isAvailable = true;
                    letterIndex++;
                }
            }
        }

        Logger.debug('GridDataManager', '字母数组设置完成');
        return true;
    }

    /**
     * 获取指定位置的字母数据
     * @param row 行索引
     * @param col 列索引
     */
    public getLetterData(row: number, col: number): ILetterData | null {
        if (!this._isValidPosition(row, col)) {
            return null;
        }

        return this._letterGrid[row][col];
    }

    /**
     * 获取指定位置的字母
     * @param row 行索引
     * @param col 列索引
     */
    public getLetter(row: number, col: number): string {
        const letterData = this.getLetterData(row, col);
        return letterData ? letterData.letter : '';
    }

    /**
     * 设置指定位置的字母
     * @param row 行索引
     * @param col 列索引
     * @param letter 字母
     */
    public setLetter(row: number, col: number, letter: string): boolean {
        if (!this._isValidPosition(row, col)) {
            return false;
        }

        this._letterGrid[row][col].letter = letter;
        return true;
    }

    /**
     * 设置字母选中状态
     * @param row 行索引
     * @param col 列索引
     * @param selected 是否选中
     */
    public setLetterSelected(row: number, col: number, selected: boolean): boolean {
        if (!this._isValidPosition(row, col)) {
            return false;
        }

        this._letterGrid[row][col].isSelected = selected;
        return true;
    }

    /**
     * 设置字母可用状态
     * @param row 行索引
     * @param col 列索引
     * @param available 是否可用
     */
    public setLetterAvailable(row: number, col: number, available: boolean): boolean {
        if (!this._isValidPosition(row, col)) {
            return false;
        }

        this._letterGrid[row][col].isAvailable = available;
        return true;
    }

    /**
     * 检查字母是否被选中
     * @param row 行索引
     * @param col 列索引
     */
    public isLetterSelected(row: number, col: number): boolean {
        const letterData = this.getLetterData(row, col);
        return letterData ? letterData.isSelected : false;
    }

    /**
     * 检查字母是否可用
     * @param row 行索引
     * @param col 列索引
     */
    public isLetterAvailable(row: number, col: number): boolean {
        const letterData = this.getLetterData(row, col);
        return letterData ? letterData.isAvailable : false;
    }

    /**
     * 清空网格数据
     */
    public clearGrid(): void {
        if (!this._isInitialized) {
            return;
        }

        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                this._letterGrid[row][col].letter = '';
                this._letterGrid[row][col].isSelected = false;
                this._letterGrid[row][col].isAvailable = true;
            }
        }
    }

    /**
     * 获取完整的网格数据副本
     */
    public getGridData(): ILetterData[][] {
        if (!this._isInitialized) {
            return [];
        }

        // 返回深拷贝，避免外部修改
        const gridCopy: ILetterData[][] = [];
        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            gridCopy[row] = [];
            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                gridCopy[row][col] = {
                    letter: this._letterGrid[row][col].letter,
                    isSelected: this._letterGrid[row][col].isSelected,
                    isAvailable: this._letterGrid[row][col].isAvailable
                };
            }
        }
        return gridCopy;
    }

    /**
     * 获取所有字母的一维数组
     */
    public getLettersArray(): string[] {
        if (!this._isInitialized) {
            return [];
        }

        const letters: string[] = [];
        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                letters.push(this._letterGrid[row][col].letter);
            }
        }
        return letters;
    }

    /**
     * 获取网格尺寸信息
     */
    public getGridSize(): { rows: number, cols: number, totalCells: number } {
        return {
            rows: GridDataManager.GRID_ROWS,
            cols: GridDataManager.GRID_COLS,
            totalCells: GridDataManager.TOTAL_CELLS
        };
    }

    /**
     * 验证网格输入数据
     * @param letterGrid 字母网格
     */
    private _validateGridInput(letterGrid: string[][]): boolean {
        if (!letterGrid || letterGrid.length !== GridDataManager.GRID_ROWS) {
            Logger.error('GridDataManager', `字母网格行数不正确，期望${GridDataManager.GRID_ROWS}行，实际${letterGrid?.length || 0}行`);
            return false;
        }

        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            if (!letterGrid[row] || letterGrid[row].length !== GridDataManager.GRID_COLS) {
                Logger.error('GridDataManager', `第${row + 1}行列数不正确，期望${GridDataManager.GRID_COLS}列，实际${letterGrid[row]?.length || 0}列`);
                return false;
            }
        }

        return true;
    }

    /**
     * 验证位置是否有效
     * @param row 行索引
     * @param col 列索引
     */
    private _isValidPosition(row: number, col: number): boolean {
        if (row < 0 || row >= GridDataManager.GRID_ROWS || col < 0 || col >= GridDataManager.GRID_COLS) {
            Logger.warn('GridDataManager', `位置超出范围: (${row}, ${col})`);
            return false;
        }
        return true;
    }

    /**
     * 获取数据统计信息
     */
    public getStatistics(): {
        totalCells: number;
        filledCells: number;
        emptyCells: number;
        selectedCells: number;
        availableCells: number;
    } {
        if (!this._isInitialized) {
            return {
                totalCells: 0,
                filledCells: 0,
                emptyCells: 0,
                selectedCells: 0,
                availableCells: 0
            };
        }

        let filledCells = 0;
        let selectedCells = 0;
        let availableCells = 0;

        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                const letterData = this._letterGrid[row][col];
                if (letterData.letter) {
                    filledCells++;
                }
                if (letterData.isSelected) {
                    selectedCells++;
                }
                if (letterData.isAvailable) {
                    availableCells++;
                }
            }
        }

        return {
            totalCells: GridDataManager.TOTAL_CELLS,
            filledCells,
            emptyCells: GridDataManager.TOTAL_CELLS - filledCells,
            selectedCells,
            availableCells
        };
    }

    /**
     * 重置数据管理器
     */
    public reset(): void {
        this._letterGrid = [];
        this._isInitialized = false;
        Logger.info('GridDataManager', '数据管理器已重置');
    }
}
