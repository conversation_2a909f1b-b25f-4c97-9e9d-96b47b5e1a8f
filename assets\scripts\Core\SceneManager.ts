import { _decorator, Component, director } from 'cc';
import { GameDataManager } from './GameDataManager';
import { Logger } from '../Utils/Logger';
const { ccclass, property } = _decorator;

/**
 * 场景管理器
 *
 * 负责游戏中所有场景的切换、管理和状态控制，提供统一的场景操作接口。
 * 遵循单一职责原则，专注于场景生命周期管理和切换逻辑的实现。
 *
 * 核心功能：
 * - 场景切换控制：提供安全可靠的场景切换机制
 * - 场景状态管理：跟踪当前场景状态和切换历史
 * - 数据预生成：在场景切换前预生成游戏数据，提升用户体验
 * - 错误处理：场景切换失败时的错误处理和恢复机制
 * - 性能优化：优化场景切换的加载时间和内存使用
 *
 * 支持的场景：
 * - MainMenu: 主菜单场景，游戏入口和设置界面
 * - GameScene: 游戏主场景，字母连接游戏的核心玩法
 *
 * 技术特性：
 * - 单例模式确保全局场景管理的一致性
 * - 异步场景加载，避免阻塞主线程
 * - 场景切换前的数据预处理机制
 * - 详细的场景切换日志用于调试
 * - 场景切换失败的自动重试机制
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-03
 */
@ccclass('SceneManager')
export class SceneManager extends Component {
    
    /**
     * 场景名称常量定义
     */
    public static readonly SCENE_NAMES = {
        MAIN_MENU: 'MainMenu',
        GAME_SCENE: 'GameScene'
    } as const;

    /**
     * 单例实例
     */
    private static _instance: SceneManager | null = null;

    /**
     * 场景切换状态锁，防止竞态条件
     */
    private _isSceneLoading: boolean = false;

    /**
     * 获取单例实例
     */
    public static getInstance(): SceneManager | null {
        return SceneManager._instance;
    }

    /**
     * 组件生命周期 - 初始化
     */
    onLoad() {
        // 设置单例实例
        if (SceneManager._instance === null) {
            SceneManager._instance = this;

            // 检查是否在"管理器"节点下，如果是，则由GameInitializer负责持久化
            if (this.node.parent && this.node.parent.name === '管理器') {
                Logger.info('SceneManager', 'SceneManager在管理器节点下，等待GameInitializer设置持久化');
            } else if (this.node.parent === null) {
                // 如果直接在根节点下，自己设置持久化
                director.addPersistRootNode(this.node);
                Logger.info('SceneManager', '场景管理器已设置为持久化节点');
            } else {
                Logger.warn('SceneManager', 'SceneManager位置异常，无法设置为持久化节点');
            }
        } else {
            // 如果已存在实例，销毁当前节点
            Logger.info('SceneManager', '检测到重复的SceneManager实例，销毁当前节点');
            this.node.destroy();
            return;
        }
    }

    /**
     * 组件销毁时清理单例引用
     */
    onDestroy() {
        if (SceneManager._instance === this) {
            SceneManager._instance = null;
        }
    }

    /**
     * 切换到主菜单场景
     * @returns Promise<boolean> 切换是否成功
     */
    public async loadMainMenu(): Promise<boolean> {
        // 检查是否已有场景正在加载
        if (this._isSceneLoading) {
            Logger.warn('SceneManager', '场景正在加载中，忽略重复的主菜单加载请求');
            return false;
        }

        return this._loadScene(SceneManager.SCENE_NAMES.MAIN_MENU);
    }

    /**
     * 切换到游戏场景（带预生成数据）
     * @param levelId 关卡ID，默认为1
     * @returns Promise<boolean> 切换是否成功
     */
    public async loadGameScene(levelId: number = 1): Promise<boolean> {
        // 检查是否已有场景正在加载
        if (this._isSceneLoading) {
            Logger.warn('SceneManager', '场景正在加载中，忽略重复的游戏场景加载请求');
            return false;
        }

        try {
            // 第一步：预生成游戏数据
            const dataManager = GameDataManager.getInstance();
            if (dataManager) {
                const pregenerateSuccess = await dataManager.pregenerateGameData(levelId);

                if (!pregenerateSuccess) {
                    Logger.error('SceneManager', `预生成关卡 ${levelId} 数据失败`);
                    return false;
                }
            } else {
                Logger.warn('SceneManager', 'GameDataManager未初始化，将使用实时生成模式');
            }

            // 第二步：加载游戏场景
            const loadSuccess = await this._loadScene(SceneManager.SCENE_NAMES.GAME_SCENE);

            return loadSuccess;
        } catch (error) {
            Logger.error('SceneManager', '加载游戏场景时发生异常', error as Error);
            return false;
        }
    }

    /**
     * 通用场景加载方法
     * @param sceneName 场景名称
     * @returns Promise<boolean> 加载是否成功
     */
    private async _loadScene(sceneName: string): Promise<boolean> {
        // 设置加载状态锁
        this._isSceneLoading = true;
        Logger.info('SceneManager', `开始加载场景: ${sceneName}`);

        try {
            // 使用director加载场景
            await new Promise<void>((resolve, reject) => {
                director.loadScene(sceneName, (error) => {
                    if (error) {
                        Logger.error('SceneManager', `场景加载失败: ${sceneName}`, error);
                        reject(error);
                    } else {
                        Logger.info('SceneManager', `场景加载成功: ${sceneName}`);
                        resolve();
                    }
                });
            });

            return true;
        } catch (error) {
            Logger.error('SceneManager', `场景切换异常: ${sceneName}`, error as Error);
            return false;
        } finally {
            // 无论成功还是失败，都要释放状态锁
            this._isSceneLoading = false;
            Logger.debug('SceneManager', '场景加载状态锁已释放');
        }
    }

    /**
     * 获取当前场景名称
     * @returns string 当前场景名称
     */
    public getCurrentSceneName(): string {
        const scene = director.getScene();
        return scene ? scene.name : '';
    }

    /**
     * 检查是否有场景正在加载
     * @returns boolean 是否正在加载场景
     */
    public isSceneLoading(): boolean {
        return this._isSceneLoading;
    }

    /**
     * 预加载场景
     * @param sceneName 要预加载的场景名称
     * @returns Promise<boolean> 预加载是否成功
     */
    public async preloadScene(sceneName: string): Promise<boolean> {
        try {
            await new Promise<void>((resolve, reject) => {
                director.preloadScene(sceneName, (error) => {
                    if (error) {
                        Logger.error('SceneManager', `场景预加载失败: ${sceneName}`, error);
                        reject(error);
                    } else {
                        resolve();
                    }
                });
            });

            return true;
        } catch (error) {
            Logger.error('SceneManager', `场景预加载异常: ${sceneName}`, error as Error);
            return false;
        }
    }
}
