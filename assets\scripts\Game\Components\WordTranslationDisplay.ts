import { _decorator, Component, Node, Label, UIOpacity, tween, Vec3, Color, UITransform, Enum, Font, AudioClip } from 'cc';
import { WordTranslationService } from '../Services/WordTranslationService';
import { AnimationManager } from '../Animation/AnimationManager';
import { ColorThemeManager } from '../Managers/ColorThemeManager';
import { AudioIntegrationService } from '../Services/AudioIntegrationService';
import { Logger } from '../../Utils/Logger';

const { ccclass, property } = _decorator;

/**
 * 单词翻译显示组件
 * 
 * 负责翻译文本的动态创建、定位和动画显示。
 * 采用动态创建UI节点的方式，不使用预制体，与项目现有模式保持一致。
 * 
 * 核心功能：
 * - 动态创建翻译文本节点
 * - 翻译文本定位和动画
 * - 节点生命周期管理
 * - 与翻译服务集成
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-04
 */
@ccclass('WordTranslationDisplay')
export class WordTranslationDisplay extends Component {
    
    /**
     * 是否启用翻译显示
     */
    @property({ displayName: "启用翻译显示", tooltip: "是否显示单词翻译" })
    enableTranslation: boolean = true;

    /**
     * 翻译容器节点（手动配置）
     */
    @property({ type: Node, displayName: "翻译容器", tooltip: "用于显示翻译文本的容器节点，可控制显示区域和层级" })
    translationContainer: Node = null!;

    // ==================== 翻译文本样式配置 ====================

    /**
     * 翻译文本字体资源
     */
    @property({ type: Font, displayName: "字体资源", tooltip: "翻译文本使用的字体资源，留空使用系统默认字体" })
    fontAsset: Font = null!;

    /**
     * 翻译文本字体大小
     */
    @property({ displayName: "字体大小", tooltip: "翻译文本的字体大小", range: [12, 72, 1] })
    fontSize: number = 32;

    /**
     * 翻译文本颜色
     */
    @property({ displayName: "文本颜色", tooltip: "翻译文本的颜色" })
    textColor: Color = new Color(255, 255, 100, 255);

    /**
     * 是否加粗显示
     */
    @property({ displayName: "字体加粗", tooltip: "是否以加粗样式显示翻译文本" })
    isBold: boolean = false;

    /**
     * 加粗实现方式
     */
    @property({
        type: Enum({
            FontSize: 0,    // 通过增加字体大小
            Outline: 1,     // 通过描边效果
            Both: 2         // 两种方式结合
        }),
        displayName: "加粗方式",
        tooltip: "加粗效果的实现方式",
        visible: function() { return this.isBold; }
    })
    boldMethod: number = 1; // 默认使用描边

    /**
     * 描边宽度（用于加粗效果）
     */
    @property({
        displayName: "描边宽度",
        tooltip: "用于加粗效果的描边宽度",
        range: [1, 5, 1],
        visible: function() { return this.isBold && (this.boldMethod === 1 || this.boldMethod === 2); }
    })
    outlineWidth: number = 2;

    /**
     * 翻译文本相对单词的Y轴偏移
     */
    @property({ displayName: "Y轴偏移", tooltip: "翻译文本相对于单词的Y轴偏移距离", range: [20, 150, 5] })
    offsetY: number = 80;

    // ==================== 动画配置 ====================

    /**
     * 出现动画持续时间
     */
    @property({ displayName: "出现动画时长", tooltip: "翻译文本出现动画的持续时间（秒）", range: [0.1, 1.0, 0.1] })
    appearDuration: number = 0.4;

    /**
     * 显示持续时间
     */
    @property({ displayName: "显示持续时长", tooltip: "翻译文本显示的持续时间（秒）", range: [0.5, 3.0, 0.1] })
    displayDuration: number = 1.2;

    /**
     * 消失动画持续时间
     */
    @property({ displayName: "消失动画时长", tooltip: "翻译文本消失动画的持续时间（秒）", range: [0.1, 1.0, 0.1] })
    disappearDuration: number = 0.4;

    /**
     * 初始缩放比例
     */
    @property({ displayName: "初始缩放", tooltip: "翻译文本的初始缩放比例", range: [0.1, 1.0, 0.1] })
    initialScale: number = 0.6;

    /**
     * 目标缩放比例
     */
    @property({ displayName: "目标缩放", tooltip: "翻译文本的目标缩放比例", range: [0.5, 2.0, 0.1] })
    targetScale: number = 1.0;

    /**
     * 缓动函数类型
     */
    @property({
        type: Enum({
            backOut: 0,
            bounceOut: 1,
            elasticOut: 2,
            sineOut: 3,
            quadOut: 4,
            cubicOut: 5
        }),
        displayName: "缓动类型",
        tooltip: "翻译文本出现动画的缓动函数类型"
    })
    easingType: number = 0; // 默认为backOut
    
    /**
     * 翻译服务实例
     */
    private _translationService: WordTranslationService = null!;

    /**
     * 动画管理器实例
     */
    private _animationManager: AnimationManager = null!;

    /**
     * 颜色主题管理器实例
     */
    private _colorThemeManager: ColorThemeManager = null!;



    /**
     * 当前活跃的翻译显示
     */
    private _activeTranslations: Set<Node> = new Set();

    /**
     * 组件初始化
     */
    onLoad(): void {
        this._initializeServices();
        this._validateTranslationContainer();
        Logger.info('WordTranslationDisplay', '翻译显示组件初始化完成');
    }

    /**
     * 获取缓动函数名称
     */
    private _getEasingName(): string {
        const easingNames = ['backOut', 'bounceOut', 'elasticOut', 'sineOut', 'quadOut', 'cubicOut'];
        return easingNames[this.easingType] || 'backOut';
    }

    /**
     * 应用字体设置到Label组件
     * @param label Label组件
     */
    private _applyFontSettings(label: Label): void {
        try {
            // 设置字体资源
            if (this.fontAsset) {
                label.font = this.fontAsset;
                Logger.debug('WordTranslationDisplay', `应用自定义字体: ${this.fontAsset.name}`);
            } else {
                // 使用系统默认字体
                label.useSystemFont = true;
                Logger.debug('WordTranslationDisplay', '使用系统默认字体');
            }

            // 设置字体样式（加粗）
            if (this.isBold) {
                this._applyBoldEffect(label);
            }

            Logger.debug('WordTranslationDisplay', `字体设置完成: 大小=${label.fontSize}, 加粗=${this.isBold}`);

        } catch (error) {
            Logger.error('WordTranslationDisplay', '应用字体设置失败', error as Error);
        }
    }

    /**
     * 应用加粗效果
     * @param label Label组件
     */
    private _applyBoldEffect(label: Label): void {
        try {
            switch (this.boldMethod) {
                case 0: // FontSize - 通过增加字体大小
                    label.fontSize = Math.round(this.fontSize * 1.08);
                    Logger.debug('WordTranslationDisplay', `通过字体大小实现加粗: ${label.fontSize}`);
                    break;

                case 1: // Outline - 通过描边效果
                    label.enableOutline = true;
                    label.outlineColor = this.textColor.clone();
                    label.outlineColor.a = Math.round(255 * 0.8); // 稍微透明的描边
                    label.outlineWidth = this.outlineWidth;
                    Logger.debug('WordTranslationDisplay', `通过描边实现加粗: 宽度=${this.outlineWidth}`);
                    break;

                case 2: // Both - 两种方式结合
                    // 增加字体大小
                    label.fontSize = Math.round(this.fontSize * 1.05);
                    // 添加描边
                    label.enableOutline = true;
                    label.outlineColor = this.textColor.clone();
                    label.outlineColor.a = Math.round(255 * 0.6); // 更透明的描边
                    label.outlineWidth = Math.max(1, this.outlineWidth - 1); // 稍细的描边
                    Logger.debug('WordTranslationDisplay', `通过组合方式实现加粗: 字体=${label.fontSize}, 描边=${label.outlineWidth}`);
                    break;

                default:
                    Logger.warn('WordTranslationDisplay', `未知的加粗方式: ${this.boldMethod}`);
                    break;
            }
        } catch (error) {
            Logger.error('WordTranslationDisplay', '应用加粗效果失败', error as Error);
        }
    }

    /**
     * 组件销毁时清理
     */
    onDestroy(): void {
        this._clearAllTranslations();
    }

    /**
     * 显示单词翻译
     * @param word 要翻译的单词
     * @param targetWordNode 目标单词节点
     */
    public async showTranslation(word: string, targetWordNode: Node): Promise<void> {
        if (!this.enableTranslation) {
            return;
        }

        // 参数验证
        if (!word || typeof word !== 'string' || !targetWordNode || !targetWordNode.isValid || !this._translationService) {
            Logger.warn('WordTranslationDisplay', `翻译显示参数无效: word=${word}, node=${!!targetWordNode}, service=${!!this._translationService}`);
            return;
        }

        try {
            // 1. 获取翻译
            const translation = await this._translationService.getTranslation(word);
            if (!translation) {
                Logger.debug('WordTranslationDisplay', `无翻译结果: ${word}`);
                return;
            }

            // 2. 创建翻译节点
            const translationNode = this._createTranslationNode(translation);

            // 3. 定位到目标单词上方
            this._positionTranslationNode(translationNode, targetWordNode);

            // 4. 播放动画序列
            await this._playTranslationAnimation(translationNode);

            // 5. 清理节点
            this._removeTranslationNode(translationNode);

            Logger.success('WordTranslationDisplay', `翻译显示完成: ${word} -> ${translation}`);

        } catch (error) {
            Logger.error('WordTranslationDisplay', `翻译显示失败: ${word}`, error as Error);
        }
    }

    /**
     * 初始化所有服务
     */
    private _initializeServices(): void {
        try {
            Logger.info('WordTranslationDisplay', '开始初始化服务');

            // 初始化翻译服务
            this._translationService = WordTranslationService.getInstance();
            if (this._translationService && this._translationService.isAvailable()) {
                const stats = this._translationService.getLocalDictStats();
                Logger.info('WordTranslationDisplay', `翻译服务初始化成功，支持${stats.totalWords}个单词`);
            } else {
                Logger.warn('WordTranslationDisplay', '翻译服务不可用');
            }

            // 初始化动画管理器
            this._animationManager = AnimationManager.getInstance();
            if (this._animationManager) {
                Logger.info('WordTranslationDisplay', '动画管理器初始化成功');
            } else {
                Logger.warn('WordTranslationDisplay', '动画管理器不可用');
            }

            // 初始化颜色主题管理器
            this._colorThemeManager = ColorThemeManager.getInstance();
            if (this._colorThemeManager) {
                Logger.info('WordTranslationDisplay', '颜色主题管理器初始化成功');
            } else {
                Logger.warn('WordTranslationDisplay', '颜色主题管理器不可用');
            }

        } catch (error) {
            Logger.error('WordTranslationDisplay', '初始化服务失败', error as Error);
        }
    }

    /**
     * 验证翻译容器配置
     */
    private _validateTranslationContainer(): void {
        if (!this.translationContainer) {
            Logger.warn('WordTranslationDisplay', '翻译容器未配置，将使用当前节点作为容器');
            this.translationContainer = this.node;
        } else {
            Logger.info('WordTranslationDisplay', `翻译容器配置成功: ${this.translationContainer.name}`);
        }

        // 确保容器有UITransform组件
        if (!this.translationContainer.getComponent(UITransform)) {
            this.translationContainer.addComponent(UITransform);
            Logger.info('WordTranslationDisplay', '为翻译容器添加UITransform组件');
        }
    }

    /**
     * 动态创建翻译节点（使用手动配置的属性）
     * @param translation 翻译文本
     * @returns 创建的翻译节点
     */
    private _createTranslationNode(translation: string): Node {
        // 创建主节点
        const translationNode = new Node('TranslationText');
        const transform = translationNode.addComponent(UITransform);
        const opacity = translationNode.addComponent(UIOpacity);

        // 设置节点属性（根据字体大小动态调整容器大小）
        const containerWidth = Math.max(200, translation.length * this.fontSize * 0.8);
        const containerHeight = this.fontSize + 20;
        transform.setContentSize(containerWidth, containerHeight);
        transform.setAnchorPoint(0.5, 0.5);

        // 创建Label组件
        const label = translationNode.addComponent(Label);
        label.string = translation;
        label.fontSize = this.fontSize;
        label.color = this.textColor;
        label.horizontalAlign = Label.HorizontalAlign.CENTER;
        label.verticalAlign = Label.VerticalAlign.CENTER;

        // 应用字体配置
        this._applyFontSettings(label);

        // 设置初始状态（用于动画）
        translationNode.setScale(this.initialScale, this.initialScale, 1);
        opacity.opacity = 0;
        
        // 添加到活跃翻译集合
        this._activeTranslations.add(translationNode);
        
        Logger.debug('WordTranslationDisplay', `创建翻译节点: ${translation}`);
        return translationNode;
    }

    /**
     * 定位翻译节点到目标单词上方
     * @param translationNode 翻译节点
     * @param targetWordNode 目标单词节点
     */
    private _positionTranslationNode(translationNode: Node, targetWordNode: Node): void {
        try {
            // 获取目标单词的世界坐标
            const worldPos = targetWordNode.getWorldPosition();
            
            // 转换为翻译容器的本地坐标
            const containerTransform = this.translationContainer.getComponent(UITransform)!;
            const localPos = containerTransform.convertToNodeSpaceAR(worldPos);
            
            // 设置翻译节点位置（在目标单词上方）
            translationNode.setPosition(
                localPos.x,
                localPos.y + this.offsetY,
                0
            );

            // 添加到翻译容器
            translationNode.setParent(this.translationContainer);

            Logger.debug('WordTranslationDisplay', `翻译节点定位完成: (${localPos.x}, ${localPos.y + this.offsetY})`);
        } catch (error) {
            Logger.error('WordTranslationDisplay', '翻译节点定位失败', error as Error);
        }
    }

    /**
     * 播放翻译动画序列（使用AnimationManager）
     * @param translationNode 翻译节点
     */
    private async _playTranslationAnimation(translationNode: Node): Promise<void> {
        try {
            if (!this._animationManager) {
                Logger.warn('WordTranslationDisplay', '动画管理器不可用，使用简单动画');
                await this._playSimpleAnimation(translationNode);
                return;
            }

            Logger.info('WordTranslationDisplay', '开始播放翻译动画序列');

            // 1. 出现动画（缩放 + 透明度）
            await this._playAppearAnimationWithManager(translationNode);

            // 2. 保持显示
            await this._delay(this.displayDuration * 1000);

            // 3. 消失动画
            await this._playDisappearAnimationWithManager(translationNode);

            Logger.info('WordTranslationDisplay', '翻译动画序列完成');

        } catch (error) {
            Logger.error('WordTranslationDisplay', '翻译动画播放失败', error as Error);
        }
    }

    /**
     * 使用AnimationManager播放出现动画
     * @param node 翻译节点
     */
    private async _playAppearAnimationWithManager(node: Node): Promise<void> {
        return new Promise(resolve => {
            let completedAnimations = 0;
            const totalAnimations = 2; // 缩放 + 透明度

            const onAnimationComplete = () => {
                completedAnimations++;
                if (completedAnimations >= totalAnimations) {
                    resolve();
                }
            };

            // 1. 缩放动画
            const scaleAnimationId = this._animationManager.playScaleAnimation({
                target: node,
                targetScale: this.targetScale,
                config: {
                    duration: this.appearDuration,
                    easing: this._getEasingName(),
                    onComplete: onAnimationComplete
                }
            });

            // 2. 透明度动画
            const opacity = node.getComponent(UIOpacity)!;
            const opacityAnimationId = this._animationManager.playOpacityAnimation({
                target: opacity,
                targetOpacity: 255,
                config: {
                    duration: this.appearDuration,
                    easing: 'sineOut',
                    onComplete: onAnimationComplete
                }
            });

            if (!scaleAnimationId || !opacityAnimationId) {
                Logger.warn('WordTranslationDisplay', '动画管理器播放失败，使用简单动画');
                this._playSimpleAppearAnimation(node).then(resolve);
            }
        });
    }

    /**
     * 播放出现动画（原有方法，作为备用）
     * @param node 翻译节点
     */
    private _playAppearAnimation(node: Node): Promise<void> {
        return new Promise(resolve => {
            const opacity = node.getComponent(UIOpacity)!;

            tween(node)
                .parallel(
                    tween().to(this.appearDuration,
                        { scale: new Vec3(this.targetScale, this.targetScale, 1) },
                        { easing: this._getEasingName() }
                    ),
                    tween(opacity).to(this.appearDuration, { opacity: 255 })
                )
                .call(() => {
                    Logger.debug('WordTranslationDisplay', '出现动画完成');
                    resolve();
                })
                .start();
        });
    }

    /**
     * 使用AnimationManager播放消失动画
     * @param node 翻译节点
     */
    private async _playDisappearAnimationWithManager(node: Node): Promise<void> {
        return new Promise(resolve => {
            const opacity = node.getComponent(UIOpacity)!;

            const opacityAnimationId = this._animationManager.playOpacityAnimation({
                target: opacity,
                targetOpacity: 0,
                config: {
                    duration: this.disappearDuration,
                    easing: 'sineIn',
                    onComplete: () => {
                        Logger.debug('WordTranslationDisplay', '消失动画完成');
                        resolve();
                    }
                }
            });

            if (!opacityAnimationId) {
                Logger.warn('WordTranslationDisplay', '消失动画播放失败，使用简单动画');
                this._playSimpleDisappearAnimation(node).then(resolve);
            }
        });
    }

    /**
     * 播放消失动画（原有方法，作为备用）
     * @param node 翻译节点
     */
    private _playDisappearAnimation(node: Node): Promise<void> {
        return new Promise(resolve => {
            const opacity = node.getComponent(UIOpacity)!;

            tween(opacity)
                .to(this.disappearDuration, { opacity: 0 })
                .call(() => {
                    Logger.debug('WordTranslationDisplay', '消失动画完成');
                    resolve();
                })
                .start();
        });
    }

    /**
     * 简单的出现动画（备用方案）
     * @param node 翻译节点
     */
    private _playSimpleAppearAnimation(node: Node): Promise<void> {
        return this._playAppearAnimation(node);
    }

    /**
     * 简单的消失动画（备用方案）
     * @param node 翻译节点
     */
    private _playSimpleDisappearAnimation(node: Node): Promise<void> {
        return this._playDisappearAnimation(node);
    }

    /**
     * 简单动画序列（备用方案）
     * @param node 翻译节点
     */
    private async _playSimpleAnimation(node: Node): Promise<void> {
        await this._playAppearAnimation(node);
        await this._delay(this.displayDuration * 1000);
        await this._playDisappearAnimation(node);
    }

    /**
     * 移除翻译节点
     * @param translationNode 翻译节点
     */
    private _removeTranslationNode(translationNode: Node): void {
        try {
            this._activeTranslations.delete(translationNode);
            translationNode.destroy();
            Logger.debug('WordTranslationDisplay', '翻译节点已移除');
        } catch (error) {
            Logger.error('WordTranslationDisplay', '移除翻译节点失败', error as Error);
        }
    }

    /**
     * 清理所有翻译节点
     */
    private _clearAllTranslations(): void {
        for (const node of this._activeTranslations) {
            if (node && node.isValid) {
                node.destroy();
            }
        }
        this._activeTranslations.clear();
        Logger.info('WordTranslationDisplay', '所有翻译节点已清理');
    }

    /**
     * 延迟函数
     * @param ms 延迟毫秒数
     */
    private _delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取翻译服务统计信息
     */
    public getTranslationStats(): any {
        if (this._translationService) {
            return this._translationService.getStatistics();
        }
        return null;
    }

    /**
     * 设置翻译显示开关
     * @param enabled 是否启用
     */
    public setTranslationEnabled(enabled: boolean): void {
        this.enableTranslation = enabled;
        Logger.info('WordTranslationDisplay', `翻译显示${enabled ? '启用' : '禁用'}`);
    }


}
