import { _decorator, Component } from 'cc';
import { IWordData } from '../../Data/WordDatabase';
import { LetterGenerationEngine } from '../../Utils/LetterGeneration/LetterGenerationEngine';
import { Logger } from '../../Utils/Logger';
import { GameplayValidator } from '../../Game/Validators/GameplayValidator';

const { ccclass } = _decorator;

/**
 * 字母生成服务 - 专门处理字母生成逻辑
 * 遵循单一职责原则，专注于字母生成功能
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-05
 */
@ccclass('LetterGenerationService')
export class LetterGenerationService extends Component {

    /**
     * 网格行数
     */
    private static readonly GRID_ROWS = 9;

    /**
     * 网格列数
     */
    private static readonly GRID_COLS = 8;

    /**
     * 总字母数量
     */
    private static readonly TOTAL_LETTERS = LetterGenerationService.GRID_ROWS * LetterGenerationService.GRID_COLS;

    /**
     * 组件初始化
     */
    onLoad(): void {
        // 字母生成服务初始化
    }

    /**
     * 生成字母网格
     * @param targetWords 目标单词列表
     * @returns 生成的字母网格 (9行8列)
     */
    public generateLetterGrid(targetWords: IWordData[]): string[][] {
        if (!targetWords || targetWords.length === 0) {
            Logger.warn('LetterGenerationService', '目标单词列表为空，生成空网格');
            return this._createEmptyGrid();
        }

        try {
            // 使用增强的生成算法，包含连接性验证和重试机制
            let letterGrid = this._generateValidatedLetterGrid(targetWords);

            if (letterGrid && this._validateGrid(letterGrid)) {
                return letterGrid;
            } else {
                Logger.warn('LetterGenerationService', '标准生成失败，尝试降级策略');

                // 使用降级策略
                letterGrid = this.generateFallbackLetterGrid(targetWords);

                if (letterGrid && this._validateGrid(letterGrid)) {
                    return letterGrid;
                } else {
                    Logger.error('LetterGenerationService', '降级策略也失败，返回空网格');
                    return this._createEmptyGrid();
                }
            }
        } catch (error) {
            Logger.error('LetterGenerationService', '字母网格生成失败', error as Error);
            return this._createEmptyGrid();
        }
    }

    /**
     * 生成经过连接性验证的字母网格
     * 包含重试机制，确保所有目标单词都可连接
     * @param targetWords 目标单词列表
     * @returns 验证通过的字母网格
     */
    private _generateValidatedLetterGrid(targetWords: IWordData[]): string[][] | null {
        const maxAttempts = 10; // 最大重试次数
        let attempt = 0;

        // 获取可玩性验证器
        const gameplayValidator = GameplayValidator.getInstance();
        if (!gameplayValidator) {
            Logger.error('LetterGenerationService', '无法获取GameplayValidator实例');
            return null;
        }

        while (attempt < maxAttempts) {
            attempt++;

            try {
                // 🔧 优化1：生成字母网格
                const letterGrid = LetterGenerationEngine.generateLettersForGrid(targetWords);

                // 🔧 优化2：多层验证机制
                // 第一层：快速验证字母存在性
                if (!gameplayValidator.quickValidatePlayability(letterGrid, targetWords)) {
                    if (attempt % 5 === 0) {
                        Logger.warn('LetterGenerationService', `前${attempt}次尝试：字母存在性验证失败`);
                    }
                    continue;
                }

                // 第二层：网格质量检查
                if (!this._validateGridQuality(letterGrid, targetWords)) {
                    if (attempt % 5 === 0) {
                        Logger.warn('LetterGenerationService', `前${attempt}次尝试：网格质量检查失败`);
                    }
                    continue;
                }

                // 第三层：完整可玩性验证
                const playabilityResult = gameplayValidator.validateGameplayability(letterGrid, targetWords);

                if (playabilityResult.isPlayable) {
                    // 🔧 最终成功验证：输出详细信息
                    Logger.info('LetterGenerationService', `✅ 生成成功！第 ${attempt} 次尝试`);
                    Logger.info('LetterGenerationService', `网格质量评分: ${playabilityResult.qualityScore || 'N/A'}`);
                    Logger.info('LetterGenerationService', `可连接单词: ${playabilityResult.connectableWords || targetWords.length}/${targetWords.length}`);
                    return letterGrid;
                } else {
                    if (attempt % 10 === 0) {
                        Logger.warn('LetterGenerationService',
                            `前${attempt}次尝试失败，继续重试... (${playabilityResult.summary})`);
                    }
                }
            } catch (error) {
                Logger.error('LetterGenerationService', `第 ${attempt} 次尝试出现异常`, error as Error);
            }
        }

        Logger.error('LetterGenerationService', `经过 ${maxAttempts} 次尝试仍无法生成可玩的字母网格`);

        // 🔧 最后的安全措施：强制生成完整网格
        Logger.warn('LetterGenerationService', '启动最后安全措施：强制生成完整网格');

        try {
            const emergencyGrid = this._generateEmergencyGrid(targetWords);
            if (emergencyGrid) {
                Logger.info('LetterGenerationService', '紧急网格生成成功');
                return emergencyGrid;
            }
        } catch (error) {
            Logger.error('LetterGenerationService', '紧急网格生成失败', error as Error);
        }

        // 输出验证统计信息
        const stats = gameplayValidator.getValidationStats();
        Logger.error('LetterGenerationService', `最终失败 - 验证统计：成功率 ${(stats.successRate * 100).toFixed(1)}%，总验证 ${stats.totalValidations} 次`);

        return null;
    }

    /**
     * 创建空网格
     * @returns 空的字母网格
     */
    private _createEmptyGrid(): string[][] {
        const grid: string[][] = [];
        for (let row = 0; row < LetterGenerationService.GRID_ROWS; row++) {
            grid[row] = [];
            for (let col = 0; col < LetterGenerationService.GRID_COLS; col++) {
                grid[row][col] = '';
            }
        }
        return grid;
    }

    /**
     * 验证生成的网格
     * @param grid 字母网格
     * @returns 是否有效
     */
    private _validateGrid(grid: string[][]): boolean {
        // 检查网格尺寸
        if (!grid || grid.length !== LetterGenerationService.GRID_ROWS) {
            Logger.error('LetterGenerationService', '网格行数不正确');
            return false;
        }

        for (let row = 0; row < grid.length; row++) {
            if (!grid[row] || grid[row].length !== LetterGenerationService.GRID_COLS) {
                Logger.error('LetterGenerationService', `第${row}行列数不正确`);
                return false;
            }

            // 检查每个字母是否有效
            for (let col = 0; col < grid[row].length; col++) {
                const letter = grid[row][col];
                if (!letter || typeof letter !== 'string' || letter.length !== 1) {
                    Logger.error('LetterGenerationService', `位置(${row}, ${col})的字母无效: "${letter}"`);
                    return false;
                }

                // 检查是否为有效的英文字母
                if (!/^[A-Z]$/.test(letter)) {
                    Logger.error('LetterGenerationService', `位置(${row}, ${col})不是有效的大写英文字母: "${letter}"`);
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 验证网格质量
     * @param letterGrid 字母网格
     * @param targetWords 目标单词
     * @returns 是否通过质量检查
     */
    private _validateGridQuality(letterGrid: string[][], targetWords: IWordData[]): boolean {
        // 🔧 质量检查1：字母分布均匀性
        if (!this._checkLetterDistribution(letterGrid)) {
            return false;
        }

        // 🔧 质量检查2：避免过多连续相同字母
        if (!this._checkConsecutiveLetters(letterGrid)) {
            return false;
        }

        // 🔧 质量检查3：确保目标单词字母充足
        if (!this._checkTargetWordLetters(letterGrid, targetWords)) {
            return false;
        }

        return true;
    }

    /**
     * 检查字母分布均匀性
     * @param letterGrid 字母网格
     * @returns 是否分布均匀
     */
    private _checkLetterDistribution(letterGrid: string[][]): boolean {
        const letterCount = new Map<string, number>();
        let totalLetters = 0;

        // 统计字母频率
        for (const row of letterGrid) {
            for (const letter of row) {
                letterCount.set(letter, (letterCount.get(letter) || 0) + 1);
                totalLetters++;
            }
        }

        // 检查是否有字母过度集中（超过20%）
        for (const [letter, count] of letterCount) {
            const ratio = count / totalLetters;
            if (ratio > 0.2) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查连续字母问题
     * @param letterGrid 字母网格
     * @returns 是否通过检查
     */
    private _checkConsecutiveLetters(letterGrid: string[][]): boolean {
        // 检查水平方向连续字母
        for (let row = 0; row < letterGrid.length; row++) {
            for (let col = 0; col < letterGrid[row].length - 2; col++) {
                if (letterGrid[row][col] === letterGrid[row][col + 1] &&
                    letterGrid[row][col + 1] === letterGrid[row][col + 2]) {
                    return false;
                }
            }
        }

        // 检查垂直方向连续字母
        for (let row = 0; row < letterGrid.length - 2; row++) {
            for (let col = 0; col < letterGrid[row].length; col++) {
                if (letterGrid[row][col] === letterGrid[row + 1][col] &&
                    letterGrid[row + 1][col] === letterGrid[row + 2][col]) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 检查目标单词字母充足性
     * @param letterGrid 字母网格
     * @param targetWords 目标单词
     * @returns 是否字母充足
     */
    private _checkTargetWordLetters(letterGrid: string[][], targetWords: IWordData[]): boolean {
        const gridLetterCount = new Map<string, number>();

        // 统计网格字母
        for (const row of letterGrid) {
            for (const letter of row) {
                gridLetterCount.set(letter, (gridLetterCount.get(letter) || 0) + 1);
            }
        }

        // 检查每个目标单词的字母需求
        for (const wordData of targetWords) {
            const wordLetterCount = new Map<string, number>();
            for (const letter of wordData.word.toUpperCase()) {
                wordLetterCount.set(letter, (wordLetterCount.get(letter) || 0) + 1);
            }

            // 验证网格是否包含足够的字母
            for (const [letter, needed] of wordLetterCount) {
                const available = gridLetterCount.get(letter) || 0;
                if (available < needed) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 紧急网格生成 - 最后的安全措施
     * @param targetWords 目标单词
     * @returns 字母网格或null
     */
    private _generateEmergencyGrid(targetWords: IWordData[]): string[][] | null {
        Logger.warn('LetterGenerationService', '执行紧急网格生成');

        // 🔧 创建9x8空网格
        const grid: string[][] = [];
        for (let row = 0; row < 9; row++) {
            grid[row] = new Array(8).fill('');
        }

        // 🔧 使用最简单的策略：水平放置所有单词
        let currentRow = 0;
        let currentCol = 0;

        for (const wordData of targetWords) {
            const word = wordData.word.toUpperCase();

            // 检查当前行是否有足够空间
            if (currentCol + word.length > 8) {
                currentRow++;
                currentCol = 0;
            }

            // 检查是否超出网格
            if (currentRow >= 9) {
                Logger.error('LetterGenerationService', '紧急网格空间不足');
                break;
            }

            // 放置单词
            for (let i = 0; i < word.length; i++) {
                if (currentCol + i < 8) {
                    grid[currentRow][currentCol + i] = word[i];
                }
            }

            currentCol += word.length + 1; // 留一个空格间隔
            Logger.info('LetterGenerationService', `紧急放置: ${word} 在行 ${currentRow}`);
        }

        // 🔧 填充剩余空位
        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 8; col++) {
                if (grid[row][col] === '') {
                    grid[row][col] = letters[Math.floor(Math.random() * letters.length)];
                }
            }
        }

        // 🔧 验证紧急网格
        const emergencyReport = this._verifyEmergencyGrid(grid, targetWords);
        if (emergencyReport.isComplete) {
            Logger.info('LetterGenerationService', '紧急网格验证通过');
            return grid;
        } else {
            Logger.error('LetterGenerationService', `紧急网格验证失败：缺失 ${emergencyReport.missingWords.join(', ')}`);
            return null;
        }
    }

    /**
     * 验证紧急网格的完整性
     * @param grid 字母网格
     * @param targetWords 目标单词
     * @returns 验证报告
     */
    private _verifyEmergencyGrid(grid: string[][], targetWords: IWordData[]): {
        isComplete: boolean;
        missingWords: string[];
        foundWords: string[];
    } {
        const foundWords: string[] = [];
        const missingWords: string[] = [];

        for (const wordData of targetWords) {
            const word = wordData.word.toUpperCase();
            let found = false;

            // 搜索水平方向
            for (let row = 0; row < 9 && !found; row++) {
                for (let col = 0; col <= 8 - word.length && !found; col++) {
                    let matches = true;
                    for (let i = 0; i < word.length; i++) {
                        if (grid[row][col + i] !== word[i]) {
                            matches = false;
                            break;
                        }
                    }
                    if (matches) {
                        found = true;
                        foundWords.push(word);
                    }
                }
            }

            if (!found) {
                missingWords.push(word);
            }
        }

        return {
            isComplete: missingWords.length === 0,
            missingWords,
            foundWords
        };
    }

    /**
     * 获取网格尺寸信息
     * @returns 网格尺寸对象
     */
    public getGridDimensions(): {
        rows: number;
        cols: number;
        totalCells: number;
    } {
        return {
            rows: LetterGenerationService.GRID_ROWS,
            cols: LetterGenerationService.GRID_COLS,
            totalCells: LetterGenerationService.TOTAL_LETTERS
        };
    }

    /**
     * 检查字母生成服务是否就绪
     * @returns 是否就绪
     */
    public isReady(): boolean {
        return this._letterGenerationUtils !== null;
    }

    /**
     * 重新生成字母网格
     * @param targetWords 目标单词列表
     * @returns 重新生成的字母网格
     */
    public regenerateLetterGrid(targetWords: IWordData[]): string[][] {
        Logger.info('LetterGenerationService', '重新生成字母网格');
        return this.generateLetterGrid(targetWords);
    }

    /**
     * 生成降级字母网格
     * 当标准生成失败时使用的备用方案
     * @param targetWords 目标单词列表
     * @returns 降级生成的字母网格
     */
    public generateFallbackLetterGrid(targetWords: IWordData[]): string[][] {
        Logger.warn('LetterGenerationService', '使用降级策略生成字母网格');

        try {
            // 使用简化的生成策略
            const letterGrid = this._generateSimplifiedGrid(targetWords);

            if (this._validateGrid(letterGrid)) {
                Logger.info('LetterGenerationService', '降级策略生成成功');
                return letterGrid;
            } else {
                Logger.error('LetterGenerationService', '降级策略生成失败，返回空网格');
                return this._createEmptyGrid();
            }
        } catch (error) {
            Logger.error('LetterGenerationService', '降级策略异常', error as Error);
            return this._createEmptyGrid();
        }
    }

    /**
     * 生成简化的字母网格
     * 不保证所有单词都可连接，但确保基本结构正确
     * @param targetWords 目标单词列表
     * @returns 简化的字母网格
     */
    private _generateSimplifiedGrid(targetWords: IWordData[]): string[][] {
        const grid = this._createEmptyGrid();

        // 提取所有单词的字母
        const allLetters: string[] = [];
        for (const wordData of targetWords) {
            allLetters.push(...wordData.word.toUpperCase().split(''));
        }

        // 添加随机字母填充到72个
        const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        while (allLetters.length < 72) {
            const randomLetter = alphabet[Math.floor(Math.random() * alphabet.length)];
            allLetters.push(randomLetter);
        }

        // 随机打乱字母
        for (let i = allLetters.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [allLetters[i], allLetters[j]] = [allLetters[j], allLetters[i]];
        }

        // 填充到网格
        let letterIndex = 0;
        for (let row = 0; row < grid.length; row++) {
            for (let col = 0; col < grid[row].length; col++) {
                grid[row][col] = allLetters[letterIndex] || 'A';
                letterIndex++;
            }
        }

        Logger.info('LetterGenerationService', '简化网格生成完成');
        return grid;
    }

    /**
     * 获取服务状态信息
     * @returns 状态信息对象
     */
    public getServiceStatus(): {
        isReady: boolean;
        gridDimensions: {
            rows: number;
            cols: number;
            totalCells: number;
        };
    } {
        return {
            isReady: this.isReady(),
            gridDimensions: this.getGridDimensions()
        };
    }

    /**
     * 组件销毁时清理资源
     */
    onDestroy(): void {
        this._letterGenerationUtils = null;
        Logger.info('LetterGenerationService', '字母生成服务已清理');
    }
}
