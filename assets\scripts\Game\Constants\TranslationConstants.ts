/**
 * 单词翻译功能常量定义
 * 
 * 集中管理翻译功能相关的配置常量，复用发音功能的架构模式。
 * 遵循项目的常量命名规范，使用UPPER_SNAKE_CASE命名。
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-04
 */

/**
 * 翻译API配置常量
 */
export const TRANSLATION_API_CONFIG = {
    /** 有道翻译API基础URL */
    BASE_URL: 'https://fanyi.youdao.com/translate',
    /** 请求超时时间（毫秒） */
    TIMEOUT: 8000,
    /** 重试次数 */
    RETRY_ATTEMPTS: 2,
    /** 重试延迟（毫秒） */
    RETRY_DELAY: 1000,
    /** 翻译类型参数 */
    TYPE: 'AUTO',
    /** 文档类型 */
    DOC_TYPE: 'json'
} as const;

/**
 * 翻译缓存配置常量
 */
export const TRANSLATION_CACHE_CONFIG = {
    /** 最大缓存数量 */
    MAX_SIZE: 200,
    /** 缓存过期时间（小时） */
    EXPIRATION_HOURS: 24,
    /** 本地存储键名 */
    STORAGE_KEY: 'word_translation_cache'
} as const;

/**
 * 翻译性能配置常量
 */
export const TRANSLATION_PERFORMANCE_CONFIG = {
    /** 最大并发请求数 */
    MAX_CONCURRENT_REQUESTS: 2,
    /** 请求队列最大长度 */
    MAX_QUEUE_SIZE: 10
} as const;

/**
 * 翻译动画配置常量
 */
export const TRANSLATION_ANIMATION_CONFIG = {
    /** 出现动画持续时间（秒） */
    APPEAR_DURATION: 0.3,
    /** 显示持续时间（秒） */
    DISPLAY_DURATION: 1.0,
    /** 消失动画持续时间（秒） */
    DISAPPEAR_DURATION: 0.3,
    /** 翻译文本相对单词的Y偏移 */
    OFFSET_Y: 50,
    /** 字体大小 */
    FONT_SIZE: 24
} as const;

/**
 * 翻译错误代码枚举
 */
export enum TranslationErrorCode {
    /** 网络错误 */
    NETWORK_ERROR = 'NETWORK_ERROR',
    /** API响应无效 */
    INVALID_API_RESPONSE = 'INVALID_API_RESPONSE',
    /** 请求超时 */
    REQUEST_TIMEOUT = 'REQUEST_TIMEOUT',
    /** 翻译结果为空 */
    EMPTY_TRANSLATION = 'EMPTY_TRANSLATION',
    /** 参数无效 */
    INVALID_PARAMETER = 'INVALID_PARAMETER'
}
