/**
 * 单词发音功能常量定义
 * 
 * 集中管理发音功能相关的配置常量，包括API配置、缓存策略、性能参数等。
 * 遵循项目的常量命名规范，使用UPPER_SNAKE_CASE命名。
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22
 */

/**
 * 发音API配置常量
 */
export const PRONUNCIATION_API_CONFIG = {
    /** 有道词典语音API基础URL */
    BASE_URL: 'https://dict.youdao.com/dictvoice',
    /** 请求超时时间（毫秒） */
    TIMEOUT: 5000,
    /** 重试次数 */
    RETRY_ATTEMPTS: 3,
    /** 重试延迟（毫秒） */
    RETRY_DELAY: 1000,
    /** API类型参数（0=英式发音，1=美式发音） */
    DEFAULT_TYPE: 0
} as const;

/**
 * 音频缓存配置常量
 */
export const PRONUNCIATION_CACHE_CONFIG = {
    /** 最大缓存数量 */
    MAX_SIZE: 50,
    /** 最大内存使用（MB） */
    MAX_MEMORY_MB: 10,
    /** 缓存过期时间（小时） */
    EXPIRATION_HOURS: 24,
    /** 预加载数量 */
    PRELOAD_COUNT: 10
} as const;

/**
 * 性能配置常量
 */
export const PRONUNCIATION_PERFORMANCE_CONFIG = {
    /** 最大并发请求数 */
    MAX_CONCURRENT_REQUESTS: 3,
    /** 预加载延迟（毫秒） */
    PRELOAD_DELAY: 100,
    /** 缓存清理间隔（毫秒） */
    CLEANUP_INTERVAL: 300000,
    /** 音频播放延迟（毫秒） */
    PLAY_DELAY: 200
} as const;

/**
 * 发音错误码枚举
 */
export enum PronunciationErrorCode {
    /** 网络请求错误 */
    NETWORK_ERROR = 'PRONUNCIATION_NETWORK_ERROR',
    /** 音频播放错误 */
    AUDIO_PLAY_ERROR = 'PRONUNCIATION_AUDIO_PLAY_ERROR',
    /** 缓存操作错误 */
    CACHE_ERROR = 'PRONUNCIATION_CACHE_ERROR',
    /** 平台不支持 */
    PLATFORM_NOT_SUPPORTED = 'PRONUNCIATION_PLATFORM_NOT_SUPPORTED',
    /** 用户权限被拒绝 */
    USER_PERMISSION_DENIED = 'PRONUNCIATION_USER_PERMISSION_DENIED',
    /** API响应无效 */
    INVALID_API_RESPONSE = 'PRONUNCIATION_INVALID_API_RESPONSE',
    /** 单词参数无效 */
    INVALID_WORD_PARAMETER = 'PRONUNCIATION_INVALID_WORD_PARAMETER'
}

/**
 * 平台类型枚举
 */
export enum PlatformType {
    /** 微信小游戏 */
    WECHAT = 'wechat',
    /** 抖音小游戏 */
    DOUYIN = 'douyin',
    /** Web浏览器 */
    WEB = 'web',
    /** 未知平台 */
    UNKNOWN = 'unknown'
}

/**
 * 音频状态枚举
 */
export enum AudioStatus {
    /** 空闲状态 */
    IDLE = 'idle',
    /** 加载中 */
    LOADING = 'loading',
    /** 播放中 */
    PLAYING = 'playing',
    /** 播放完成 */
    COMPLETED = 'completed',
    /** 播放失败 */
    FAILED = 'failed'
}

/**
 * 发音配置接口
 */
export interface PronunciationConfig {
    /** 是否启用发音功能 */
    enabled: boolean;
    /** 发音类型（0=英式，1=美式） */
    type: number;
    /** 音量（0-1） */
    volume: number;
    /** 是否允许使用移动网络 */
    allowMobileNetwork: boolean;
    /** 是否启用缓存 */
    enableCache: boolean;
}

/**
 * 默认发音配置
 */
export const DEFAULT_PRONUNCIATION_CONFIG: PronunciationConfig = {
    enabled: true,
    type: 0,
    volume: 0.8,
    allowMobileNetwork: false,
    enableCache: true
} as const;
