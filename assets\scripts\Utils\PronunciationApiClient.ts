import { Logger } from './Logger';
import { AudioUrlGenerator } from './AudioUrlGenerator';
import { PlatformAudioAdapter, AudioPlayResult } from './PlatformAudioAdapter';
import { 
    PRONUNCIATION_API_CONFIG, 
    PRONUNCIATION_PERFORMANCE_CONFIG,
    PronunciationErrorCode 
} from '../Game/Constants/PronunciationConstants';

/**
 * 发音请求结果接口
 */
export interface PronunciationResult {
    /** 请求是否成功 */
    success: boolean;
    /** 单词 */
    word: string;
    /** 音频URL */
    audioUrl?: string;
    /** 播放结果 */
    playResult?: AudioPlayResult;
    /** 错误码 */
    errorCode?: PronunciationErrorCode;
    /** 错误信息 */
    errorMessage?: string;
    /** 请求耗时（毫秒） */
    duration?: number;
}

/**
 * 发音API客户端
 * 
 * 负责与有道词典语音API的网络通信和音频播放协调。
 * 遵循单一职责原则，专注于API调用和网络错误处理。
 * 
 * 核心功能：
 * - 单词发音请求和播放
 * - 网络错误处理和重试机制
 * - 请求并发控制和队列管理
 * - 平台兼容性处理
 * 
 * 技术特性：
 * - 异步请求处理，不阻塞主线程
 * - 智能重试机制，提高成功率
 * - 并发请求限制，避免资源浪费
 * - 详细的错误分类和日志记录
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22
 */
export class PronunciationApiClient {
    
    /**
     * 当前活跃的请求数量
     */
    private static _activeRequests: number = 0;
    
    /**
     * 请求队列
     */
    private static _requestQueue: Array<() => void> = [];
    
    /**
     * 是否已初始化
     */
    private static _initialized: boolean = false;

    /**
     * 初始化API客户端
     */
    public static initialize(): void {
        try {
            // 🔧 强制重新初始化，即使已经初始化过
            if (PronunciationApiClient._initialized) {
                Logger.info('PronunciationApiClient', 'API客户端已初始化，执行重新初始化');
                PronunciationApiClient._initialized = false;
            }

            // 🔧 清理之前的状态
            PronunciationApiClient._activeRequests = 0;
            PronunciationApiClient._requestQueue = [];

            // 检查平台兼容性
            const platform = PlatformAudioAdapter.detectPlatform();
            const capabilities = PlatformAudioAdapter.getAudioCapabilities();

            if (!capabilities.supportsNetworkAudio) {
                Logger.error('PronunciationApiClient', '当前平台不支持网络音频播放');
                return;
            }

            PronunciationApiClient._initialized = true;
            Logger.success('PronunciationApiClient', `发音API客户端初始化完成 (平台: ${platform})`);

        } catch (error) {
            PronunciationApiClient._initialized = false;
            Logger.error('PronunciationApiClient', 'API客户端初始化失败', error as Error);
        }
    }

    /**
     * 播放单词发音
     * @param word 要发音的单词
     * @param type 发音类型（0=英式，1=美式），默认为英式
     * @param volume 音量控制 (0-1)，可选参数
     * @returns 发音结果Promise
     */
    public static async playWordPronunciation(word: string, type: number = 0, volume: number = 1.0): Promise<PronunciationResult> {
        const startTime = Date.now();
        
        try {
            // 检查初始化状态
            if (!PronunciationApiClient._initialized) {
                PronunciationApiClient.initialize();
                if (!PronunciationApiClient._initialized) {
                    return PronunciationApiClient._createErrorResult(
                        word, 
                        PronunciationErrorCode.PLATFORM_NOT_SUPPORTED,
                        '平台不支持或初始化失败'
                    );
                }
            }

            // 参数验证
            const validationResult = PronunciationApiClient._validateParameters(word, type);
            if (!validationResult.valid) {
                return PronunciationApiClient._createErrorResult(
                    word,
                    PronunciationErrorCode.INVALID_WORD_PARAMETER,
                    validationResult.error!
                );
            }

            // 并发控制
            if (PronunciationApiClient._activeRequests >= PRONUNCIATION_PERFORMANCE_CONFIG.MAX_CONCURRENT_REQUESTS) {
                Logger.warn('PronunciationApiClient', `并发请求数达到限制(${PRONUNCIATION_PERFORMANCE_CONFIG.MAX_CONCURRENT_REQUESTS})，加入队列等待`);
                await PronunciationApiClient._waitForSlot();
            }

            // 增加活跃请求计数
            PronunciationApiClient._activeRequests++;

            try {
                // 生成音频URL
                const audioUrl = AudioUrlGenerator.generatePronunciationUrl(word, type);
                Logger.info('PronunciationApiClient', `单词 "${word}" 生成的音频URL: ${audioUrl}`);

                if (!audioUrl) {
                    Logger.error('PronunciationApiClient', `单词 "${word}" URL生成失败`);
                    return PronunciationApiClient._createErrorResult(
                        word,
                        PronunciationErrorCode.INVALID_API_RESPONSE,
                        'URL生成失败'
                    );
                }

                // 请求用户交互权限（如果需要）
                const capabilities = PlatformAudioAdapter.getAudioCapabilities();
                if (capabilities.requiresUserInteraction) {
                    const permissionGranted = await PlatformAudioAdapter.requestUserInteraction();
                    if (!permissionGranted) {
                        return PronunciationApiClient._createErrorResult(
                            word,
                            PronunciationErrorCode.USER_PERMISSION_DENIED,
                            '用户权限被拒绝'
                        );
                    }
                }

                // 播放音频（带重试机制和音量控制）
                const playResult = await PronunciationApiClient._playWithRetry(audioUrl, word, volume);
                
                const duration = Date.now() - startTime;
                
                if (playResult.success) {
                    Logger.success('PronunciationApiClient', `单词 "${word}" 发音播放成功，耗时: ${duration}ms`);
                    return {
                        success: true,
                        word,
                        audioUrl,
                        playResult,
                        duration
                    };
                } else {
                    Logger.error('PronunciationApiClient', `单词 "${word}" 音频播放失败: ${playResult.error}, URL: ${audioUrl}`);
                    return PronunciationApiClient._createErrorResult(
                        word,
                        PronunciationErrorCode.AUDIO_PLAY_ERROR,
                        playResult.error || '音频播放失败',
                        audioUrl,
                        duration
                    );
                }

            } finally {
                // 减少活跃请求计数
                PronunciationApiClient._activeRequests--;
                // 处理队列中的下一个请求
                PronunciationApiClient._processQueue();
            }

        } catch (error) {
            const duration = Date.now() - startTime;
            Logger.error('PronunciationApiClient', `单词 "${word}" 发音请求异常`, error as Error);
            return PronunciationApiClient._createErrorResult(
                word,
                PronunciationErrorCode.NETWORK_ERROR,
                (error as Error).message,
                undefined,
                duration
            );
        }
    }

    /**
     * 批量预加载单词发音
     * @param words 单词数组
     * @param type 发音类型
     * @returns 预加载结果Promise
     */
    public static async preloadWordPronunciations(words: string[], type: number = 0): Promise<Map<string, boolean>> {
        const results = new Map<string, boolean>();
        
        if (!Array.isArray(words) || words.length === 0) {
            Logger.warn('PronunciationApiClient', '预加载单词列表为空');
            return results;
        }

        Logger.info('PronunciationApiClient', `开始预加载${words.length}个单词的发音`);

        // 限制并发数量，避免过多请求
        const batchSize = Math.min(words.length, PRONUNCIATION_PERFORMANCE_CONFIG.MAX_CONCURRENT_REQUESTS);
        
        for (let i = 0; i < words.length; i += batchSize) {
            const batch = words.slice(i, i + batchSize);
            const batchPromises = batch.map(async (word) => {
                try {
                    const result = await PronunciationApiClient.playWordPronunciation(word, type);
                    results.set(word, result.success);
                    
                    // 添加延迟，避免请求过于频繁
                    if (i + batchSize < words.length) {
                        await PronunciationApiClient._delay(PRONUNCIATION_PERFORMANCE_CONFIG.PRELOAD_DELAY);
                    }
                } catch (error) {
                    Logger.error('PronunciationApiClient', `预加载单词 "${word}" 失败`, error as Error);
                    results.set(word, false);
                }
            });

            await Promise.all(batchPromises);
        }

        const successCount = Array.from(results.values()).filter(success => success).length;
        Logger.info('PronunciationApiClient', `预加载完成: ${successCount}/${words.length} 成功`);

        return results;
    }

    /**
     * 验证参数有效性
     */
    private static _validateParameters(word: string, type: number): { valid: boolean; error?: string } {
        if (!word || typeof word !== 'string' || word.trim().length === 0) {
            return { valid: false, error: '单词参数无效' };
        }

        if (typeof type !== 'number' || (type !== 0 && type !== 1)) {
            return { valid: false, error: '发音类型参数无效' };
        }

        return { valid: true };
    }

    /**
     * 带重试机制的音频播放
     */
    private static async _playWithRetry(audioUrl: string, word: string, volume: number = 1.0): Promise<AudioPlayResult> {
        let lastError: string = '';
        
        for (let attempt = 1; attempt <= PRONUNCIATION_API_CONFIG.RETRY_ATTEMPTS; attempt++) {
            try {
                // 尝试播放音频（带音量控制）
                const result = await PlatformAudioAdapter.playNetworkAudio(audioUrl, volume);
                
                if (result.success) {
                    if (attempt > 1) {
                        Logger.info('PronunciationApiClient', `单词 "${word}" 在第${attempt}次尝试后播放成功`);
                    }
                    return result;
                }
                
                lastError = result.error || '未知错误';
                
                // 如果不是最后一次尝试，等待后重试
                if (attempt < PRONUNCIATION_API_CONFIG.RETRY_ATTEMPTS) {
                    Logger.warn('PronunciationApiClient', `单词 "${word}" 第${attempt}次播放失败: ${lastError}，${PRONUNCIATION_API_CONFIG.RETRY_DELAY}ms后重试`);
                    await PronunciationApiClient._delay(PRONUNCIATION_API_CONFIG.RETRY_DELAY);
                }
                
            } catch (error) {
                lastError = (error as Error).message;
                Logger.error('PronunciationApiClient', `单词 "${word}" 第${attempt}次播放异常`, error as Error);
                
                if (attempt < PRONUNCIATION_API_CONFIG.RETRY_ATTEMPTS) {
                    await PronunciationApiClient._delay(PRONUNCIATION_API_CONFIG.RETRY_DELAY);
                }
            }
        }
        
        Logger.error('PronunciationApiClient', `单词 "${word}" 在${PRONUNCIATION_API_CONFIG.RETRY_ATTEMPTS}次尝试后仍然失败: ${lastError}`);
        return { success: false, error: lastError };
    }

    /**
     * 等待请求槽位
     */
    private static async _waitForSlot(): Promise<void> {
        return new Promise((resolve) => {
            PronunciationApiClient._requestQueue.push(resolve);
        });
    }

    /**
     * 处理请求队列
     */
    private static _processQueue(): void {
        if (PronunciationApiClient._requestQueue.length > 0 && 
            PronunciationApiClient._activeRequests < PRONUNCIATION_PERFORMANCE_CONFIG.MAX_CONCURRENT_REQUESTS) {
            const nextRequest = PronunciationApiClient._requestQueue.shift();
            if (nextRequest) {
                nextRequest();
            }
        }
    }

    /**
     * 延迟工具方法
     */
    private static _delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 创建错误结果
     */
    private static _createErrorResult(
        word: string, 
        errorCode: PronunciationErrorCode, 
        errorMessage: string,
        audioUrl?: string,
        duration?: number
    ): PronunciationResult {
        return {
            success: false,
            word,
            audioUrl,
            errorCode,
            errorMessage,
            duration
        };
    }

    /**
     * 获取客户端状态信息
     */
    public static getStatus(): {
        initialized: boolean;
        activeRequests: number;
        queueLength: number;
    } {
        return {
            initialized: PronunciationApiClient._initialized,
            activeRequests: PronunciationApiClient._activeRequests,
            queueLength: PronunciationApiClient._requestQueue.length
        };
    }

    /**
     * 重置客户端状态（用于测试）
     */
    public static reset(): void {
        PronunciationApiClient._activeRequests = 0;
        PronunciationApiClient._requestQueue = [];
        PronunciationApiClient._initialized = false;
        Logger.info('PronunciationApiClient', 'API客户端状态已重置');
    }
}
