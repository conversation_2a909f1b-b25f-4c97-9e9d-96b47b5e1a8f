import { _decorator } from 'cc';
import { IWordData } from '../../Data/WordDatabase';
import { Logger } from '../Logger';
import { GameUtils } from '../GameUtils';

const { ccclass } = _decorator;

/**
 * 网格验证器
 * 负责网格验证和单词查找功能
 * 专门处理验证逻辑，遵循单一职责原则
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-09
 */
@ccclass('GridValidator')
export class GridValidator {

    /**
     * 验证字母集合是否能够在交叉单词游戏中拼出所有指定的单词
     * 在交叉单词游戏中，字母可以被多个单词共享
     * @param letters 字母数组
     * @param words 单词数据数组
     * @returns 验证结果
     */
    public static validateLettersCanFormWords(letters: string[], words: IWordData[]): boolean {
        if (GameUtils.isArrayEmpty(letters) || GameUtils.isArrayEmpty(words)) {
            Logger.error('GridValidator', '字母或单词列表为空！');
            return false;
        }

        Logger.info('GridValidator', `开始验证字母集合，字母数量: ${letters.length}, 单词数量: ${words.length}`);

        // 统计可用字母的数量
        const availableLetterCount = new Map<string, number>();
        for (const letter of letters) {
            const upperLetter = letter.toUpperCase();
            const currentCount = availableLetterCount.get(upperLetter) || 0;
            availableLetterCount.set(upperLetter, currentCount + 1);
        }

        // 检查是否包含所有必需的字母
        const requiredLetters = new Set<string>();
        for (const wordData of words) {
            const word = wordData.word.toUpperCase();
            for (const letter of word) {
                requiredLetters.add(letter);
            }
        }

        // 验证是否包含所有必需的字母
        for (const requiredLetter of requiredLetters) {
            if (!availableLetterCount.has(requiredLetter)) {
                Logger.error('GridValidator', `缺少必需字母: ${requiredLetter}`);
                return false;
            }
        }

        // 进一步验证：检查每个单词是否都能在当前字母集合中找到
        for (const wordData of words) {
            const word = wordData.word.toUpperCase();
            // 检查单词中的每个字母是否都存在
            for (const letter of word) {
                if (!availableLetterCount.has(letter)) {
                    Logger.error('GridValidator', `单词 "${word}" 无法拼出，缺少字母 "${letter}"`);
                    return false;
                }
            }
        }

        Logger.success('GridValidator', '字母集合验证通过');
        return true;
    }

    /**
     * 验证网格中的所有单词
     * @param grid 字母网格
     * @param words 单词数据数组
     * @returns 验证结果
     */
    public static verifyAllWordsInGrid(grid: string[][], words: IWordData[]): boolean {
        Logger.info('GridValidator', `开始验证网格中的${words.length}个单词`);
        
        let allFound = true;
        for (const wordData of words) {
            const word = wordData.word.toUpperCase();
            const found = this.findWordInGrid(grid, word);

            if (found.length === 0) {
                Logger.error('GridValidator', `无法找到单词 "${word}"`);
                allFound = false;
            }
        }

        if (allFound) {
            Logger.info('GridValidator', '所有单词验证通过');
        }

        return allFound;
    }

    /**
     * 在网格中查找单词的所有路径
     * @param grid 网格
     * @param word 单词
     * @returns 找到的路径数组
     */
    public static findWordInGrid(grid: string[][], word: string): Array<{
        startRow: number,
        startCol: number,
        direction: string,
        positions: Array<{row: number, col: number}>
    }> {
        const directions = [
            { name: '水平向右', dr: 0, dc: 1 },
            { name: '水平向左', dr: 0, dc: -1 },
            { name: '垂直向下', dr: 1, dc: 0 },
            { name: '垂直向上', dr: -1, dc: 0 },
            { name: '对角线右下', dr: 1, dc: 1 },
            { name: '对角线左上', dr: -1, dc: -1 },
            { name: '对角线右上', dr: -1, dc: 1 },
            { name: '对角线左下', dr: 1, dc: -1 }
        ];

        const foundPaths: Array<{
            startRow: number,
            startCol: number,
            direction: string,
            positions: Array<{row: number, col: number}>
        }> = [];

        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 8; col++) {
                for (const direction of directions) {
                    const result = this.checkWordPath(grid, word, row, col, direction);
                    if (result.found) {
                        foundPaths.push({
                            startRow: row,
                            startCol: col,
                            direction: direction.name,
                            positions: result.positions
                        });
                    }
                }
            }
        }

        return foundPaths;
    }

    /**
     * 检查从指定位置开始是否能找到单词
     * @param grid 网格
     * @param word 单词
     * @param startRow 起始行
     * @param startCol 起始列
     * @param direction 方向
     * @returns 检查结果
     */
    public static checkWordPath(grid: string[][], word: string, startRow: number, startCol: number, direction: {dr: number, dc: number}): {
        found: boolean,
        positions: Array<{row: number, col: number}>
    } {
        const positions: Array<{row: number, col: number}> = [];

        for (let i = 0; i < word.length; i++) {
            const row = startRow + i * direction.dr;
            const col = startCol + i * direction.dc;

            // 检查边界
            if (row < 0 || row >= 9 || col < 0 || col >= 8) {
                return { found: false, positions: [] };
            }

            // 检查字母是否匹配
            if (grid[row][col] !== word[i]) {
                return { found: false, positions: [] };
            }

            positions.push({ row, col });
        }

        return { found: true, positions };
    }

    /**
     * 验证网格结构的完整性
     * @param grid 网格
     * @returns 验证结果
     */
    public static validateGridStructure(grid: string[][]): boolean {
        if (!grid || grid.length !== 9) {
            Logger.error('GridValidator', '网格行数不正确，应为9行');
            return false;
        }

        for (let row = 0; row < 9; row++) {
            if (!grid[row] || grid[row].length !== 8) {
                Logger.error('GridValidator', `第${row}行列数不正确，应为8列`);
                return false;
            }

            for (let col = 0; col < 8; col++) {
                const letter = grid[row][col];
                if (typeof letter !== 'string' || letter.length !== 1) {
                    Logger.error('GridValidator', `位置[${row},${col}]的字母格式不正确: ${letter}`);
                    return false;
                }
            }
        }

        Logger.success('GridValidator', '网格结构验证通过');
        return true;
    }

    /**
     * 检查字母可用性
     * @param availableLetters 可用字母数组
     * @param requiredLetters 需要的字母数组
     * @returns 检查结果
     */
    public static checkLetterAvailability(availableLetters: string[], requiredLetters: string[]): {
        available: boolean,
        missingLetters: string[]
    } {
        const availableCount = new Map<string, number>();
        const requiredCount = new Map<string, number>();
        const missingLetters: string[] = [];

        // 统计可用字母
        for (const letter of availableLetters) {
            const upperLetter = letter.toUpperCase();
            availableCount.set(upperLetter, (availableCount.get(upperLetter) || 0) + 1);
        }

        // 统计需要的字母
        for (const letter of requiredLetters) {
            const upperLetter = letter.toUpperCase();
            requiredCount.set(upperLetter, (requiredCount.get(upperLetter) || 0) + 1);
        }

        // 检查是否有足够的字母
        for (const [letter, needed] of requiredCount) {
            const available = availableCount.get(letter) || 0;
            if (available < needed) {
                for (let i = 0; i < needed - available; i++) {
                    missingLetters.push(letter);
                }
            }
        }

        return {
            available: missingLetters.length === 0,
            missingLetters
        };
    }

    /**
     * 验证单词形成能力
     * @param letters 字母数组
     * @param words 单词数组
     * @returns 验证结果详情
     */
    public static verifyWordFormation(letters: string[], words: IWordData[]): {
        canFormAll: boolean,
        formableWords: string[],
        unformableWords: string[],
        missingLetters: Map<string, string[]>
    } {
        const formableWords: string[] = [];
        const unformableWords: string[] = [];
        const missingLetters = new Map<string, string[]>();

        for (const wordData of words) {
            const word = wordData.word.toUpperCase();
            const wordLetters = word.split('');
            const result = this.checkLetterAvailability(letters, wordLetters);

            if (result.available) {
                formableWords.push(word);
            } else {
                unformableWords.push(word);
                missingLetters.set(word, result.missingLetters);
            }
        }

        return {
            canFormAll: unformableWords.length === 0,
            formableWords,
            unformableWords,
            missingLetters
        };
    }

    /**
     * 执行质量检查
     * @param grid 网格
     * @param words 单词数组
     * @returns 质量检查结果
     */
    public static performQualityCheck(grid: string[][], words: IWordData[]): {
        structureValid: boolean,
        allWordsFound: boolean,
        wordPaths: Map<string, number>,
        overallQuality: number
    } {
        Logger.info('GridValidator', '开始执行质量检查');

        // 结构验证
        const structureValid = this.validateGridStructure(grid);

        // 单词验证
        const allWordsFound = this.verifyAllWordsInGrid(grid, words);

        // 统计每个单词的路径数量
        const wordPaths = new Map<string, number>();
        for (const wordData of words) {
            const word = wordData.word.toUpperCase();
            const paths = this.findWordInGrid(grid, word);
            wordPaths.set(word, paths.length);
        }

        // 计算整体质量分数
        let qualityScore = 0;
        if (structureValid) qualityScore += 30;
        if (allWordsFound) qualityScore += 50;

        // 根据路径多样性加分
        const totalPaths = Array.from(wordPaths.values()).reduce((sum, count) => sum + count, 0);
        const averagePaths = totalPaths / words.length;
        if (averagePaths >= 2) qualityScore += 20;
        else if (averagePaths >= 1) qualityScore += 10;

        Logger.info('GridValidator', `质量检查完成，总分: ${qualityScore}/100`);

        return {
            structureValid,
            allWordsFound,
            wordPaths,
            overallQuality: qualityScore
        };
    }
}
