/**
 * 日志系统相关枚举定义
 * 
 * 为了避免在多个文件中重复定义相同数值的Enum，
 * 将所有日志相关的枚举统一定义在此文件中，
 * 并为不同类型的枚举分配不同的数值范围。
 */

/**
 * 日志级别枚举
 * 数值范围: 0-9
 */
export enum LogLevel {
    ERROR = 0,    // 错误级别 - 只显示错误
    WARN = 1,     // 警告级别 - 显示警告和错误
    INFO = 2,     // 信息级别 - 显示信息、警告和错误
    DEBUG = 3     // 调试级别 - 显示所有日志
}

/**
 * 日志预设模式枚举
 * 数值范围: 10-19
 */
export enum LogPresetMode {
    PRODUCTION = 10,    // 生产模式 - 只显示错误和警告
    TESTING = 11,       // 测试模式 - 显示信息级别日志
    DEVELOPMENT = 12,   // 开发模式 - 显示所有日志
    DEBUG_ONLY = 13     // 纯调试模式 - 显示调试级别日志
}

/**
 * 核心组件枚举
 * 数值范围: 20-39
 */
export enum CoreComponent {
    GAME_INITIALIZER = 20,
    SCENE_MANAGER = 21,
    AUDIO_MANAGER = 22,
    WORD_PRONUNCIATION_MANAGER = 23,
    GAME_DATA_MANAGER = 24,
    AUDIO_INTEGRATION_SERVICE = 25
}

/**
 * 游戏逻辑组件枚举
 * 数值范围: 40-59
 */
export enum GameLogicComponent {
    WORD_CONNECTION_CONTROLLER = 40,
    LETTER_GRID_CONTROLLER = 41,
    WORD_AREA_CONTROLLER = 42,
    HINT_CONTROLLER = 43,
    LEVEL_MANAGER = 44,
    SCORE_MANAGER = 45
}

/**
 * 服务组件枚举
 * 数值范围: 60-79
 */
export enum ServiceComponent {
    WORD_TRANSLATION_SERVICE = 60,
    WORD_PRONUNCIATION_SERVICE = 61,
    TRANSLATION_API_CLIENT = 62,
    PRONUNCIATION_API_CLIENT = 63
}

/**
 * 工具组件枚举
 * 数值范围: 80-99
 */
export enum UtilityComponent {
    TRANSLATION_CACHE = 80,
    WORD_PLACEMENT_ENGINE = 81,
    GRID_SPATIAL_ANALYZER = 82,
    ANIMATION_MANAGER = 83,
    COLOR_THEME_MANAGER = 84,
    PLATFORM_AUDIO_ADAPTER = 85
}

/**
 * UI组件枚举
 * 数值范围: 100-119
 */
export enum UIComponent {
    MAIN_MENU_CONTROLLER = 100,
    GAME_UI_CONTROLLER = 101,
    LETTER_EVENT_HANDLER = 102,
    WORD_TRANSLATION_DISPLAY = 103,
    CONNECTION_VISUALIZER = 104
}

/**
 * 组件名称映射
 * 将枚举值映射到实际的组件名称字符串
 */
export const COMPONENT_NAME_MAP = new Map<number, string>([
    // 核心组件
    [CoreComponent.GAME_INITIALIZER, 'GameInitializer'],
    [CoreComponent.SCENE_MANAGER, 'SceneManager'],
    [CoreComponent.AUDIO_MANAGER, 'AudioManager'],
    [CoreComponent.WORD_PRONUNCIATION_MANAGER, 'WordPronunciationManager'],
    [CoreComponent.GAME_DATA_MANAGER, 'GameDataManager'],
    [CoreComponent.AUDIO_INTEGRATION_SERVICE, 'AudioIntegrationService'],
    
    // 游戏逻辑组件
    [GameLogicComponent.WORD_CONNECTION_CONTROLLER, 'WordConnectionController'],
    [GameLogicComponent.LETTER_GRID_CONTROLLER, 'LetterGridController'],
    [GameLogicComponent.WORD_AREA_CONTROLLER, 'WordAreaController'],
    [GameLogicComponent.HINT_CONTROLLER, 'HintController'],
    [GameLogicComponent.LEVEL_MANAGER, 'LevelManager'],
    [GameLogicComponent.SCORE_MANAGER, 'ScoreManager'],
    
    // 服务组件
    [ServiceComponent.WORD_TRANSLATION_SERVICE, 'WordTranslationService'],
    [ServiceComponent.WORD_PRONUNCIATION_SERVICE, 'WordPronunciationService'],
    [ServiceComponent.TRANSLATION_API_CLIENT, 'TranslationApiClient'],
    [ServiceComponent.PRONUNCIATION_API_CLIENT, 'PronunciationApiClient'],
    
    // 工具组件
    [UtilityComponent.TRANSLATION_CACHE, 'TranslationCache'],
    [UtilityComponent.WORD_PLACEMENT_ENGINE, 'WordPlacementEngine'],
    [UtilityComponent.GRID_SPATIAL_ANALYZER, 'GridSpatialAnalyzer'],
    [UtilityComponent.ANIMATION_MANAGER, 'AnimationManager'],
    [UtilityComponent.COLOR_THEME_MANAGER, 'ColorThemeManager'],
    [UtilityComponent.PLATFORM_AUDIO_ADAPTER, 'PlatformAudioAdapter'],
    
    // UI组件
    [UIComponent.MAIN_MENU_CONTROLLER, 'MainMenuController'],
    [UIComponent.GAME_UI_CONTROLLER, 'GameUIController'],
    [UIComponent.LETTER_EVENT_HANDLER, 'LetterEventHandler'],
    [UIComponent.WORD_TRANSLATION_DISPLAY, 'WordTranslationDisplay'],
    [UIComponent.CONNECTION_VISUALIZER, 'ConnectionVisualizer']
]);

/**
 * 获取组件名称
 * @param componentEnum 组件枚举值
 * @returns 组件名称字符串
 */
export function getComponentName(componentEnum: number): string {
    return COMPONENT_NAME_MAP.get(componentEnum) || 'Unknown';
}

/**
 * 获取所有核心组件的枚举值
 * @returns 核心组件枚举值数组
 */
export function getCoreComponents(): number[] {
    return [
        CoreComponent.GAME_INITIALIZER,
        CoreComponent.SCENE_MANAGER,
        CoreComponent.AUDIO_MANAGER,
        CoreComponent.WORD_PRONUNCIATION_MANAGER,
        CoreComponent.GAME_DATA_MANAGER,
        CoreComponent.AUDIO_INTEGRATION_SERVICE
    ];
}

/**
 * 获取所有高频组件的枚举值（通常需要静默）
 * @returns 高频组件枚举值数组
 */
export function getHighFrequencyComponents(): number[] {
    return [
        UtilityComponent.TRANSLATION_CACHE,
        UtilityComponent.WORD_PLACEMENT_ENGINE,
        UtilityComponent.GRID_SPATIAL_ANALYZER,
        ServiceComponent.TRANSLATION_API_CLIENT,
        UtilityComponent.PLATFORM_AUDIO_ADAPTER
    ];
}
