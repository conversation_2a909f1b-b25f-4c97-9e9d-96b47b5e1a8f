import { _decorator, Component, director, Node } from 'cc';
import { SceneManager } from './SceneManager';
import { GameDataManager } from './GameDataManager';
import { AudioManager } from './AudioManager';
import { AudioIntegrationService } from './AudioIntegrationService';
import { Logger } from '../Utils/Logger';

const { ccclass } = _decorator;

/**
 * 核心系统管理器
 * 
 * 统一管理所有核心系统组件的初始化和生命周期，确保系统在场景切换时的连续性。
 * 这个组件必须放在场景根节点下，并且会自动设置为持久化节点。
 * 
 * 核心功能：
 * - 统一初始化所有核心管理器组件
 * - 管理持久化节点的生命周期
 * - 提供全局访问接口
 * - 处理场景切换时的状态保持
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22
 */
@ccclass('CoreSystemManager')
export class CoreSystemManager extends Component {

    /**
     * 单例实例
     */
    private static _instance: CoreSystemManager | null = null;

    /**
     * 核心组件引用
     */
    private _sceneManager: SceneManager | null = null;
    private _gameDataManager: GameDataManager | null = null;
    private _audioManager: AudioManager | null = null;
    private _audioIntegrationService: AudioIntegrationService | null = null;

    /**
     * 获取单例实例
     */
    public static getInstance(): CoreSystemManager | null {
        return CoreSystemManager._instance;
    }

    /**
     * 组件生命周期 - 初始化
     */
    onLoad() {
        // 检查是否已有实例
        if (CoreSystemManager._instance !== null) {
            Logger.warn('CoreSystemManager', '检测到重复的CoreSystemManager实例，销毁当前节点');
            this.node.destroy();
            return;
        }

        // 检查节点是否在根节点下
        if (this.node.parent !== null) {
            Logger.error('CoreSystemManager', 'CoreSystemManager必须放在场景根节点下才能设置为持久化节点');
            return;
        }

        // 设置单例实例
        CoreSystemManager._instance = this;

        // 设置为持久化节点
        director.addPersistRootNode(this.node);
        Logger.info('CoreSystemManager', '核心系统管理器已设置为持久化节点');

        // 初始化所有核心系统
        this._initializeCoreSystems();
    }

    /**
     * 组件销毁时清理单例引用
     */
    onDestroy() {
        if (CoreSystemManager._instance === this) {
            CoreSystemManager._instance = null;
            Logger.info('CoreSystemManager', '核心系统管理器已销毁');
        }
    }

    /**
     * 初始化所有核心系统
     */
    private _initializeCoreSystems(): void {
        Logger.info('CoreSystemManager', '开始初始化核心系统');

        // 初始化音频系统（优先级最高）
        this._initializeAudioSystem();

        // 初始化场景管理器
        this._initializeSceneManager();

        // 初始化游戏数据管理器
        this._initializeGameDataManager();

        Logger.info('CoreSystemManager', '核心系统初始化完成');
    }

    /**
     * 初始化音频系统
     */
    private _initializeAudioSystem(): void {
        Logger.info('CoreSystemManager', '初始化音频系统');

        // 初始化音频管理器
        this._audioManager = this.node.getComponent(AudioManager);
        if (!this._audioManager) {
            this._audioManager = this.node.addComponent(AudioManager);
            Logger.info('CoreSystemManager', 'AudioManager组件已添加');
        } else {
            Logger.info('CoreSystemManager', 'AudioManager组件已存在');
        }

        // 初始化音效集成服务
        this._audioIntegrationService = this.node.getComponent(AudioIntegrationService);
        if (!this._audioIntegrationService) {
            this._audioIntegrationService = this.node.addComponent(AudioIntegrationService);
            Logger.info('CoreSystemManager', 'AudioIntegrationService组件已添加');
        } else {
            Logger.info('CoreSystemManager', 'AudioIntegrationService组件已存在');
        }
    }

    /**
     * 初始化场景管理器
     */
    private _initializeSceneManager(): void {
        Logger.info('CoreSystemManager', '初始化场景管理器');

        this._sceneManager = this.node.getComponent(SceneManager);
        if (!this._sceneManager) {
            this._sceneManager = this.node.addComponent(SceneManager);
            Logger.info('CoreSystemManager', 'SceneManager组件已添加');
        } else {
            Logger.info('CoreSystemManager', 'SceneManager组件已存在');
        }
    }

    /**
     * 初始化游戏数据管理器
     */
    private _initializeGameDataManager(): void {
        Logger.info('CoreSystemManager', '初始化游戏数据管理器');

        this._gameDataManager = this.node.getComponent(GameDataManager);
        if (!this._gameDataManager) {
            this._gameDataManager = this.node.addComponent(GameDataManager);
            Logger.info('CoreSystemManager', 'GameDataManager组件已添加');
        } else {
            Logger.info('CoreSystemManager', 'GameDataManager组件已存在');
        }
    }

    /**
     * 获取场景管理器实例
     */
    public getSceneManager(): SceneManager | null {
        return this._sceneManager;
    }

    /**
     * 获取游戏数据管理器实例
     */
    public getGameDataManager(): GameDataManager | null {
        return this._gameDataManager;
    }

    /**
     * 获取音频管理器实例
     */
    public getAudioManager(): AudioManager | null {
        return this._audioManager;
    }

    /**
     * 获取音效集成服务实例
     */
    public getAudioIntegrationService(): AudioIntegrationService | null {
        return this._audioIntegrationService;
    }

    /**
     * 检查所有核心系统是否已初始化
     */
    public areAllSystemsInitialized(): boolean {
        return !!(this._sceneManager && 
                 this._gameDataManager && 
                 this._audioManager && 
                 this._audioIntegrationService);
    }

    /**
     * 获取系统状态报告
     */
    public getSystemStatus(): {
        sceneManager: boolean;
        gameDataManager: boolean;
        audioManager: boolean;
        audioIntegrationService: boolean;
    } {
        return {
            sceneManager: !!this._sceneManager,
            gameDataManager: !!this._gameDataManager,
            audioManager: !!this._audioManager,
            audioIntegrationService: !!this._audioIntegrationService
        };
    }
}
