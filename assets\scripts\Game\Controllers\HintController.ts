import { _decorator, Component, Node, Button, Color } from 'cc';
import { Logger } from '../../Utils/Logger';
import { LetterGridController } from './LetterGridController';
import { WordAreaController } from './WordAreaController';
import { ColorThemeManager } from '../../Game/Managers/ColorThemeManager';
import { ConnectionVisualizer } from '../../Game/Components/ConnectionVisualizer';
import { AudioIntegrationService } from '../../Core/AudioIntegrationService';
import { LetterPosition } from '../../Game/Constants/GameConstants';

const { ccclass, property } = _decorator;

/**
 * 提示控制器 - 负责游戏提示功能的管理
 * 遵循单一职责原则，专门处理提示相关的逻辑
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15
 */
@ccclass('HintController')
export class HintController extends Component {

    /**
     * 提示按钮节点
     */
    @property(Node)
    hintButton: Node = null!;



    /**
     * 字母网格控制器引用
     */
    @property(LetterGridController)
    letterGridController: LetterGridController = null!;

    /**
     * 单词区域控制器引用
     */
    @property(WordAreaController)
    wordAreaController: WordAreaController = null!;

    /**
     * 连线可视化器引用
     */
    @property(ConnectionVisualizer)
    connectionVisualizer: ConnectionVisualizer = null!;

    /**
     * 颜色主题管理器
     */
    private _colorThemeManager: ColorThemeManager = null!;

    /**
     * 当前提示路径（用于清除高亮）
     */
    private _currentHintPath: LetterPosition[] = [];

    /**
     * 提示按钮组件
     */
    private _hintButtonComponent: Button = null!;

    /**
     * 当前提示状态
     */
    private _isHintActive: boolean = false;

    /**
     * 音效集成服务
     */
    private _audioService: AudioIntegrationService | null = null;

    /**
     * 当前活跃的提示定时器
     */
    private _hintTimer: any = null;

    /**
     * 当前提示路径
     */
    private _currentHintPath: LetterPosition[] = [];

    /**
     * 组件初始化
     */
    onLoad(): void {
        Logger.info('HintController', '提示控制器初始化');
        this._initializeComponents();
        this._setupEventListeners();
    }

    /**
     * 初始化组件引用
     */
    private _initializeComponents(): void {
        // 初始化颜色主题管理器
        this._colorThemeManager = ColorThemeManager.getInstance();

        // 获取音效集成服务
        this._audioService = AudioIntegrationService.getInstance();
        if (!this._audioService) {
            Logger.warn('HintController', '音效集成服务未初始化，音效功能将不可用');
        }

        // 获取提示按钮组件
        if (this.hintButton) {
            this._hintButtonComponent = this.hintButton.getComponent(Button);
            if (!this._hintButtonComponent) {
                Logger.warn('HintController', '提示按钮缺少Button组件');
            }
        }

        // 验证必要的控制器引用
        this._validateControllerReferences();

        Logger.info('HintController', '组件初始化完成');
    }

    /**
     * 验证控制器引用
     */
    private _validateControllerReferences(): void {
        const missingControllers: string[] = [];

        if (!this.letterGridController) {
            missingControllers.push('LetterGridController');
        }
        if (!this.wordAreaController) {
            missingControllers.push('WordAreaController');
        }
        if (!this.connectionVisualizer) {
            missingControllers.push('ConnectionVisualizer');
        }

        if (missingControllers.length > 0) {
            Logger.error('HintController', `缺少必要的控制器引用: ${missingControllers.join(', ')}`);
        }
    }

    /**
     * 设置事件监听器
     */
    private _setupEventListeners(): void {
        // 绑定提示按钮点击事件
        if (this._hintButtonComponent) {
            this._hintButtonComponent.node.on(Button.EventType.CLICK, this._onHintButtonClicked, this);
            Logger.debug('HintController', '提示按钮事件绑定完成');
        }
    }

    /**
     * 提示按钮点击事件处理
     */
    private _onHintButtonClicked(): void {
        Logger.info('HintController', '提示按钮被点击');

        // 播放按钮点击音效
        if (this._audioService) {
            this._audioService.playButtonClickSound();
        }

        if (this._isHintActive) {
            // 如果提示正在显示，则隐藏提示
            this.hideHint();
        } else {
            // 显示提示
            this.showHint();
        }
    }

    /**
     * 显示提示
     */
    public showHint(): void {
        if (this._isHintActive) {
            Logger.warn('HintController', '提示已经在显示中');
            return;
        }



        // 获取可用的提示
        const hintPath = this._findAvailableHint();
        if (!hintPath || hintPath.length === 0) {
            Logger.warn('HintController', '没有可用的提示');
            this._showNoHintAvailable();
            return;
        }

        // 设置提示状态
        this._isHintActive = true;
        this._currentHintPath = [...hintPath];

        // 显示提示高亮
        this._showHintHighlight(hintPath);

        // 播放提示音效
        if (this._audioService) {
            this._audioService.playHintShowSound();
        }

        // 清除之前的定时器
        if (this._hintTimer) {
            this._hintTimer.cancel();
        }

        // 设置0.5秒后自动清除提示
        this._hintTimer = this.scheduleOnce(() => {
            this.hideHint();
            this._hintTimer = null;
        }, 0.5);
    }

    /**
     * 隐藏提示
     */
    public hideHint(): void {
        if (!this._isHintActive) {
            return;
        }

        Logger.info('HintController', '隐藏提示');

        // 清除提示高亮
        this._clearHintHighlight();

        // 重置提示状态
        this._isHintActive = false;

        Logger.debug('HintController', '提示隐藏完成');
    }

    /**
     * 查找可用的提示
     * @returns 提示路径或null
     */
    private _findAvailableHint(): LetterPosition[] | null {
        if (!this.wordAreaController || !this.letterGridController) {
            Logger.error('HintController', '控制器引用缺失，无法查找提示');
            return null;
        }

        try {
            // 获取未完成的单词列表
            const uncompletedWords = this.wordAreaController.getUncompletedWords();
            if (uncompletedWords.length === 0) {
                Logger.info('HintController', '所有单词已完成，无需提示');
                return null;
            }

            // 随机选择一个未完成的单词作为提示
            const randomIndex = Math.floor(Math.random() * uncompletedWords.length);
            const targetWord = uncompletedWords[randomIndex];
            // 在字母网格中查找该单词的路径
            const wordPath = this._findWordPath(targetWord);
            return wordPath;

        } catch (error) {
            Logger.error('HintController', '查找提示时发生错误', error as Error);
            return null;
        }
    }

    /**
     * 在字母网格中查找单词路径
     * @param word 目标单词
     * @returns 单词路径或null
     */
    private _findWordPath(word: string): LetterPosition[] | null {
        if (!this.letterGridController || !word) {
            Logger.error('HintController', '字母网格控制器未初始化或单词为空');
            return null;
        }

        const upperWord = word.toUpperCase();

        // 8个搜索方向：水平、垂直、对角线
        const directions = [
            { name: '水平向右', dr: 0, dc: 1 },
            { name: '水平向左', dr: 0, dc: -1 },
            { name: '垂直向下', dr: 1, dc: 0 },
            { name: '垂直向上', dr: -1, dc: 0 },
            { name: '对角线右下', dr: 1, dc: 1 },
            { name: '对角线左上', dr: -1, dc: -1 },
            { name: '对角线右上', dr: -1, dc: 1 },
            { name: '对角线左下', dr: 1, dc: -1 }
        ];

        // 获取网格尺寸
        const gridSize = this.letterGridController.getGridSize();
        const rows = gridSize.rows;
        const cols = gridSize.cols;

        // 遍历网格中的每个位置作为起始点
        for (let startRow = 0; startRow < rows; startRow++) {
            for (let startCol = 0; startCol < cols; startCol++) {
                // 检查起始字母是否匹配
                const startLetter = this.letterGridController.getLetterAt(startRow, startCol);
                if (!startLetter || startLetter.toUpperCase() !== upperWord.charAt(0)) {
                    continue;
                }

                // 尝试每个方向
                for (const direction of directions) {
                    const path = this._searchWordInDirection(upperWord, startRow, startCol, direction);
                    if (path) {
                        return path;
                    }
                }
            }
        }

        Logger.warn('HintController', `未找到单词 "${upperWord}" 的路径`);
        return null;
    }

    /**
     * 在指定方向搜索单词
     * @param word 目标单词
     * @param startRow 起始行
     * @param startCol 起始列
     * @param direction 搜索方向
     * @returns 单词路径或null
     */
    private _searchWordInDirection(
        word: string,
        startRow: number,
        startCol: number,
        direction: {dr: number, dc: number}
    ): LetterPosition[] | null {
        const path: LetterPosition[] = [];
        const gridSize = this.letterGridController!.getGridSize();

        for (let i = 0; i < word.length; i++) {
            const currentRow = startRow + i * direction.dr;
            const currentCol = startCol + i * direction.dc;

            // 检查边界
            if (currentRow < 0 || currentRow >= gridSize.rows ||
                currentCol < 0 || currentCol >= gridSize.cols) {
                return null;
            }

            // 检查字母是否匹配
            const currentLetter = this.letterGridController!.getLetterAt(currentRow, currentCol);
            if (!currentLetter || currentLetter.toUpperCase() !== word.charAt(i)) {
                return null;
            }

            // 添加到路径
            path.push({ row: currentRow, col: currentCol });
        }

        return path;
    }

    /**
     * 显示提示高亮
     * @param path 提示路径
     */
    private _showHintHighlight(path: LetterPosition[]): void {
        if (!this.connectionVisualizer || !this._colorThemeManager) {
            Logger.error('HintController', '连线可视化器或颜色管理器未初始化，无法显示提示高亮');
            return;
        }

        // 获取提示高亮颜色
        const hintColor = this._colorThemeManager.getHintHighlightColor();

        // 使用连线可视化器的智能高亮系统
        this.connectionVisualizer.showHintHighlight(path, hintColor);

        // 保存当前高亮路径，用于后续清除
        this._currentHintPath = [...path];

        Logger.info('HintController', `已高亮提示路径，共${path.length}个字母`);
    }

    /**
     * 清除提示高亮
     */
    private _clearHintHighlight(): void {
        if (!this.connectionVisualizer || this._currentHintPath.length === 0) {
            return;
        }

        // 使用连线可视化器的智能清除系统
        this.connectionVisualizer.clearHintHighlight();

        // 清空当前路径
        this._currentHintPath = [];

        Logger.debug('HintController', '已清除提示高亮');
    }



    /**
     * 显示无提示可用的消息
     */
    private _showNoHintAvailable(): void {
        Logger.info('HintController', '显示无提示可用消息');
        // 暂时不显示UI反馈，保持简洁
    }

    /**
     * 组件销毁时清理
     */
    onDestroy(): void {
        this.hideHint();

        // 清理活跃的提示定时器
        if (this._hintTimer) {
            this._hintTimer.cancel();
            this._hintTimer = null;
        }

        // 清理所有定时器
        this.unscheduleAllCallbacks();

        // 移除事件监听器
        if (this._hintButtonComponent && this._hintButtonComponent.node && this._hintButtonComponent.node.isValid) {
            this._hintButtonComponent.node.off(Button.EventType.CLICK, this._onHintButtonClicked, this);
        }

        Logger.debug('HintController', '提示控制器已销毁');
    }
}
