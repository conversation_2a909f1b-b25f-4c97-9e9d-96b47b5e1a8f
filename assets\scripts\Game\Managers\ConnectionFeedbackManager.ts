import { _decorator, Component } from 'cc';
import { Logger } from '../../Utils/Logger';
import { WordCompletionAnimator } from '../../Game/Components/WordCompletionAnimator';
import { IConnectionAnimationHandler, LetterPosition } from '../../Game/Constants/GameConstants';

const { ccclass, property } = _decorator;

/**
 * 连接反馈管理器 - 负责协调单词完成动画
 * 遵循单一职责原则，专注于单词完成动画的管理
 * 连接动画已移除，使用Sprite高亮系统替代
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-20
 */
@ccclass('ConnectionFeedbackManager')
export class ConnectionFeedbackManager extends Component implements IConnectionAnimationHandler {

    /**
     * 单词完成动画器
     */
    @property(WordCompletionAnimator)
    public wordCompletionAnimator: WordCompletionAnimator | null = null;

    /**
     * 是否启用动画
     */
    @property
    public enableAnimations: boolean = true;

    /**
     * 是否正在播放反馈动画
     */
    private _isPlayingFeedback: boolean = false;

    /**
     * 组件初始化
     */
    onLoad(): void {
        this._validateComponents();
    }

    /**
     * 验证组件配置
     */
    private _validateComponents(): void {
        if (!this.wordCompletionAnimator) {
            Logger.warn('ConnectionFeedbackManager', 'WordCompletionAnimator未配置，单词完成动画将被禁用');
        }
    }

    // ==================== IConnectionAnimationHandler 接口实现 ====================

    /**
     * 开始连接动画（已移除，保留接口兼容性）
     * @param startPos 起始位置
     * @param startWorldPos 起始世界坐标
     */
    public startConnection(startPos: LetterPosition, startWorldPos: any): void {
        // 连接动画已移除，使用Sprite高亮系统替代
        Logger.debug('ConnectionFeedbackManager', '连接开始 - 使用Sprite高亮系统');
    }

    /**
     * 扩展连接动画（已移除，保留接口兼容性）
     * @param newPos 新位置
     * @param newWorldPos 新世界坐标
     */
    public extendConnection(newPos: LetterPosition, newWorldPos: any): void {
        // 连接动画已移除，使用Sprite高亮系统替代
        Logger.debug('ConnectionFeedbackManager', '连接扩展 - 使用Sprite高亮系统');
    }

    /**
     * 结束连接动画（已移除，保留接口兼容性）
     */
    public endConnection(): void {
        // 连接动画已移除，使用Sprite高亮系统替代
        Logger.debug('ConnectionFeedbackManager', '连接结束 - 使用Sprite高亮系统');
    }

    /**
     * 播放成功反馈动画
     * @param onComplete 完成回调
     */
    public playSuccessFeedback(onComplete?: () => void): void {
        if (!this.enableAnimations) {
            Logger.debug('ConnectionFeedbackManager', '动画已禁用，跳过成功反馈');
            if (onComplete) {
                onComplete();
            }
            return;
        }

        if (this._isPlayingFeedback) {
            Logger.warn('ConnectionFeedbackManager', '正在播放反馈动画，跳过新的成功反馈');
            if (onComplete) {
                onComplete();
            }
            return;
        }

        this._isPlayingFeedback = true;
        Logger.info('ConnectionFeedbackManager', '播放成功反馈动画');

        // 播放单词完成动画
        if (this.wordCompletionAnimator) {
            this.wordCompletionAnimator.playWordCompletionAnimation(() => {
                this._isPlayingFeedback = false;
                Logger.debug('ConnectionFeedbackManager', '成功反馈动画完成');
                if (onComplete) {
                    onComplete();
                }
            });
        } else {
            // 如果没有动画器，直接完成
            this._isPlayingFeedback = false;
            Logger.warn('ConnectionFeedbackManager', '单词完成动画器未配置，跳过动画');
            if (onComplete) {
                onComplete();
            }
        }
    }

    /**
     * 播放失败反馈动画
     * @param onComplete 完成回调
     */
    public playFailureFeedback(onComplete?: () => void): void {
        if (!this.enableAnimations) {
            Logger.debug('ConnectionFeedbackManager', '动画已禁用，跳过失败反馈');
            if (onComplete) {
                onComplete();
            }
            return;
        }

        if (this._isPlayingFeedback) {
            Logger.warn('ConnectionFeedbackManager', '正在播放反馈动画，跳过新的失败反馈');
            if (onComplete) {
                onComplete();
            }
            return;
        }

        this._isPlayingFeedback = true;
        Logger.info('ConnectionFeedbackManager', '播放失败反馈动画');

        // 简单的失败反馈（可以扩展为更复杂的动画）
        this.scheduleOnce(() => {
            this._isPlayingFeedback = false;
            Logger.debug('ConnectionFeedbackManager', '失败反馈动画完成');
            if (onComplete) {
                onComplete();
            }
        }, 0.3); // 300ms的简单延迟
    }

    // ==================== 公共方法 ====================

    /**
     * 检查是否正在播放反馈动画
     * @returns 是否正在播放
     */
    public isPlayingFeedback(): boolean {
        return this._isPlayingFeedback;
    }

    /**
     * 停止所有反馈动画
     */
    public stopAllFeedback(): void {
        if (this._isPlayingFeedback) {
            Logger.info('ConnectionFeedbackManager', '停止所有反馈动画');
            
            // 停止单词完成动画
            if (this.wordCompletionAnimator) {
                this.wordCompletionAnimator.stopAllAnimations();
            }

            // 取消所有计划的回调
            this.unscheduleAllCallbacks();

            this._isPlayingFeedback = false;
        }
    }

    /**
     * 设置动画启用状态
     * @param enabled 是否启用
     */
    public setAnimationsEnabled(enabled: boolean): void {
        this.enableAnimations = enabled;
        Logger.info('ConnectionFeedbackManager', `动画${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 获取反馈管理器状态
     * @returns 状态信息
     */
    public getStatus(): {
        isPlayingFeedback: boolean;
        animationsEnabled: boolean;
        hasWordCompletionAnimator: boolean;
    } {
        return {
            isPlayingFeedback: this._isPlayingFeedback,
            animationsEnabled: this.enableAnimations,
            hasWordCompletionAnimator: this.wordCompletionAnimator !== null
        };
    }

    // ==================== 生命周期方法 ====================

    /**
     * 组件销毁时清理资源
     */
    onDestroy(): void {
        try {
            // 停止所有动画
            this.stopAllFeedback();
            
            // 清理引用
            this.wordCompletionAnimator = null;
            
            Logger.info('ConnectionFeedbackManager', '连接反馈管理器资源清理完成');
        } catch (error) {
            Logger.error('ConnectionFeedbackManager', '清理连接反馈管理器时发生错误', error as Error);
        }
    }
}
