import { Node, Label, UITransform, Vec3, Color } from 'cc';
import { AnimationManager } from '../Animation/AnimationManager';
import { ColorThemeManager } from '../Managers/ColorThemeManager';
import { Logger } from '../../Utils/Logger';

/**
 * 翻译动画控制器
 * 
 * 负责管理翻译文本的显示动画效果和UI表现。
 * 遵循单一职责原则，专门处理翻译相关的动画控制和视觉效果。
 * 
 * 核心功能：
 * - 翻译节点创建和管理
 * - 翻译显示动画序列控制
 * - 与AnimationManager和ColorThemeManager集成
 * - 翻译文本的视觉效果处理
 * - 动画生命周期管理
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-03
 */
export class TranslationAnimationController {

    /**
     * 动画管理器引用
     */
    private _animationManager: AnimationManager | null = null;

    /**
     * 颜色主题管理器引用
     */
    private _colorThemeManager: ColorThemeManager | null = null;

    /**
     * 活跃的翻译显示节点
     */
    private _activeTranslations: Map<string, Node> = new Map();

    /**
     * 翻译显示持续时间（毫秒）
     */
    private static readonly TRANSLATION_DISPLAY_DURATION = 1000;

    /**
     * 翻译文本字体大小
     */
    private static readonly TRANSLATION_FONT_SIZE = 24;

    /**
     * 翻译文本相对位置偏移
     */
    private static readonly TRANSLATION_OFFSET_Y = 60;

    /**
     * 构造函数
     */
    constructor() {
        this._initializeReferences();
    }

    /**
     * 初始化外部引用
     */
    private _initializeReferences(): void {
        this._animationManager = AnimationManager.getInstance();
        this._colorThemeManager = ColorThemeManager.getInstance();

        if (!this._animationManager) {
            Logger.warn('TranslationAnimationController', 'AnimationManager未初始化');
        }

        if (!this._colorThemeManager) {
            Logger.warn('TranslationAnimationController', 'ColorThemeManager未初始化');
        }
    }

    /**
     * 显示翻译动画
     * @param word 原单词
     * @param translation 翻译文本
     * @param wordNode 单词节点
     */
    public showTranslationAnimation(word: string, translation: string, wordNode: Node): void {
        if (!word || !translation || !wordNode) {
            Logger.warn('TranslationAnimationController', '显示翻译动画参数无效');
            return;
        }

        // 清理可能存在的旧翻译显示
        this._clearExistingTranslation(word);

        // 创建翻译显示节点
        const translationNode = this._createTranslationNode(translation, wordNode);
        if (!translationNode) {
            Logger.error('TranslationAnimationController', '创建翻译节点失败');
            return;
        }

        // 记录活跃的翻译
        this._activeTranslations.set(word, translationNode);

        // 播放翻译动画序列
        this._playTranslationAnimationSequence(translationNode, word);

        Logger.debug('TranslationAnimationController', `翻译动画开始: ${word} -> ${translation}`);
    }

    /**
     * 创建翻译显示节点
     * @param translation 翻译文本
     * @param wordNode 参考的单词节点
     * @returns 翻译节点或null
     */
    private _createTranslationNode(translation: string, wordNode: Node): Node | null {
        try {
            // 创建翻译节点
            const translationNode = new Node('Translation');
            
            // 添加Label组件
            const label = translationNode.addComponent(Label);
            label.string = translation;
            label.fontSize = TranslationAnimationController.TRANSLATION_FONT_SIZE;
            
            // 设置颜色（使用主题色彩）
            if (this._colorThemeManager) {
                label.color = this._colorThemeManager.getTranslationTextColor();
            } else {
                label.color = new Color(80, 80, 80, 255); // 默认深灰色
            }

            // 添加UITransform组件
            const uiTransform = translationNode.addComponent(UITransform);
            uiTransform.setContentSize(200, 40);

            // 设置父节点和位置
            translationNode.setParent(wordNode.parent);
            
            // 计算翻译文本位置（在单词上方）
            const wordPosition = wordNode.getPosition();
            const translationPosition = new Vec3(
                wordPosition.x,
                wordPosition.y + TranslationAnimationController.TRANSLATION_OFFSET_Y,
                wordPosition.z
            );
            translationNode.setPosition(translationPosition);

            // 初始设置为透明
            translationNode.opacity = 0;

            return translationNode;
        } catch (error) {
            Logger.error('TranslationAnimationController', '创建翻译节点失败', error as Error);
            return null;
        }
    }

    /**
     * 播放翻译动画序列
     * @param translationNode 翻译节点
     * @param word 单词（用于清理）
     */
    private _playTranslationAnimationSequence(translationNode: Node, word: string): void {
        if (!this._animationManager) {
            Logger.warn('TranslationAnimationController', 'AnimationManager不可用，跳过动画');
            // 直接显示翻译文本
            translationNode.opacity = 255;
            this._scheduleTranslationCleanup(translationNode, word);
            return;
        }

        // 阶段1：淡入动画
        this._animationManager.playOpacityAnimation({
            target: translationNode,
            targetOpacity: 255,
            config: {
                duration: 0.3,
                easing: 'easeOut',
                onComplete: () => {
                    // 阶段2：保持显示
                    this._scheduleTranslationCleanup(translationNode, word);
                }
            }
        });
    }

    /**
     * 安排翻译清理
     * @param translationNode 翻译节点
     * @param word 单词
     */
    private _scheduleTranslationCleanup(translationNode: Node, word: string): void {
        // 延迟清理翻译显示
        setTimeout(() => {
            this._hideTranslationWithAnimation(translationNode, word);
        }, TranslationAnimationController.TRANSLATION_DISPLAY_DURATION);
    }

    /**
     * 带动画隐藏翻译
     * @param translationNode 翻译节点
     * @param word 单词
     */
    private _hideTranslationWithAnimation(translationNode: Node, word: string): void {
        if (!translationNode || !translationNode.isValid) {
            this._activeTranslations.delete(word);
            return;
        }

        if (!this._animationManager) {
            // 直接清理
            this._cleanupTranslationNode(translationNode, word);
            return;
        }

        // 淡出动画
        this._animationManager.playOpacityAnimation({
            target: translationNode,
            targetOpacity: 0,
            config: {
                duration: 0.3,
                easing: 'easeIn',
                onComplete: () => {
                    this._cleanupTranslationNode(translationNode, word);
                }
            }
        });
    }

    /**
     * 清理翻译节点
     * @param translationNode 翻译节点
     * @param word 单词
     */
    private _cleanupTranslationNode(translationNode: Node, word: string): void {
        try {
            if (translationNode && translationNode.isValid) {
                translationNode.destroy();
            }
            this._activeTranslations.delete(word);
            Logger.debug('TranslationAnimationController', `翻译节点已清理: ${word}`);
        } catch (error) {
            Logger.warn('TranslationAnimationController', '清理翻译节点失败', error as Error);
        }
    }

    /**
     * 清理已存在的翻译显示
     * @param word 单词
     */
    private _clearExistingTranslation(word: string): void {
        const existingNode = this._activeTranslations.get(word);
        if (existingNode && existingNode.isValid) {
            this._cleanupTranslationNode(existingNode, word);
        }
    }

    /**
     * 清理所有活跃的翻译显示
     */
    public clearAllTranslations(): void {
        for (const [word, node] of this._activeTranslations) {
            this._cleanupTranslationNode(node, word);
        }
        this._activeTranslations.clear();
        Logger.info('TranslationAnimationController', '所有翻译显示已清理');
    }

    /**
     * 获取活跃翻译数量
     */
    public getActiveTranslationCount(): number {
        return this._activeTranslations.size;
    }

    /**
     * 检查是否有活跃的翻译显示
     * @param word 单词
     */
    public hasActiveTranslation(word: string): boolean {
        const node = this._activeTranslations.get(word);
        return node !== undefined && node.isValid;
    }

    /**
     * 更新外部引用（用于热重载等场景）
     */
    public updateReferences(): void {
        this._initializeReferences();
        Logger.debug('TranslationAnimationController', '外部引用已更新');
    }

    /**
     * 销毁控制器
     */
    public destroy(): void {
        this.clearAllTranslations();
        this._animationManager = null;
        this._colorThemeManager = null;
        Logger.info('TranslationAnimationController', '翻译动画控制器已销毁');
    }
}
