import { Logger } from './Logger';
import { TRANSLATION_API_CONFIG } from '../Game/Constants/TranslationConstants';

/**
 * 翻译URL生成器
 * 
 * 负责生成有道翻译API的请求URL，复用AudioUrlGenerator的架构模式。
 * 提供灵活的参数配置和URL构建功能。
 * 
 * 核心功能：
 * - 生成标准的有道翻译API URL
 * - 参数验证和错误处理
 * - URL编码和格式化
 * 
 * API格式：https://fanyi.youdao.com/translate?&doctype=json&type=AUTO&i=单词
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-04
 */
export class TranslationUrlGenerator {
    
    /**
     * 验证单词参数（现在主要用于参数验证）
     * @param word 要翻译的单词
     * @returns 是否有效
     */
    public static validateWord(word: string): boolean {
        try {
            return TranslationUrlGenerator._validateWord(word);
        } catch (error) {
            Logger.error('TranslationUrlGenerator', `单词验证失败: ${word}`, error as Error);
            return false;
        }
    }

    /**
     * 清理单词（供外部使用）
     * @param word 要清理的单词
     * @returns 清理后的单词
     */
    public static cleanWord(word: string): string {
        try {
            return TranslationUrlGenerator._cleanWord(word);
        } catch (error) {
            Logger.error('TranslationUrlGenerator', `单词清理失败: ${word}`, error as Error);
            return word;
        }
    }

    /**
     * 批量生成多个单词的翻译URL
     * @param words 单词数组
     * @returns URL映射表，键为单词，值为对应的URL
     */
    public static generateBatchUrls(words: string[]): Map<string, string> {
        const urlMap = new Map<string, string>();

        if (!Array.isArray(words) || words.length === 0) {
            Logger.warn('TranslationUrlGenerator', '单词数组为空或无效');
            return urlMap;
        }

        let successCount = 0;
        let failureCount = 0;

        for (const word of words) {
            const url = TranslationUrlGenerator.generateTranslationUrl(word);
            if (url) {
                urlMap.set(word, url);
                successCount++;
            } else {
                failureCount++;
            }
        }

        Logger.info('TranslationUrlGenerator', `批量生成翻译URL完成: 成功${successCount}个, 失败${failureCount}个`);
        return urlMap;
    }

    /**
     * 验证单词参数
     * @param word 单词
     * @returns 是否有效
     */
    private static _validateWord(word: string): boolean {
        return word && typeof word === 'string' && word.trim().length > 0 && word.trim().length <= 50;
    }

    /**
     * 清理和格式化单词
     * @param word 原始单词
     * @returns 清理后的单词
     */
    private static _cleanWord(word: string): string {
        // 去除首尾空白字符
        let cleanWord = word.trim();

        // 转换为小写
        cleanWord = cleanWord.toLowerCase();

        // 移除多余的空格和特殊字符
        cleanWord = cleanWord.replace(/\s+/g, '');

        return cleanWord;
    }

    /**
     * 构建完整的API URL
     * @param word 清理后的单词
     * @returns 完整的URL
     */
    private static _buildUrl(word: string): string {
        const baseUrl = TRANSLATION_API_CONFIG.BASE_URL;
        
        // URL编码单词参数
        const encodedWord = encodeURIComponent(word);
        
        // 构建查询参数
        const params = new URLSearchParams({
            doctype: 'json',
            type: TRANSLATION_API_CONFIG.TYPE,
            i: encodedWord
        });

        // 组合完整URL
        const fullUrl = `${baseUrl}?${params.toString()}`;
        
        return fullUrl;
    }
}
