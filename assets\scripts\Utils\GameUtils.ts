import { EventTouch, UITransform, Vec3, Vec2, Node } from 'cc';
import { LetterPosition } from '../Game/Constants/GameConstants';
import { Logger } from './Logger';

/**
 * 游戏工具类 - 统一管理游戏中的通用工具方法
 * 
 * 整合了原 ArrayUtils.ts 和 CoordinateUtils.ts 的内容
 * 遵循单一职责原则，专注于游戏相关的通用功能实现
 * 
 * 核心功能：
 * - 集合操作工具：数组洗牌、验证、安全操作
 * - 空间计算工具：坐标转换、位置计算、边界检测
 * - 游戏逻辑工具：位置验证、距离计算、相邻检测
 * 
 * 算法特性：
 * - Fisher-Yates洗牌算法：确保每种排列的概率完全相等
 * - 类型安全：使用TypeScript泛型确保类型安全
 * - 性能优化：高效的算法实现，适合大数组操作
 * - 错误处理：完善的边界条件检查和错误处理
 * 
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-20
 */
export class GameUtils {

    // ==================== 集合操作工具 ====================

    /**
     * Fisher-Yates洗牌算法 - 随机打乱数组元素
     * 使用现代版本的Fisher-Yates算法，确保每种排列的概率相等
     * @param array 要打乱的数组
     * @returns 打乱后的新数组（不修改原数组）
     */
    public static shuffleArray<T>(array: T[]): T[] {
        if (!array || array.length <= 1) {
            Logger.warn('GameUtils', '数组为空或只有一个元素，无需打乱');
            return [...array];
        }

        // 创建数组副本，避免修改原数组
        const shuffled = [...array];
        
        // Fisher-Yates洗牌算法
        for (let i = shuffled.length - 1; i > 0; i--) {
            // 生成0到i之间的随机索引
            const randomIndex = Math.floor(Math.random() * (i + 1));
            
            // 交换元素
            [shuffled[i], shuffled[randomIndex]] = [shuffled[randomIndex], shuffled[i]];
        }

        // 数组打乱完成
        return shuffled;
    }

    /**
     * 验证数组是否为空或未定义
     * @param array 要验证的数组
     * @returns 是否为空
     */
    public static isArrayEmpty<T>(array: T[]): boolean {
        return !array || array.length === 0;
    }

    /**
     * 安全获取数组元素
     * @param array 数组
     * @param index 索引
     * @param defaultValue 默认值
     * @returns 数组元素或默认值
     */
    public static safeGetArrayElement<T>(array: T[], index: number, defaultValue: T): T {
        if (!array || index < 0 || index >= array.length) {
            Logger.warn('GameUtils', `索引 ${index} 超出数组范围，返回默认值`);
            return defaultValue;
        }
        return array[index];
    }

    /**
     * 验证两个数组长度是否匹配
     * @param array1 第一个数组
     * @param array2 第二个数组
     * @returns 长度是否匹配
     */
    public static arraysLengthMatch<T, U>(array1: T[], array2: U[]): boolean {
        if (!array1 || !array2) {
            Logger.error('GameUtils', '数组不能为空');
            return false;
        }

        const match = array1.length === array2.length;
        if (!match) {
            Logger.warn('GameUtils', `数组长度不匹配: ${array1.length} vs ${array2.length}`);
        }

        return match;
    }

    /**
     * 从数组中随机选择一个元素
     * @param array 数组
     * @returns 随机元素或null
     */
    public static getRandomElement<T>(array: T[]): T | null {
        if (this.isArrayEmpty(array)) {
            return null;
        }
        const randomIndex = Math.floor(Math.random() * array.length);
        return array[randomIndex];
    }

    /**
     * 从数组中随机选择多个不重复的元素
     * @param array 数组
     * @param count 选择数量
     * @returns 随机元素数组
     */
    public static getRandomElements<T>(array: T[], count: number): T[] {
        if (this.isArrayEmpty(array) || count <= 0) {
            return [];
        }

        const shuffled = this.shuffleArray(array);
        return shuffled.slice(0, Math.min(count, array.length));
    }

    // ==================== 空间计算工具 ====================

    /**
     * 将触摸事件转换为字母位置（主要方法）
     * @param event 触摸事件
     * @param letterGridController 字母网格控制器
     * @returns 字母位置或null
     */
    public static convertTouchToLetterPosition(event: EventTouch, letterGridController: any): LetterPosition | null {
        if (!letterGridController) {
            Logger.warn('GameUtils', 'letterGridController未初始化');
            return null;
        }

        // 获取触摸在UI坐标系中的位置
        const uiLocation = event.getUILocation();
        if (!uiLocation) {
            Logger.warn('GameUtils', '触摸位置获取失败');
            return null;
        }

        // 获取字母网格节点
        const gridNode = letterGridController.node;
        if (!gridNode) {
            Logger.warn('GameUtils', '字母网格节点不存在');
            return null;
        }

        // 将UI坐标转换为网格节点的本地坐标
        const localPos = gridNode.getComponent(UITransform)?.convertToNodeSpaceAR(new Vec3(uiLocation.x, uiLocation.y, 0));
        if (!localPos) {
            Logger.warn('GameUtils', '坐标转换失败');
            return null;
        }

        // 通过LetterGridController获取字母位置
        return letterGridController.getLetterPositionFromLocalCoord(localPos.x, localPos.y);
    }

    /**
     * 通过直接命中测试查找字母位置（备用方法）
     * @param event 触摸事件
     * @param letterGridController 字母网格控制器
     * @returns 字母位置或null
     */
    public static getLetterPositionByHitTest(event: EventTouch, letterGridController: any): LetterPosition | null {
        if (!letterGridController) {
            Logger.warn('GameUtils', 'letterGridController未设置');
            return null;
        }

        try {
            const touchLocation = event.getUILocation();
            if (!touchLocation) {
                Logger.warn('GameUtils', '触摸位置获取失败');
                return null;
            }

            // 直接遍历所有单元格，找到包含触摸点的单元格
            return this.findLetterPositionByDirectHitTest(touchLocation, letterGridController);
        } catch (error) {
            Logger.warn('GameUtils', '字母位置检测失败', error as Error);
            return null;
        }
    }

    /**
     * 通过直接命中测试查找字母位置
     * @param touchLocation 触摸位置
     * @param letterGridController 字母网格控制器
     * @returns 字母位置或null
     */
    public static findLetterPositionByDirectHitTest(touchLocation: any, letterGridController: any): LetterPosition | null {
        // 获取字母网格容器节点，用于坐标转换
        const gridContainer = letterGridController.node;
        if (!gridContainer) {
            Logger.warn('GameUtils', '字母网格容器节点未找到');
            return null;
        }

        // 将触摸位置转换为网格容器的本地坐标
        const gridUITransform = gridContainer.getComponent(UITransform);
        if (!gridUITransform) {
            Logger.warn('GameUtils', '网格容器UITransform组件未找到');
            return null;
        }

        // 将世界坐标转换为网格容器的本地坐标
        const localPos = gridUITransform.convertToNodeSpaceAR(new Vec3(touchLocation.x, touchLocation.y, 0));

        // 遍历所有字母位置，检查触摸点是否在单元格范围内
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 8; col++) {
                const cellNode = letterGridController.getCellNode(row, col);
                if (cellNode) {
                    const uiTransform = cellNode.getComponent(UITransform);
                    if (uiTransform) {
                        // 获取单元格相对于父节点的边界框
                        const localBounds = uiTransform.getBoundingBox();

                        // 检查本地坐标是否在单元格边界框内
                        if (localBounds.contains(new Vec2(localPos.x, localPos.y))) {
                            return { row, col };
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * 将UI坐标转换为节点本地坐标
     * @param uiLocation UI坐标
     * @param targetNode 目标节点
     * @returns 本地坐标或null
     */
    public static convertUILocationToLocalPosition(uiLocation: any, targetNode: Node): Vec3 | null {
        if (!targetNode || !uiLocation) {
            return null;
        }

        const uiTransform = targetNode.getComponent(UITransform);
        if (!uiTransform) {
            return null;
        }

        return uiTransform.convertToNodeSpaceAR(new Vec3(uiLocation.x, uiLocation.y, 0));
    }

    /**
     * 检查本地坐标是否在节点边界内
     * @param localPos 本地坐标
     * @param node 目标节点
     * @returns 是否在边界内
     */
    public static isLocalPositionInNodeBounds(localPos: Vec3, node: Node): boolean {
        if (!node || !localPos) {
            return false;
        }

        const uiTransform = node.getComponent(UITransform);
        if (!uiTransform) {
            return false;
        }

        const bounds = uiTransform.getBoundingBox();
        return bounds.contains(new Vec2(localPos.x, localPos.y));
    }

    // ==================== 游戏逻辑工具 ====================

    /**
     * 计算两个字母位置之间的距离
     * @param pos1 位置1
     * @param pos2 位置2
     * @returns 距离值
     */
    public static calculatePositionDistance(pos1: LetterPosition, pos2: LetterPosition): number {
        if (!pos1 || !pos2) {
            return Number.MAX_VALUE;
        }

        const rowDiff = pos1.row - pos2.row;
        const colDiff = pos1.col - pos2.col;
        return Math.sqrt(rowDiff * rowDiff + colDiff * colDiff);
    }

    /**
     * 检查两个字母位置是否相邻（8方向）
     * @param pos1 位置1
     * @param pos2 位置2
     * @returns 是否相邻
     */
    public static arePositionsAdjacent(pos1: LetterPosition, pos2: LetterPosition): boolean {
        if (!pos1 || !pos2) {
            return false;
        }

        const rowDiff = Math.abs(pos1.row - pos2.row);
        const colDiff = Math.abs(pos1.col - pos2.col);
        return rowDiff <= 1 && colDiff <= 1 && (rowDiff + colDiff > 0);
    }

    /**
     * 验证字母位置是否在网格范围内
     * @param position 字母位置
     * @param gridRows 网格行数（默认9）
     * @param gridCols 网格列数（默认8）
     * @returns 是否在范围内
     */
    public static isPositionInGrid(position: LetterPosition, gridRows: number = 9, gridCols: number = 8): boolean {
        if (!position) {
            return false;
        }

        return position.row >= 0 && position.row < gridRows &&
               position.col >= 0 && position.col < gridCols;
    }

    /**
     * 获取字母位置的字符串表示（用于调试和缓存键）
     * @param position 字母位置
     * @returns 位置字符串
     */
    public static positionToString(position: LetterPosition): string {
        if (!position) {
            return 'invalid';
        }
        return `${position.row}-${position.col}`;
    }

    /**
     * 从字符串解析字母位置
     * @param positionString 位置字符串
     * @returns 字母位置或null
     */
    public static stringToPosition(positionString: string): LetterPosition | null {
        if (!positionString || typeof positionString !== 'string') {
            return null;
        }

        const parts = positionString.split('-');
        if (parts.length !== 2) {
            return null;
        }

        const row = parseInt(parts[0]);
        const col = parseInt(parts[1]);

        if (isNaN(row) || isNaN(col)) {
            return null;
        }

        return { row, col };
    }

    /**
     * 获取位置周围的8个相邻位置
     * @param position 中心位置
     * @param gridRows 网格行数
     * @param gridCols 网格列数
     * @returns 相邻位置数组
     */
    public static getAdjacentPositions(position: LetterPosition, gridRows: number = 9, gridCols: number = 8): LetterPosition[] {
        if (!position) {
            return [];
        }

        const adjacent: LetterPosition[] = [];
        const directions = [
            [-1, -1], [-1, 0], [-1, 1],
            [0, -1],           [0, 1],
            [1, -1],  [1, 0],  [1, 1]
        ];

        for (const [rowOffset, colOffset] of directions) {
            const newPos: LetterPosition = {
                row: position.row + rowOffset,
                col: position.col + colOffset
            };

            if (this.isPositionInGrid(newPos, gridRows, gridCols)) {
                adjacent.push(newPos);
            }
        }

        return adjacent;
    }

    /**
     * 计算路径的总长度
     * @param path 位置路径
     * @returns 路径长度
     */
    public static calculatePathLength(path: LetterPosition[]): number {
        if (!path || path.length < 2) {
            return 0;
        }

        let totalLength = 0;
        for (let i = 1; i < path.length; i++) {
            totalLength += this.calculatePositionDistance(path[i - 1], path[i]);
        }

        return totalLength;
    }

    /**
     * 检查路径是否连续（所有相邻位置都相邻）
     * @param path 位置路径
     * @returns 是否连续
     */
    public static isPathContinuous(path: LetterPosition[]): boolean {
        if (!path || path.length < 2) {
            return true;
        }

        for (let i = 1; i < path.length; i++) {
            if (!this.arePositionsAdjacent(path[i - 1], path[i])) {
                return false;
            }
        }

        return true;
    }

    /**
     * 生成指定范围内的随机整数
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @returns 随机整数
     */
    public static getRandomInt(min: number, max: number): number {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * 生成指定范围内的随机浮点数
     * @param min 最小值
     * @param max 最大值
     * @returns 随机浮点数
     */
    public static getRandomFloat(min: number, max: number): number {
        return Math.random() * (max - min) + min;
    }
}
