import { Logger } from '../../Utils/Logger';

/**
 * 翻译缓存服务
 * 
 * 负责管理翻译结果的缓存存储和检索功能。
 * 遵循单一职责原则，专门处理缓存管理、持久化存储和性能优化。
 * 
 * 核心功能：
 * - 内存缓存管理（Map结构）
 * - 本地存储持久化（localStorage）
 * - LRU缓存淘汰策略
 * - 缓存统计和性能监控
 * - 缓存清理和内存优化
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-03
 */
export class TranslationCacheService {

    /**
     * 内存缓存
     */
    private _memoryCache: Map<string, string> = new Map();

    /**
     * 缓存访问时间记录（用于LRU策略）
     */
    private _accessTimes: Map<string, number> = new Map();

    /**
     * 最大缓存条目数
     */
    private static readonly MAX_CACHE_SIZE = 100;

    /**
     * 本地存储键名
     */
    private static readonly STORAGE_KEY = 'word_translation_cache';

    /**
     * 缓存统计信息
     */
    private _stats = {
        hits: 0,
        misses: 0,
        sets: 0
    };

    /**
     * 构造函数 - 初始化缓存服务
     */
    constructor() {
        this._loadFromLocalStorage();
        Logger.info('TranslationCacheService', '翻译缓存服务已初始化');
    }

    /**
     * 获取翻译缓存
     * @param word 单词
     * @returns 翻译结果或null
     */
    public get(word: string): string | null {
        if (!word || word.trim().length === 0) {
            return null;
        }

        const key = this._normalizeKey(word);
        const cached = this._memoryCache.get(key);

        if (cached) {
            // 更新访问时间
            this._accessTimes.set(key, Date.now());
            this._stats.hits++;
            Logger.debug('TranslationCacheService', `缓存命中: ${key} -> ${cached}`);
            return cached;
        }

        this._stats.misses++;
        Logger.debug('TranslationCacheService', `缓存未命中: ${key}`);
        return null;
    }

    /**
     * 设置翻译缓存
     * @param word 单词
     * @param translation 翻译结果
     */
    public set(word: string, translation: string): void {
        if (!word || word.trim().length === 0 || !translation || translation.trim().length === 0) {
            Logger.warn('TranslationCacheService', '尝试缓存空的单词或翻译');
            return;
        }

        const key = this._normalizeKey(word);
        const value = translation.trim();

        // 检查缓存大小，必要时清理
        this._ensureCacheSize();

        // 设置缓存
        this._memoryCache.set(key, value);
        this._accessTimes.set(key, Date.now());
        this._stats.sets++;

        Logger.debug('TranslationCacheService', `缓存设置: ${key} -> ${value}`);

        // 异步保存到本地存储
        this._saveToLocalStorageAsync();
    }

    /**
     * 检查是否存在缓存
     * @param word 单词
     * @returns 是否存在
     */
    public has(word: string): boolean {
        if (!word || word.trim().length === 0) {
            return false;
        }

        const key = this._normalizeKey(word);
        return this._memoryCache.has(key);
    }

    /**
     * 清除指定单词的缓存
     * @param word 单词
     */
    public delete(word: string): boolean {
        if (!word || word.trim().length === 0) {
            return false;
        }

        const key = this._normalizeKey(word);
        const deleted = this._memoryCache.delete(key);
        if (deleted) {
            this._accessTimes.delete(key);
            Logger.debug('TranslationCacheService', `缓存删除: ${key}`);
            this._saveToLocalStorageAsync();
        }
        return deleted;
    }

    /**
     * 清空所有缓存
     */
    public clear(): void {
        this._memoryCache.clear();
        this._accessTimes.clear();
        this._stats = { hits: 0, misses: 0, sets: 0 };
        
        // 清空本地存储
        try {
            localStorage.removeItem(TranslationCacheService.STORAGE_KEY);
        } catch (error) {
            Logger.warn('TranslationCacheService', '清空本地存储失败', error as Error);
        }

        Logger.info('TranslationCacheService', '所有缓存已清空');
    }

    /**
     * 获取缓存统计信息
     */
    public getStats(): {
        size: number;
        hits: number;
        misses: number;
        sets: number;
        hitRate: number;
    } {
        const total = this._stats.hits + this._stats.misses;
        return {
            size: this._memoryCache.size,
            hits: this._stats.hits,
            misses: this._stats.misses,
            sets: this._stats.sets,
            hitRate: total > 0 ? (this._stats.hits / total) : 0
        };
    }

    /**
     * 标准化缓存键
     * @param word 原始单词
     * @returns 标准化的键
     */
    private _normalizeKey(word: string): string {
        return word.trim().toLowerCase();
    }

    /**
     * 确保缓存大小不超过限制
     */
    private _ensureCacheSize(): void {
        if (this._memoryCache.size >= TranslationCacheService.MAX_CACHE_SIZE) {
            this._evictLeastRecentlyUsed();
        }
    }

    /**
     * LRU淘汰策略 - 移除最久未使用的缓存项
     */
    private _evictLeastRecentlyUsed(): void {
        let oldestKey: string | null = null;
        let oldestTime = Date.now();

        // 找到最久未访问的键
        for (const [key, time] of this._accessTimes) {
            if (time < oldestTime) {
                oldestTime = time;
                oldestKey = key;
            }
        }

        // 移除最久未使用的项
        if (oldestKey) {
            this._memoryCache.delete(oldestKey);
            this._accessTimes.delete(oldestKey);
            Logger.debug('TranslationCacheService', `LRU淘汰: ${oldestKey}`);
        }
    }

    /**
     * 从本地存储加载缓存
     */
    private _loadFromLocalStorage(): void {
        try {
            const stored = localStorage.getItem(TranslationCacheService.STORAGE_KEY);
            if (stored) {
                const data = JSON.parse(stored);
                if (data && typeof data === 'object') {
                    // 恢复缓存数据
                    for (const [key, value] of Object.entries(data)) {
                        if (typeof value === 'string') {
                            this._memoryCache.set(key, value);
                            this._accessTimes.set(key, Date.now());
                        }
                    }
                    Logger.info('TranslationCacheService', `从本地存储加载了${this._memoryCache.size}条缓存`);
                }
            }
        } catch (error) {
            Logger.warn('TranslationCacheService', '从本地存储加载缓存失败', error as Error);
        }
    }

    /**
     * 异步保存到本地存储
     */
    private _saveToLocalStorageAsync(): void {
        // 使用setTimeout异步执行，避免阻塞主线程
        setTimeout(() => {
            this._saveToLocalStorage();
        }, 0);
    }

    /**
     * 保存到本地存储
     */
    private _saveToLocalStorage(): void {
        try {
            const data: { [key: string]: string } = {};
            for (const [key, value] of this._memoryCache) {
                data[key] = value;
            }
            localStorage.setItem(TranslationCacheService.STORAGE_KEY, JSON.stringify(data));
        } catch (error) {
            Logger.warn('TranslationCacheService', '保存到本地存储失败', error as Error);
        }
    }

    /**
     * 获取所有缓存的单词列表
     */
    public getAllCachedWords(): string[] {
        return Array.from(this._memoryCache.keys());
    }

    /**
     * 销毁缓存服务
     */
    public destroy(): void {
        this._saveToLocalStorage();
        this._memoryCache.clear();
        this._accessTimes.clear();
        Logger.info('TranslationCacheService', '翻译缓存服务已销毁');
    }
}
