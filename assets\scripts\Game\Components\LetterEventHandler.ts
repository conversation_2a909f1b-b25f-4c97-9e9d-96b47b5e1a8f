import { _decorator, Component, Node, EventTouch } from 'cc';
import { Logger } from '../../Utils/Logger';
import { LetterPosition } from '../../Game/Constants/GameConstants';

const { ccclass } = _decorator;

/**
 * 字母选择回调函数类型定义
 */
export type LetterSelectionCallback = (row: number, col: number, event: EventTouch) => void;

/**
 * 全局触摸事件回调函数类型定义
 */
export type GlobalTouchCallback = (event: EventTouch) => void;

/**
 * 字母事件处理器 - 专门处理字母相关的所有事件逻辑
 * 遵循单一职责原则，专注于事件处理功能
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-05
 */
@ccclass('LetterEventHandler')
export class LetterEventHandler extends Component {



    /**
     * 字母选择回调函数
     */
    private _letterSelectionCallback: LetterSelectionCallback | null = null;

    /**
     * 全局触摸开始回调
     */
    private _globalTouchStartCallback: GlobalTouchCallback | null = null;

    /**
     * 全局触摸移动回调
     */
    private _globalTouchMoveCallback: GlobalTouchCallback | null = null;

    /**
     * 全局触摸结束回调
     */
    private _globalTouchEndCallback: GlobalTouchCallback | null = null;

    /**
     * 是否已设置全局事件监听器
     */
    private _globalListenersSetup: boolean = false;

    /**
     * 组件初始化
     */
    onLoad(): void {
    }

    /**
     * 组件销毁时清理事件监听器
     */
    onDestroy(): void {
        this._removeGlobalEventListeners();
    }

    /**
     * 设置字母选择回调函数
     * @param callback 回调函数
     */
    public setLetterSelectionCallback(callback: LetterSelectionCallback): void {
        this._letterSelectionCallback = callback;
    }

    /**
     * 设置全局触摸事件回调函数
     * @param startCallback 触摸开始回调
     * @param moveCallback 触摸移动回调
     * @param endCallback 触摸结束回调
     */
    public setGlobalTouchCallbacks(
        startCallback: GlobalTouchCallback,
        moveCallback: GlobalTouchCallback,
        endCallback: GlobalTouchCallback
    ): void {
        this._globalTouchStartCallback = startCallback;
        this._globalTouchMoveCallback = moveCallback;
        this._globalTouchEndCallback = endCallback;
    }

    /**
     * 设置全局事件监听器
     */
    public setupGlobalEventListeners(): void {
        if (this._globalListenersSetup) {
            Logger.warn('LetterEventHandler', '全局事件监听器已设置，跳过重复设置');
            return;
        }

        if (!this.node) {
            Logger.error('LetterEventHandler', '节点未初始化，无法设置全局事件监听器');
            return;
        }

        // 设置全局触摸事件监听器
        this.node.on(Node.EventType.TOUCH_START, this._onGlobalTouchStart, this);
        this.node.on(Node.EventType.TOUCH_MOVE, this._onGlobalTouchMove, this);
        this.node.on(Node.EventType.TOUCH_END, this._onGlobalTouchEnd, this);
        this.node.on(Node.EventType.TOUCH_CANCEL, this._onGlobalTouchEnd, this);

        this._globalListenersSetup = true;
    }

    /**
     * 移除全局事件监听器
     */
    private _removeGlobalEventListeners(): void {
        if (!this._globalListenersSetup || !this.node || !this.node.isValid) {
            return;
        }

        this.node.off(Node.EventType.TOUCH_START, this._onGlobalTouchStart, this);
        this.node.off(Node.EventType.TOUCH_MOVE, this._onGlobalTouchMove, this);
        this.node.off(Node.EventType.TOUCH_END, this._onGlobalTouchEnd, this);
        this.node.off(Node.EventType.TOUCH_CANCEL, this._onGlobalTouchEnd, this);

        this._globalListenersSetup = false;
    }

    /**
     * 处理字母选择事件
     * @param row 行索引
     * @param col 列索引
     * @param event 触摸事件
     */
    public handleLetterSelection(row: number, col: number, event: EventTouch): void {
        if (this._letterSelectionCallback) {
            this._letterSelectionCallback(row, col, event);
        }
    }

    /**
     * 全局触摸开始事件处理
     * @param event 触摸事件
     */
    private _onGlobalTouchStart(event: EventTouch): void {
        if (this._globalTouchStartCallback) {
            this._globalTouchStartCallback(event);
        }
    }

    /**
     * 全局触摸移动事件处理
     * @param event 触摸事件
     */
    private _onGlobalTouchMove(event: EventTouch): void {
        if (this._globalTouchMoveCallback) {
            this._globalTouchMoveCallback(event);
        }
    }

    /**
     * 全局触摸结束事件处理
     * @param event 触摸事件
     */
    private _onGlobalTouchEnd(event: EventTouch): void {
        if (this._globalTouchEndCallback) {
            this._globalTouchEndCallback(event);
        }
    }

    /**
     * 获取事件处理器状态信息
     * @returns 状态信息对象
     */
    public getStatus(): {
        letterCallbackSet: boolean;
        globalCallbacksSet: boolean;
        globalListenersSetup: boolean;
    } {
        return {
            letterCallbackSet: this._letterSelectionCallback !== null,
            globalCallbacksSet: this._globalTouchStartCallback !== null && 
                               this._globalTouchMoveCallback !== null && 
                               this._globalTouchEndCallback !== null,
            globalListenersSetup: this._globalListenersSetup
        };
    }

    /**
     * 重置事件处理器
     */
    public reset(): void {
        this._removeGlobalEventListeners();
        this._letterSelectionCallback = null;
        this._globalTouchStartCallback = null;
        this._globalTouchMoveCallback = null;
        this._globalTouchEndCallback = null;
    }

    /**
     * 组件销毁时清理
     */
    onDestroy(): void {
        this.reset();
        Logger.debug('LetterEventHandler', '字母事件处理器已销毁');
    }
}
