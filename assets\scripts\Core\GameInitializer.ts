import { _decorator, Component, director, Node } from 'cc';
import { SceneManager } from './SceneManager';
import { GameDataManager } from './GameDataManager';
import { AudioManager } from './AudioManager';
import { AudioIntegrationService } from './AudioIntegrationService';
import { Logger } from '../Utils/Logger';
const { ccclass } = _decorator;

/**
 * 游戏初始化器
 *
 * 负责游戏启动时核心系统组件的初始化和配置，确保所有管理器在游戏开始前
 * 都已正确初始化并可用。采用持久化节点机制，在场景切换时保持系统状态。
 *
 * 核心功能：
 * - 核心管理器初始化：SceneManager、GameDataManager等
 * - 持久化节点管理：确保核心系统在场景切换时不被销毁
 * - 初始化顺序控制：按照依赖关系正确初始化各个系统
 * - 错误处理和恢复：初始化失败时的错误处理机制
 * - 系统状态监控：监控各个管理器的初始化状态
 *
 * 初始化流程：
 * 1. 设置节点持久化，确保在场景切换时不被销毁
 * 2. 按顺序初始化各个核心管理器
 * 3. 验证初始化结果，确保所有系统正常工作
 * 4. 记录初始化日志，便于调试和监控
 *
 * 技术特性：
 * - 单例模式确保全局唯一性
 * - 持久化节点技术保证系统连续性
 * - 详细的初始化日志用于调试
 * - 错误恢复机制提升系统稳定性
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-03
 */
@ccclass('GameInitializer')
export class GameInitializer extends Component {

    /**
     * 组件生命周期 - 初始化
     */
    onLoad() {
        // 检查当前节点是否在"管理器"节点下
        if (this.node.parent && this.node.parent.name === '管理器') {
            const managerNode = this.node.parent;

            // 检查管理器节点是否已经在场景根节点下
            const scene = director.getScene();
            if (managerNode.parent === scene) {
                // 管理器节点已经在根节点下，直接设置为持久化
                director.addPersistRootNode(managerNode);
                Logger.info('GameInitializer', '管理器节点已设置为持久化节点');
            } else {
                // 如果管理器节点不在根节点下，移动它
                if (scene) {
                    managerNode.setParent(scene);
                    director.addPersistRootNode(managerNode);
                    Logger.info('GameInitializer', '管理器节点已移动到根节点并设置为持久化节点');
                }
            }
        } else {
            Logger.error('GameInitializer', 'GameInitializer必须在"管理器"节点下');
            return;
        }

        // 日志配置现在由LogConfigManager组件管理
        // 不再在这里直接设置日志模式

        this._initializeCoreManagers();
    }

    /**
     * 组件启动时进行二次验证
     */
    start(): void {
        // 延迟验证，确保所有组件都已完成初始化
        this.scheduleOnce(() => {
            this._verifyAllSystemsAfterStart();
        }, 0.2);
    }

    /**
     * 启动后验证所有系统
     */
    private _verifyAllSystemsAfterStart(): void {
        Logger.info('GameInitializer', '开始启动后系统验证');

        const managerNode = this._getManagerNode();
        if (!managerNode) {
            Logger.error('GameInitializer', '管理器节点未找到');
            return;
        }

        // 验证AudioManager
        const audioManager = this._findComponentInNodeTree(managerNode, AudioManager);
        if (audioManager && audioManager.isFullyInitialized()) {
            Logger.info('GameInitializer', 'AudioManager已完全初始化');
        } else {
            Logger.warn('GameInitializer', 'AudioManager未完全初始化');
        }

        // 验证AudioIntegrationService
        const audioIntegrationService = this._findComponentInNodeTree(managerNode, AudioIntegrationService);
        if (audioIntegrationService) {
            Logger.info('GameInitializer', 'AudioIntegrationService验证通过');
        } else {
            Logger.warn('GameInitializer', 'AudioIntegrationService未找到');
        }

        Logger.info('GameInitializer', '启动后系统验证完成');
    }

    /**
     * 初始化核心管理器
     */
    private _initializeCoreManagers(): void {
        Logger.info('GameInitializer', '开始初始化核心管理器');

        // 验证并初始化音频系统（优先级最高，其他系统可能依赖音效）
        this._verifyAudioSystem();

        // 验证并初始化场景管理器
        this._verifySceneManager();

        // 验证并初始化游戏数据管理器
        this._verifyGameDataManager();

        Logger.info('GameInitializer', '核心管理器初始化完成');
    }

    /**
     * 验证场景管理器
     */
    private _verifySceneManager(): void {
        Logger.info('GameInitializer', '验证场景管理器');

        // 获取管理器节点
        const managerNode = this._getManagerNode();
        if (!managerNode) {
            Logger.error('GameInitializer', '无法找到管理器节点');
            return;
        }

        // 在管理器节点及其子节点中查找SceneManager
        const sceneManager = this._findComponentInNodeTree(managerNode, SceneManager);
        if (sceneManager) {
            Logger.info('GameInitializer', 'SceneManager组件已存在并正常工作');
        } else {
            Logger.error('GameInitializer', 'SceneManager组件未找到！请在编辑器中手动添加');
        }
    }

    /**
     * 验证音频系统
     */
    private _verifyAudioSystem(): void {
        Logger.info('GameInitializer', '验证音频系统');

        // 获取管理器节点（可能是当前节点的父节点或当前节点本身）
        const managerNode = this._getManagerNode();
        if (!managerNode) {
            Logger.error('GameInitializer', '无法找到管理器节点');
            return;
        }

        // 在管理器节点及其子节点中查找AudioManager
        const audioManager = this._findComponentInNodeTree(managerNode, AudioManager);
        if (audioManager) {
            Logger.info('GameInitializer', 'AudioManager组件已存在并正常工作');
        } else {
            Logger.error('GameInitializer', 'AudioManager组件未找到！请在编辑器中手动添加');
        }

        // 在管理器节点及其子节点中查找AudioIntegrationService
        const audioIntegrationService = this._findComponentInNodeTree(managerNode, AudioIntegrationService);
        if (audioIntegrationService) {
            Logger.info('GameInitializer', 'AudioIntegrationService组件已存在并正常工作');
        } else {
            Logger.error('GameInitializer', 'AudioIntegrationService组件未找到！请在编辑器中手动添加');
        }

        Logger.info('GameInitializer', '音频系统验证完成');
    }

    /**
     * 验证游戏数据管理器
     */
    private _verifyGameDataManager(): void {
        Logger.info('GameInitializer', '验证游戏数据管理器');

        // 获取管理器节点
        const managerNode = this._getManagerNode();
        if (!managerNode) {
            Logger.error('GameInitializer', '无法找到管理器节点');
            return;
        }

        // 在管理器节点及其子节点中查找GameDataManager
        const gameDataManager = this._findComponentInNodeTree(managerNode, GameDataManager);
        if (gameDataManager) {
            Logger.info('GameInitializer', 'GameDataManager组件已存在并正常工作');
        } else {
            Logger.error('GameInitializer', 'GameDataManager组件未找到！请在编辑器中手动添加');
        }
    }

    /**
     * 获取场景管理器实例
     * @returns SceneManager实例
     */
    public getSceneManager(): SceneManager | null {
        const managerNode = this._getManagerNode();
        return managerNode ? this._findComponentInNodeTree(managerNode, SceneManager) : null;
    }

    /**
     * 获取游戏数据管理器实例
     * @returns GameDataManager实例
     */
    public getGameDataManager(): GameDataManager | null {
        const managerNode = this._getManagerNode();
        return managerNode ? this._findComponentInNodeTree(managerNode, GameDataManager) : null;
    }

    /**
     * 获取管理器节点
     * @returns 管理器节点
     */
    private _getManagerNode(): Node | null {
        // 如果当前节点名称是"管理器"，返回当前节点
        if (this.node.name === '管理器') {
            return this.node;
        }

        // 如果当前节点的父节点名称是"管理器"，返回父节点
        if (this.node.parent && this.node.parent.name === '管理器') {
            return this.node.parent;
        }

        // 在当前节点的子节点中查找名为"管理器"的节点
        for (let i = 0; i < this.node.children.length; i++) {
            const child = this.node.children[i];
            if (child.name === '管理器') {
                return child;
            }
        }

        return null;
    }

    /**
     * 在节点树中查找指定类型的组件
     * @param node 起始节点
     * @param componentType 组件类型
     * @returns 找到的组件实例
     */
    private _findComponentInNodeTree<T extends Component>(node: Node, componentType: new() => T): T | null {
        // 首先在当前节点查找
        const component = node.getComponent(componentType);
        if (component) {
            return component;
        }

        // 在子节点中递归查找
        for (let i = 0; i < node.children.length; i++) {
            const childComponent = this._findComponentInNodeTree(node.children[i], componentType);
            if (childComponent) {
                return childComponent;
            }
        }

        return null;
    }
}
