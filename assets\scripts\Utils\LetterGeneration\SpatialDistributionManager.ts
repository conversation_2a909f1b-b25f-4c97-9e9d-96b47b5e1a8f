import { Logger } from '../Logger';
import { GameUtils } from '../GameUtils';
import { GridSpatialAnalyzer } from './GridSpatialAnalyzer';

/**
 * 象限分布信息接口
 */
export interface QuadrantInfo {
    id: number;
    startRow: number;
    endRow: number;
    startCol: number;
    endCol: number;
    centerRow: number;
    centerCol: number;
    wordCount: number;
    positions: number[];
}

/**
 * 空间分布策略接口
 */
export interface SpatialDistributionStrategy {
    quadrantDistribution: Map<number, number>;
    minDistanceBetweenWords: number;
    preferredRegions: string[];
    avoidanceZones: number[];
}

/**
 * 空间分布管理器
 * 
 * 专门负责单词在网格中的空间分布优化，确保单词均匀分散在各个象限中。
 * 遵循单一职责原则，只处理空间分布相关的逻辑。
 * 
 * 核心功能：
 * - 象限系统管理
 * - 空间分散策略
 * - 位置优先级计算
 * - 随机性增强
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-20
 */
export class SpatialDistributionManager {
    
    // 网格常量
    private static readonly GRID_ROWS = 9;
    private static readonly GRID_COLS = 8;

    /**
     * 生成高级空间分布位置序列
     * @returns 优化的位置数组
     */
    public static generateDistributedPositions(): number[] {
        Logger.info('SpatialDistributionManager', '🎯 启动高级空间分布算法');

        // 步骤1：创建象限分布系统
        const quadrants = this._createQuadrantSystem();
        
        // 步骤2：为每个象限生成候选位置
        const quadrantPositions = this._generateQuadrantPositions(quadrants);
        
        // 步骤3：应用空间分散策略
        const distributedPositions = this._applySpatialDistributionStrategy(quadrantPositions);
        
        // 步骤4：添加随机性增强
        const finalPositions = this._enhanceRandomness(distributedPositions);

        Logger.info('SpatialDistributionManager', `✅ 生成 ${finalPositions.length} 个分布式位置`);
        return finalPositions;
    }

    /**
     * 生成空间感知的位置序列
     * @param grid 当前网格状态
     * @param placedWords 已放置的单词信息
     * @returns 空间优化的位置数组
     */
    public static generateSpatiallyAwarePositions(
        grid: string[][],
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): number[] {
        // 步骤1：分析当前空间分布
        const spatialAnalysis = GridSpatialAnalyzer.analyzeSpatialDistribution(placedWords);

        // 步骤2：识别空白区域和拥挤区域
        const regionAnalysis = GridSpatialAnalyzer.analyzeRegionDensity(grid, placedWords);

        // 步骤3：生成优先级位置序列
        const prioritizedPositions = GridSpatialAnalyzer.generatePrioritizedPositions(spatialAnalysis, regionAnalysis);

        Logger.info('SpatialDistributionManager', `🎯 空间感知位置生成：${prioritizedPositions.length}个位置，优先空白区域`);
        return prioritizedPositions;
    }

    /**
     * 创建象限系统 - 将9x8网格划分为4个象限
     * @returns 象限信息数组
     */
    private static _createQuadrantSystem(): QuadrantInfo[] {
        const quadrants: QuadrantInfo[] = [];
        
        // 9x8网格的象限划分
        const midRow = Math.floor(this.GRID_ROWS / 2); // 4
        const midCol = Math.floor(this.GRID_COLS / 2); // 4

        // 🔧 修复：象限1：左上 (0-3, 0-3)
        quadrants.push({
            id: 1,
            startRow: 0,
            endRow: midRow - 1,
            startCol: 0,
            endCol: midCol - 1,
            centerRow: Math.floor((midRow - 1) / 2),
            centerCol: Math.floor((midCol - 1) / 2),
            wordCount: 0,
            positions: []
        });

        // 🔧 修复：象限2：右上 (0-3, 4-7)
        quadrants.push({
            id: 2,
            startRow: 0,
            endRow: midRow - 1,
            startCol: midCol,
            endCol: this.GRID_COLS - 1,
            centerRow: Math.floor((midRow - 1) / 2),
            centerCol: midCol + Math.floor((this.GRID_COLS - midCol) / 2),
            wordCount: 0,
            positions: []
        });

        // 🔧 修复：象限3：左下 (4-8, 0-3)
        quadrants.push({
            id: 3,
            startRow: midRow,
            endRow: this.GRID_ROWS - 1,
            startCol: 0,
            endCol: midCol - 1,
            centerRow: midRow + Math.floor((this.GRID_ROWS - midRow) / 2),
            centerCol: Math.floor((midCol - 1) / 2),
            wordCount: 0,
            positions: []
        });

        // 🔧 修复：象限4：右下 (4-8, 4-7)
        quadrants.push({
            id: 4,
            startRow: midRow,
            endRow: this.GRID_ROWS - 1,
            startCol: midCol,
            endCol: this.GRID_COLS - 1,
            centerRow: midRow + Math.floor((this.GRID_ROWS - midRow) / 2),
            centerCol: midCol + Math.floor((this.GRID_COLS - midCol) / 2),
            wordCount: 0,
            positions: []
        });

        Logger.info('SpatialDistributionManager', `📐 创建4象限系统：${quadrants.map(q => `Q${q.id}(${q.startRow}-${q.endRow},${q.startCol}-${q.endCol})`).join(', ')}`);
        return quadrants;
    }

    /**
     * 为每个象限生成候选位置
     * @param quadrants 象限信息
     * @returns 象限位置映射
     */
    private static _generateQuadrantPositions(quadrants: QuadrantInfo[]): Map<number, number[]> {
        const quadrantPositions = new Map<number, number[]>();

        for (const quadrant of quadrants) {
            const positions: number[] = [];
            
            // 生成象限内的所有位置
            for (let row = quadrant.startRow; row <= quadrant.endRow; row++) {
                for (let col = quadrant.startCol; col <= quadrant.endCol; col++) {
                    const position = row * this.GRID_COLS + col;
                    positions.push(position);
                }
            }

            // 按距离象限中心的远近排序，优先选择分散的位置
            positions.sort((a, b) => {
                const rowA = Math.floor(a / this.GRID_COLS);
                const colA = a % this.GRID_COLS;
                const rowB = Math.floor(b / this.GRID_COLS);
                const colB = b % this.GRID_COLS;

                const distA = Math.abs(rowA - quadrant.centerRow) + Math.abs(colA - quadrant.centerCol);
                const distB = Math.abs(rowB - quadrant.centerRow) + Math.abs(colB - quadrant.centerCol);

                return distB - distA; // 距离远的优先
            });

            quadrantPositions.set(quadrant.id, positions);
            Logger.info('SpatialDistributionManager', `📍 象限${quadrant.id}生成${positions.length}个候选位置`);
        }

        return quadrantPositions;
    }

    /**
     * 获取位置所属的象限
     * @param row 行
     * @param col 列
     * @returns 象限编号 (1-4)
     */
    public static getQuadrantForPosition(row: number, col: number): number {
        const midRow = Math.floor(this.GRID_ROWS / 2); // 4
        const midCol = Math.floor(this.GRID_COLS / 2); // 4

        if (row < midRow && col < midCol) return 1; // 左上 (0-3, 0-3)
        if (row < midRow && col >= midCol) return 2; // 右上 (0-3, 4-7)
        if (row >= midRow && col < midCol) return 3; // 左下 (4-8, 0-3)
        return 4; // 右下 (4-8, 4-7)
    }

    /**
     * 应用空间分散策略
     * @param quadrantPositions 象限位置映射
     * @returns 分散的位置数组
     */
    private static _applySpatialDistributionStrategy(quadrantPositions: Map<number, number[]>): number[] {
        const distributedPositions: number[] = [];
        const distributedPositionsSet = new Set<number>(); // 🔧 使用Set提高查找性能
        const minDistance = 2; // 单词起始位置之间的最小距离

        // 策略1：象限轮转分布 - 确保每个象限都有单词
        const quadrantIds = [1, 2, 3, 4];
        GameUtils.shuffleArray(quadrantIds); // 随机化象限访问顺序

        let currentQuadrantIndex = 0;
        const positionsPerQuadrant = Math.ceil(72 / 4); // 每象限大约18个位置

        for (let i = 0; i < 72; i++) {
            const currentQuadrant = quadrantIds[currentQuadrantIndex];
            const quadrantPositions_current = quadrantPositions.get(currentQuadrant) || [];

            // 从当前象限选择一个位置，确保与已选位置保持最小距离
            let selectedPosition = -1;

            for (const position of quadrantPositions_current) {
                if (this._isPositionValidForDistribution(position, distributedPositions, minDistance)) {
                    selectedPosition = position;
                    break;
                }
            }

            // 如果当前象限没有合适位置，尝试其他象限
            if (selectedPosition === -1) {
                for (const quadrantId of quadrantIds) {
                    if (quadrantId === currentQuadrant) continue;

                    const altQuadrantPositions = quadrantPositions.get(quadrantId) || [];
                    for (const position of altQuadrantPositions) {
                        if (this._isPositionValidForDistribution(position, distributedPositions, minDistance)) {
                            selectedPosition = position;
                            break;
                        }
                    }
                    if (selectedPosition !== -1) break;
                }
            }

            // 如果仍然没有找到，降低距离要求
            if (selectedPosition === -1) {
                for (const quadrantId of quadrantIds) {
                    const quadrantPositions_fallback = quadrantPositions.get(quadrantId) || [];
                    for (const position of quadrantPositions_fallback) {
                        if (!distributedPositionsSet.has(position)) {
                            selectedPosition = position;
                            break;
                        }
                    }
                    if (selectedPosition !== -1) break;
                }
            }

            if (selectedPosition !== -1) {
                distributedPositions.push(selectedPosition);
                distributedPositionsSet.add(selectedPosition); // 🔧 同步更新Set
            }

            // 🔧 修复：每4-5个位置轮转到下一个象限，确保均匀分布
            if ((i + 1) % Math.max(1, Math.floor(positionsPerQuadrant / 4)) === 0) {
                currentQuadrantIndex = (currentQuadrantIndex + 1) % 4;
            }
        }

        Logger.info('SpatialDistributionManager', `🎯 空间分散策略完成，生成${distributedPositions.length}个分布位置`);
        return distributedPositions;
    }

    /**
     * 检查位置是否符合分布要求
     * @param position 候选位置
     * @param existingPositions 已选位置
     * @param minDistance 最小距离
     * @returns 是否有效
     */
    private static _isPositionValidForDistribution(
        position: number,
        existingPositions: number[],
        minDistance: number
    ): boolean {
        const row = Math.floor(position / this.GRID_COLS);
        const col = position % this.GRID_COLS;

        for (const existingPos of existingPositions) {
            const existingRow = Math.floor(existingPos / this.GRID_COLS);
            const existingCol = existingPos % this.GRID_COLS;

            const distance = Math.abs(row - existingRow) + Math.abs(col - existingCol);
            if (distance < minDistance) {
                return false;
            }
        }

        return true;
    }

    /**
     * 增强随机性
     * @param positions 位置数组
     * @returns 随机性增强的位置数组
     */
    private static _enhanceRandomness(positions: number[]): number[] {
        // 策略1：分段随机化
        const segmentSize = 8; // 每8个位置为一段
        const enhancedPositions: number[] = [];

        for (let i = 0; i < positions.length; i += segmentSize) {
            const segment = positions.slice(i, i + segmentSize);
            GameUtils.shuffleArray(segment);
            enhancedPositions.push(...segment);
        }

        // 策略2：随机交换
        const swapCount = Math.floor(enhancedPositions.length / 4);
        for (let i = 0; i < swapCount; i++) {
            const index1 = Math.floor(Math.random() * enhancedPositions.length);
            const index2 = Math.floor(Math.random() * enhancedPositions.length);

            [enhancedPositions[index1], enhancedPositions[index2]] =
            [enhancedPositions[index2], enhancedPositions[index1]];
        }

        // 策略3：最终全局随机化（增强版）
        // 使用Fisher-Yates算法对整个数组进行最终打乱
        for (let i = enhancedPositions.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [enhancedPositions[i], enhancedPositions[j]] = [enhancedPositions[j], enhancedPositions[i]];
        }

        // 随机性增强完成
        return enhancedPositions;
    }
}
