import { Logger } from '../../Utils/Logger';

/**
 * 翻译API服务
 * 
 * 负责处理所有与翻译API相关的网络通信功能。
 * 遵循单一职责原则，专门处理API请求、响应解析和网络错误处理。
 * 
 * 核心功能：
 * - HTTP请求封装和管理
 * - 有道翻译API调用
 * - 网络超时和重试机制
 * - 响应数据解析和验证
 * - 网络错误处理和恢复
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-03
 */
export class TranslationAPIService {

    /**
     * 有道翻译API基础URL
     */
    private static readonly YOUDAO_API_URL = 'https://fanyi.youdao.com/translate?&doctype=json&type=AUTO&i=';

    /**
     * 请求超时时间（毫秒）
     */
    private static readonly REQUEST_TIMEOUT = 5000;

    /**
     * 最大重试次数
     */
    private static readonly MAX_RETRY_COUNT = 2;

    /**
     * 请求翻译
     * @param word 要翻译的单词
     * @returns Promise<翻译结果 | null>
     */
    public async requestTranslation(word: string): Promise<string | null> {
        if (!word || word.trim().length === 0) {
            Logger.warn('TranslationAPIService', '翻译请求的单词为空');
            return null;
        }

        const trimmedWord = word.trim().toLowerCase();
        Logger.debug('TranslationAPIService', `开始翻译请求: ${trimmedWord}`);

        // 尝试请求翻译，带重试机制
        for (let attempt = 1; attempt <= TranslationAPIService.MAX_RETRY_COUNT; attempt++) {
            try {
                const result = await this._performTranslationRequest(trimmedWord);
                if (result) {
                    Logger.info('TranslationAPIService', `翻译成功: ${trimmedWord} -> ${result}`);
                    return result;
                }
            } catch (error) {
                Logger.warn('TranslationAPIService', 
                    `翻译请求失败 (尝试 ${attempt}/${TranslationAPIService.MAX_RETRY_COUNT}): ${trimmedWord}`, 
                    error as Error);
                
                // 如果不是最后一次尝试，等待一段时间后重试
                if (attempt < TranslationAPIService.MAX_RETRY_COUNT) {
                    await this._delay(1000 * attempt); // 递增延迟
                }
            }
        }

        Logger.error('TranslationAPIService', `翻译请求最终失败: ${trimmedWord}`);
        return null;
    }

    /**
     * 执行翻译请求
     * @param word 要翻译的单词
     * @returns Promise<翻译结果 | null>
     */
    private async _performTranslationRequest(word: string): Promise<string | null> {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            const url = TranslationAPIService.YOUDAO_API_URL + encodeURIComponent(word);

            // 设置超时
            const timeoutId = setTimeout(() => {
                xhr.abort();
                reject(new Error('请求超时'));
            }, TranslationAPIService.REQUEST_TIMEOUT);

            xhr.onreadystatechange = () => {
                if (xhr.readyState === XMLHttpRequest.DONE) {
                    clearTimeout(timeoutId);

                    if (xhr.status === 200) {
                        try {
                            const translation = this._parseTranslationResponse(xhr.responseText);
                            resolve(translation);
                        } catch (error) {
                            reject(new Error(`响应解析失败: ${error}`));
                        }
                    } else {
                        reject(new Error(`HTTP错误: ${xhr.status}`));
                    }
                }
            };

            xhr.onerror = () => {
                clearTimeout(timeoutId);
                reject(new Error('网络请求失败'));
            };

            try {
                xhr.open('GET', url, true);
                xhr.send();
            } catch (error) {
                clearTimeout(timeoutId);
                reject(error);
            }
        });
    }

    /**
     * 解析翻译API响应
     * @param responseText 响应文本
     * @returns 翻译结果
     */
    private _parseTranslationResponse(responseText: string): string | null {
        try {
            const data = JSON.parse(responseText);
            
            // 验证响应结构
            if (!data || !data.translateResult || !Array.isArray(data.translateResult)) {
                Logger.warn('TranslationAPIService', '翻译响应格式无效');
                return null;
            }

            // 提取翻译结果
            const translateResult = data.translateResult;
            if (translateResult.length > 0 && 
                Array.isArray(translateResult[0]) && 
                translateResult[0].length > 0 && 
                translateResult[0][0].tgt) {
                
                const translation = translateResult[0][0].tgt.trim();
                if (translation.length > 0) {
                    return translation;
                }
            }

            Logger.warn('TranslationAPIService', '翻译结果为空');
            return null;
        } catch (error) {
            Logger.error('TranslationAPIService', '解析翻译响应失败', error as Error);
            return null;
        }
    }

    /**
     * 延迟执行
     * @param ms 延迟毫秒数
     */
    private _delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 检查网络连接状态
     * @returns 是否有网络连接
     */
    public isNetworkAvailable(): boolean {
        // 简单的网络状态检查
        return navigator.onLine;
    }

    /**
     * 获取API服务状态
     * @returns 服务状态信息
     */
    public getServiceStatus(): {
        isNetworkAvailable: boolean;
        apiUrl: string;
        timeout: number;
        maxRetries: number;
    } {
        return {
            isNetworkAvailable: this.isNetworkAvailable(),
            apiUrl: TranslationAPIService.YOUDAO_API_URL,
            timeout: TranslationAPIService.REQUEST_TIMEOUT,
            maxRetries: TranslationAPIService.MAX_RETRY_COUNT
        };
    }
}
