import { IWordData } from '../../Data/WordDatabase';
import { GameUtils } from '../GameUtils';
import { Logger } from '../Logger';
import { WordPlacementEngine } from './WordPlacementEngine';


/**
 * 字母生成引擎 - 统一的字母生成系统
 * 
 * 整合了原 LetterGenerationUtils.ts、WordPlacementEngine.ts 和 GameplayabilityValidator.ts 的核心功能
 * 遵循单一职责原则，专门负责字母网格的生成、验证和优化
 * 
 * 核心功能：
 * - 字母网格生成：支持交叉单词游戏的字母布局
 * - 单词放置算法：8方向单词放置，支持字母共享
 * - 可玩性验证：确保生成的网格能够正确游玩
 * - 质量评估：评估网格的游戏体验质量
 * 
 * 算法特性：
 * - 多阶段回溯算法：确保高成功率
 * - 空间分布优化：避免单词聚集
 * - 字母分布管理：避免连续相同字母
 * - 性能优化：超时保护和重试机制
 * 
 * <AUTHOR>
 * @version 3.0
 * @since 2025-07-20
 */
export class LetterGenerationEngine {

    // ==================== 配置常量 ====================

    /** 网格行数 */
    private static readonly GRID_ROWS = 9;
    /** 网格列数 */
    private static readonly GRID_COLS = 8;
    /** 总字母数 */
    private static readonly TOTAL_LETTERS = 72;
    /** 最大生成重试次数 */
    private static readonly MAX_GENERATION_ATTEMPTS = 15;
    /** 生成超时时间（毫秒） */
    private static readonly GENERATION_TIMEOUT = 3000;

    // ==================== 主要接口方法 ====================

    /**
     * 为交叉单词游戏生成字母网格（主入口方法）
     * 单词可以在8个方向放置，字母可以共用
     * @param words 单词数据数组
     * @returns 字母字符数组
     */
    public static generateLettersFromWords(words: IWordData[]): string[] {
        if (GameUtils.isArrayEmpty(words)) {
            Logger.error('LetterGenerationEngine', '单词列表为空！');
            return [];
        }

        Logger.info('LetterGenerationEngine', `开始生成字母网格，目标单词数: ${words.length}`);
        
        // 尝试生成一个包含所有单词的字母网格
        const gridResult = this._generateCrosswordGrid(words);

        if (gridResult.success) {
            Logger.info('LetterGenerationEngine', '字母网格生成成功');
            return gridResult.letters;
        } else {
            // 使用简化策略优化
            const simplifiedResult = this._generateSimplifiedGrid(words);
            return simplifiedResult.letters;
        }
    }

    /**
     * 生成字母网格（返回二维数组格式）
     * @param words 单词数据数组
     * @returns 9x8字母网格
     */
    public static generateLettersForGrid(words: IWordData[]): string[][] {
        if (GameUtils.isArrayEmpty(words)) {
            Logger.error('LetterGenerationEngine', '单词列表为空！');
            return this._createEmptyGrid();
        }

        Logger.info('LetterGenerationEngine', '生成字母网格（二维数组格式）');
        const letters = this.generateLettersFromWords(words);
        return this._convertLettersToGrid(letters);
    }

    /**
     * 验证字母集合是否能够在交叉单词游戏中拼出所有指定的单词
     * @param letters 字母数组
     * @param words 单词数据数组
     * @returns 验证结果
     */
    public static validateLettersCanFormWords(letters: string[], words: IWordData[]): boolean {
        if (GameUtils.isArrayEmpty(letters) || GameUtils.isArrayEmpty(words)) {
            Logger.error('LetterGenerationEngine', '字母或单词列表为空！');
            return false;
        }

        // 基本结构验证
        if (letters.length !== this.TOTAL_LETTERS) {
            // 字母数量不匹配，需要调整
            return false;
        }

        // 使用基本的字母存在性验证
        return this._basicLetterValidation(letters, words);
    }

    /**
     * 基本字母验证（当可玩性验证器不可用时使用）
     * @param letters 字母数组
     * @param words 目标单词
     * @returns 是否通过基本验证
     */
    private static _basicLetterValidation(letters: string[], words: IWordData[]): boolean {
        // 输入验证
        if (!letters || letters.length === 0) {
            // 字母数组为空
            return false;
        }

        if (!words || words.length === 0) {
            // 单词数组为空
            return false;
        }

        // 统计字母频率
        const letterCount = new Map<string, number>();
        letters.forEach(letter => {
            if (letter && typeof letter === 'string') {
                const count = letterCount.get(letter.toUpperCase()) || 0;
                letterCount.set(letter.toUpperCase(), count + 1);
            }
        });

        // 检查每个单词的字母是否都存在
        for (const word of words) {
            const wordLetterCount = new Map<string, number>();

            // 统计单词中每个字母的需求量
            for (const letter of word.word.toUpperCase()) {
                const count = wordLetterCount.get(letter) || 0;
                wordLetterCount.set(letter, count + 1);
            }

            // 检查是否有足够的字母
            for (const [letter, needed] of wordLetterCount) {
                const available = letterCount.get(letter) || 0;
                if (available < needed) {
                    // 字母数量不足，需要优化
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 快速验证网格可玩性
     * @param grid 字母网格
     * @param words 目标单词
     * @returns 是否可玩
     */
    public static quickValidatePlayability(grid: string[][], words: IWordData[]): boolean {
        // 将网格转换为字母数组进行基本验证
        const letters = this._convertGridToLetters(grid);
        return this._basicLetterValidation(letters, words);
    }

    /**
     * 将网格转换为字母数组
     * @param grid 字母网格
     * @returns 字母数组
     */
    private static _convertGridToLetters(grid: string[][]): string[] {
        // 验证输入网格
        if (!grid || grid.length === 0) {
            // 输入网格为空
            return [];
        }

        const letters: string[] = [];
        for (let row = 0; row < grid.length; row++) {
            // 检查行是否存在
            if (!grid[row]) {
                // 网格行为空，跳过
                continue;
            }

            for (let col = 0; col < grid[row].length; col++) {
                const letter = grid[row][col];
                if (letter && typeof letter === 'string') {
                    letters.push(letter);
                }
            }
        }
        return letters;
    }

    // ==================== 内部生成方法 ====================

    /**
     * 生成交叉单词网格
     * @param words 单词数据
     * @returns 生成结果
     */
    private static _generateCrosswordGrid(words: IWordData[]): {success: boolean, letters: string[]} {
        const maxAttempts = this.MAX_GENERATION_ATTEMPTS;
        let attempt = 0;
        const startTime = Date.now();

        while (attempt < maxAttempts) {
            attempt++;

            // 性能保护：如果生成时间超过限制，强制退出
            if (Date.now() - startTime > this.GENERATION_TIMEOUT) {
                // 生成超时，切换到备用策略
                break;
            }

            try {
                // 使用WordPlacementEngine生成网格
                const gridResult = WordPlacementEngine.generateCrosswordGrid(words);
                
                if (gridResult.success) {
                    // 成功生成交叉网格
                    return {
                        success: true,
                        letters: gridResult.letters
                    };
                }

                if (attempt % 5 === 0) {
                    // 继续尝试优化
                }

            } catch (error) {
                // 生成尝试出错
            }
        }

        // 切换到简化生成策略
        return { success: false, letters: [] };
    }

    /**
     * 生成简化网格（备用策略）
     * @param words 单词数据
     * @returns 生成结果
     */
    private static _generateSimplifiedGrid(words: IWordData[]): {success: boolean, letters: string[]} {
        Logger.info('LetterGenerationEngine', '使用简化策略生成字母网格');

        try {
            // 提取所有单词中的字母
            const allLetters: string[] = [];
            for (const wordData of words) {
                allLetters.push(...wordData.word.split(''));
            }

            // 如果字母不够，用随机字母填充
            while (allLetters.length < this.TOTAL_LETTERS) {
                const randomLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26)); // A-Z
                allLetters.push(randomLetter);
            }

            // 截取到正确长度并打乱
            const finalLetters = GameUtils.shuffleArray(allLetters.slice(0, this.TOTAL_LETTERS));

            Logger.info('LetterGenerationEngine', '简化策略生成完成');
            return {
                success: true,
                letters: finalLetters
            };

        } catch (error) {
            Logger.error('LetterGenerationEngine', '简化策略生成失败', error as Error);
            return {
                success: false,
                letters: this._createEmptyLetterArray()
            };
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 将字母数组转换为9x8网格
     * @param letters 字母数组
     * @returns 二维网格
     */
    private static _convertLettersToGrid(letters: string[]): string[][] {
        // 验证输入数组长度
        if (letters.length < this.TOTAL_LETTERS) {
            // 字母数组长度不足，用随机字母填充

            // 创建一个足够长度的数组，用原数组填充，不足部分用随机字母
            const paddedLetters = [...letters];
            while (paddedLetters.length < this.TOTAL_LETTERS) {
                const randomLetter = String.fromCharCode(65 + Math.floor(Math.random() * 26)); // A-Z
                paddedLetters.push(randomLetter);
            }
            letters = paddedLetters;
        }

        const grid: string[][] = [];

        for (let row = 0; row < this.GRID_ROWS; row++) {
            grid[row] = [];
            for (let col = 0; col < this.GRID_COLS; col++) {
                const index = row * this.GRID_COLS + col;
                // 现在可以安全访问，因为我们已经确保数组长度足够
                grid[row][col] = letters[index];
            }
        }

        return grid;
    }

    /**
     * 创建空的字母网格
     * @returns 空网格
     */
    private static _createEmptyGrid(): string[][] {
        const grid: string[][] = [];
        for (let row = 0; row < this.GRID_ROWS; row++) {
            grid[row] = [];
            for (let col = 0; col < this.GRID_COLS; col++) {
                grid[row][col] = 'A';
            }
        }
        return grid;
    }

    /**
     * 创建空的字母数组
     * @returns 空字母数组
     */
    private static _createEmptyLetterArray(): string[] {
        return new Array(this.TOTAL_LETTERS).fill('A');
    }

    /**
     * 获取生成统计信息
     * @returns 统计信息
     */
    public static getGenerationStats(): {
        gridRows: number;
        gridCols: number;
        totalLetters: number;
        maxAttempts: number;
        timeout: number;
    } {
        return {
            gridRows: this.GRID_ROWS,
            gridCols: this.GRID_COLS,
            totalLetters: this.TOTAL_LETTERS,
            maxAttempts: this.MAX_GENERATION_ATTEMPTS,
            timeout: this.GENERATION_TIMEOUT
        };
    }
}
