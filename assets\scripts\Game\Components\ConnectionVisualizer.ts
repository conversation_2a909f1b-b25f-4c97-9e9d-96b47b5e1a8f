import { _decorator, Component, Node, Color, Sprite } from 'cc';
import { LetterPosition } from '../../Game/Constants/GameConstants';
import { ColorThemeManager } from '../../Game/Managers/ColorThemeManager';
import { AnimationManager } from '../../Game/Animation/AnimationManager';
import { Logger } from '../../Utils/Logger';
const { ccclass, property } = _decorator;

/**
 * 连线可视化器
 *
 * 负责处理字母连接过程中的所有视觉效果和用户反馈，包括字母高亮、
 * 连接路径显示、动画效果管理等。遵循单一职责原则，专注于连线的视觉表现。
 *
 * 核心功能：
 * - 字母高亮效果管理（当前连接、已完成单词、提示等）
 * - 字母呼吸动画控制（连接过程中的视觉反馈）
 * - 连接状态可视化（成功、失败、进行中）
 * - 字母缩放状态管理（确保视觉一致性）
 * - 颜色主题集成（统一的颜色管理）
 *
 * 视觉效果类型：
 * - 当前连接路径高亮：实时显示用户正在连接的字母
 * - 已完成单词高亮：标记已成功连接的单词
 * - 失败反馈高亮：显示连接失败的视觉提示
 * - 提示高亮：显示可连接单词的提示效果
 * - 呼吸动画：连接过程中的字母缩放动画
 *
 * 技术特性：
 * - 基于Sprite的高亮方案，避免Graphics绘制冲突
 * - 多层保护的缩放重置机制，确保动画状态正确
 * - 颜色状态缓存，支持复杂的高亮状态管理
 * - 性能优化的动画管理，避免内存泄漏
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-04
 */
@ccclass('ConnectionVisualizer')
export class ConnectionVisualizer extends Component {

    /**
     * 字母网格根节点
     */
    @property(Node)
    letterGridNode: Node = null!;

    /**
     * 颜色主题管理器 - 统一管理所有颜色
     * 遵循单一职责原则，通过ColorThemeManager获取所有颜色
     */
    private _colorThemeManager: ColorThemeManager = null!;

    /**
     * 动画管理器 - 统一管理所有动画效果
     * 遵循单一职责原则，通过AnimationManager管理动画
     */
    private _animationManager: AnimationManager | null = null;

    /**
     * 当前连线路径
     */
    private _currentPath: LetterPosition[] = [];

    /**
     * 已完成的单词高亮
     */
    private _completedHighlights: Map<string, LetterPosition[]> = new Map();

    /**
     * 已完成字母的颜色映射
     */
    private _completedLetterColors: Map<string, Color> = new Map();

    /**
     * 当前提示路径（用于状态管理）
     */
    private _currentHintPath: LetterPosition[] = [];

    /**
     * 当前播放呼吸动画的字母位置集合
     * 用于跟踪哪些字母正在播放呼吸动画
     */
    private _breathingLetters: Set<string> = new Set();

    /**
     * 呼吸动画ID映射
     * key: 字母位置键(row-col), value: 动画ID
     */
    private _breathingAnimationIds: Map<string, string> = new Map();

    /**
     * 活跃的定时器引用，用于组件销毁时清理
     */
    private _activeTimers: Set<any> = new Set();



    /**
     * 字母Sprite组件缓存 (9行8列)
     */
    private _letterSprites: Map<string, Sprite> = new Map();

    /**
     * 原始Sprite颜色缓存
     */
    private _originalSpriteColors: Map<string, Color> = new Map();

    /**
     * 组件初始化
     */
    onLoad(): void {
        // 获取颜色主题管理器单例实例
        this._colorThemeManager = ColorThemeManager.getInstance();

        // 获取动画管理器单例实例
        this._animationManager = AnimationManager.getInstance();
        if (!this._animationManager) {
            Logger.warn('ConnectionVisualizer', '动画管理器未初始化，呼吸动画功能将不可用');
        }
    }

    /**
     * 组件启动 - 简化的缓存初始化
     */
    start(): void {
        this._initializeSpriteCache();
    }

    /**
     * 设置颜色主题管理器
     * 允许外部组件传入已配置的颜色管理器
     * @param colorThemeManager 颜色主题管理器实例
     */
    public setColorThemeManager(colorThemeManager: ColorThemeManager): void {
        this._colorThemeManager = colorThemeManager;
    }

    /**
     * 初始化Sprite缓存 - 简化版本
     */
    private _initializeSpriteCache(): void {
        if (!this.letterGridNode) {
            Logger.warn('ConnectionVisualizer', 'letterGridNode未设置');
            return;
        }

        this._cacheLetterSprites();
    }



    /**
     * 组件销毁时清理资源
     */
    onDestroy(): void {
        // 清理所有缓存的Map
        if (this._letterSprites) {
            this._letterSprites.clear();
        }
        if (this._originalSpriteColors) {
            this._originalSpriteColors.clear();
        }
        if (this._completedHighlights) {
            this._completedHighlights.clear();
        }
        if (this._completedLetterColors) {
            this._completedLetterColors.clear();
        }

        // 清理当前路径
        this._currentPath = [];
    }





    /**
     * 缓存字母Sprite组件 - 简化版本
     */
    private _cacheLetterSprites(): void {
        this._letterSprites.clear();
        this._originalSpriteColors.clear();

        // 遍历9行8列的字母网格
        for (let row = 0; row < 9; row++) {
            const rowNode = this.letterGridNode.children[row];
            if (!rowNode) continue;

            for (let col = 0; col < 8; col++) {
                const cellNode = rowNode.children[col];
                if (!cellNode) continue;

                const sprite = cellNode.getComponent(Sprite);
                if (sprite) {
                    const key = `${row}-${col}`;
                    this._letterSprites.set(key, sprite);
                    this._originalSpriteColors.set(key, new Color(sprite.color));
                }
            }
        }
    }





    /**
     * 开始连线
     * @param startPos 起始位置
     */
    public startConnection(startPos: LetterPosition): void {
        this._currentPath = [startPos];
        this._drawCurrentConnection();
    }

    /**
     * 添加位置到连线路径
     * @param pos 位置
     */
    public addToPath(pos: LetterPosition): void {
        this._currentPath.push(pos);
        this._drawCurrentConnection();
    }

    /**
     * 更新连线路径
     * @param path 完整路径
     */
    public updateConnectionPath(path: LetterPosition[]): void {
        this._currentPath = [...path];
        this._drawCurrentConnection();
    }

    /**
     * 清除当前连线
     */
    public clearConnection(): void {
        this._currentPath = [];
        // 清除临时高亮和呼吸动画
        this._clearTemporaryHighlight();
        this._stopAllBreathingAnimations();

        // 🔧 额外保险：确保所有字母缩放都重置到1.0
        this._ensureAllLettersScaleReset();
    }

    /**
     * 高亮显示完成的单词
     * @param path 单词路径
     * @param color 高亮颜色
     */
    public highlightCompletedWord(path: LetterPosition[], color: Color): void {
        const wordKey = this._getPathKey(path);
        this._completedHighlights.set(wordKey, [...path]);

        // 保存已完成字母的颜色
        for (const pos of path) {
            const key = `${pos.row}-${pos.col}`;
            this._completedLetterColors.set(key, new Color(color));
        }

        // 使用Sprite高亮方案
        this._highlightLetterSprites(path, color);

        // 🔧 修复：确保完成单词的字母缩放重置到1.0
        this._resetCompletedWordScale(path);
    }

    /**
     * 高亮当前连接路径中的字母
     *
     * 在用户连接字母过程中提供实时的视觉反馈，包括字母高亮和呼吸动画效果。
     * 自动清除之前的临时高亮状态，确保视觉效果的一致性。
     *
     * 视觉效果：
     * - 清除之前的临时高亮和动画
     * - 为路径中的字母应用高亮颜色
     * - 启动字母呼吸动画（缩放效果）
     * - 保持已完成单词的高亮状态不变
     *
     * @param {LetterPosition[]} path - 当前连接路径，包含字母的行列位置信息
     * @param {number} path[].row - 字母所在行（0-8）
     * @param {number} path[].col - 字母所在列（0-7）
     *
     * @example
     * ```typescript
     * // 高亮用户正在连接的字母路径
     * const currentPath = [
     *     { row: 0, col: 0 },
     *     { row: 0, col: 1 },
     *     { row: 1, col: 1 }
     * ];
     * connectionVisualizer.highlightCurrentPath(currentPath);
     * ```
     */
    public highlightCurrentPath(path: LetterPosition[]): void {
        // 先清除之前的临时高亮和呼吸动画
        this._clearTemporaryHighlight();
        this._stopAllBreathingAnimations();

        if (path.length > 0) {
            // 在连线过程中，允许高亮所有字母（包括已完成单词的字母）
            // 这样可以支持字母共享机制
            // 通过ColorThemeManager获取临时高亮颜色，遵循单一职责原则
            if (this._colorThemeManager) {
                const tempColor = this._colorThemeManager.getTemporaryHighlightColor();
                this._highlightLetterSprites(path, tempColor, true);

                // 为连接路径中的字母启动呼吸动画
                this._startBreathingAnimationForPath(path);
            } else {
                Logger.error('ConnectionVisualizer', 'ColorThemeManager未初始化');
                return;
            }
        }
    }

    /**
     * 显示连接失败的红色高亮效果
     * @param path 失败的连线路径
     * @param duration 显示持续时间（毫秒）
     */
    public showFailureHighlight(path: LetterPosition[], duration: number = 500): void {
        if (path.length === 0) return;

        // 先清除之前的临时高亮和呼吸动画
        this._clearTemporaryHighlight();
        this._stopAllBreathingAnimations();

        // 通过ColorThemeManager获取失败高亮颜色，遵循单一职责原则
        if (this._colorThemeManager) {
            const failureColor = this._colorThemeManager.getFailureHighlightColor();
            this._highlightLetterSprites(path, failureColor, true);

            // 快速清除红色高亮（0.5秒）
            const timer = this.scheduleOnce(() => {
                this._clearTemporaryHighlight();
                // 🔧 确保失败高亮清除后缩放也重置
                this._resetCompletedWordScale(path);
                this._activeTimers.delete(timer);
            }, duration / 1000);
            this._activeTimers.add(timer);
        } else {
            Logger.error('ConnectionVisualizer', 'ColorThemeManager未初始化');
            return;
        }
    }

    /**
     * 清除临时高亮 - 简化版本
     */
    private _clearTemporaryHighlight(): void {
        for (const [key, sprite] of this._letterSprites) {
            if (!sprite?.isValid) continue;

            if (this._isLetterInCompletedWord(key)) {
                const completedColor = this._completedLetterColors.get(key);
                if (completedColor) {
                    sprite.color = new Color(completedColor);
                }
            } else {
                const originalColor = this._originalSpriteColors.get(key);
                if (originalColor) {
                    sprite.color = new Color(originalColor);
                }
            }
        }
    }

    /**
     * 清除所有高亮 - 简化版本
     */
    public clearAllHighlights(): void {
        this._completedHighlights.clear();
        this._completedLetterColors.clear();
        this._stopAllBreathingAnimations();
        this._restoreAllSpritesToOriginal();
    }

    /**
     * 使用Sprite组件高亮字母
     * @param path 字母路径
     * @param color 高亮颜色
     * @param isTemporary 是否为临时高亮
     */
    private _highlightLetterSprites(path: LetterPosition[], color: Color, isTemporary: boolean = false): void {
        if (!path || path.length === 0) return;

        for (const pos of path) {
            const key = `${pos.row}-${pos.col}`;
            const sprite = this._letterSprites.get(key);

            if (sprite && sprite.isValid) {
                const highlightColor = new Color(color);
                if (isTemporary) {
                    // 使用原始颜色的透明度，如果是临时高亮则保持ColorThemeManager设置的透明度
                    highlightColor.a = color.a;
                }
                sprite.color = highlightColor;
            }
        }
    }

    /**
     * 恢复所有Sprite到原始颜色 - 简化版本
     */
    private _restoreAllSpritesToOriginal(): void {
        for (const [key, sprite] of this._letterSprites) {
            if (!sprite?.isValid) continue;

            const originalColor = this._originalSpriteColors.get(key);
            if (originalColor) {
                sprite.color = new Color(originalColor);
            }
        }
    }

    /**
     * 检查字母是否在已完成的单词中 - 简化版本
     */
    private _isLetterInCompletedWord(key: string): boolean {
        const [row, col] = key.split('-').map(Number);

        for (const [, path] of this._completedHighlights) {
            if (path?.some(p => p.row === row && p.col === col)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 显示提示高亮（智能状态管理）
     * @param path 提示路径
     * @param color 提示颜色
     */
    public showHintHighlight(path: LetterPosition[], color: Color): void {
        if (!path || path.length === 0) {
            Logger.warn('ConnectionVisualizer', '提示路径为空，无法显示高亮');
            return;
        }

        // 先清除之前的提示高亮（如果有）
        this.clearHintHighlight();

        // 保存当前提示路径
        this._currentHintPath = [...path];

        // 使用临时高亮机制显示提示，确保不覆盖已完成单词的状态
        this._highlightLetterSprites(path, color, true);
    }

    /**
     * 清除提示高亮（智能状态恢复）
     */
    public clearHintHighlight(): void {
        if (this._currentHintPath.length === 0) {
            return;
        }

        // 智能恢复每个字母的状态
        for (const pos of this._currentHintPath) {
            const key = `${pos.row}-${pos.col}`;
            const sprite = this._letterSprites.get(key);

            if (!sprite?.isValid) continue;

            // 检查字母是否在已完成的单词中
            if (this._isLetterInCompletedWord(key)) {
                // 恢复已完成单词的颜色
                const completedColor = this._completedLetterColors.get(key);
                if (completedColor) {
                    sprite.color = new Color(completedColor);
                }
            } else {
                // 恢复原始颜色
                const originalColor = this._originalSpriteColors.get(key);
                if (originalColor) {
                    sprite.color = new Color(originalColor);
                }
            }
        }

        // 清空提示路径
        this._currentHintPath = [];
    }

    /**
     * 重置完成单词的字母缩放到1.0
     * 确保单词完成后字母恢复到原始大小
     * @param path 完成单词的路径
     */
    private _resetCompletedWordScale(path: LetterPosition[]): void {
        if (!path || path.length === 0) return;

        for (const pos of path) {
            const letterNode = this._getLetterNode(pos);
            if (letterNode && letterNode.isValid) {
                // 🔧 强制停止该节点上的所有Tween动画
                const { Tween } = require('cc');
                Tween.stopAllByTarget(letterNode);

                // 立即重置缩放到1.0，确保视觉一致性
                letterNode.setScale(1.0, 1.0, 1.0);


            }
        }
    }

    /**
     * 确保所有字母的缩放都重置到1.0
     * 用作额外保险，防止任何缩放残留
     */
    private _ensureAllLettersScaleReset(): void {
        if (!this.letterGridController) return;

        // 遍历整个9x8网格，确保所有字母缩放都是1.0
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 8; col++) {
                const letterNode = this._getLetterNode({ row, col });
                if (letterNode && letterNode.isValid) {
                    // 检查当前缩放是否不是1.0
                    const currentScale = letterNode.scale;
                    if (Math.abs(currentScale.x - 1.0) > 0.01 ||
                        Math.abs(currentScale.y - 1.0) > 0.01 ||
                        Math.abs(currentScale.z - 1.0) > 0.01) {

                        // 🔧 强制停止该节点上的所有Tween动画
                        const { Tween } = require('cc');
                        Tween.stopAllByTarget(letterNode);

                        letterNode.setScale(1.0, 1.0, 1.0);

                    }
                }
            }
        }
    }

    // ==================== 呼吸动画管理方法组 ====================

    /**
     * 为指定路径的字母启动呼吸动画
     * @param path 字母路径
     */
    private _startBreathingAnimationForPath(path: LetterPosition[]): void {
        if (!this._animationManager || !path || path.length === 0) {
            return;
        }

        for (const pos of path) {
            this._startBreathingAnimationForLetter(pos);
        }
    }

    /**
     * 为单个字母启动呼吸动画
     * @param pos 字母位置
     */
    private _startBreathingAnimationForLetter(pos: LetterPosition): void {
        if (!this._animationManager) return;

        const key = `${pos.row}-${pos.col}`;

        // 如果该字母已经在播放呼吸动画，先停止
        if (this._breathingLetters.has(key)) {
            this._stopBreathingAnimationForLetter(pos);
        }

        // 获取字母节点
        const letterNode = this._getLetterNode(pos);
        if (!letterNode || !letterNode.isValid) {
            return;
        }

        // 创建呼吸动画配置 - 真正的无限循环播放
        const animationId = this._animationManager.playScaleAnimation({
            target: letterNode,
            targetScale: 1.2, // 呼吸效果：1.0 → 1.2 → 1.0
            config: {
                duration: 1.0, // 单向持续时间：1秒（放大或缩小阶段）
                easing: 'sineInOut', // 平滑的呼吸感
                yoyo: true, // 往返播放：1.0 → 1.2 → 1.0
                repeat: -1, // 真正的无限循环（-1表示无限重复）
                onComplete: () => {
                    // 只有在手动停止时才会触发，清理记录
                    this._breathingLetters.delete(key);
                    this._breathingAnimationIds.delete(key);
                }
            }
        });

        if (animationId) {
            this._breathingLetters.add(key);
            this._breathingAnimationIds.set(key, animationId);


        }
    }

    /**
     * 停止单个字母的呼吸动画
     * @param pos 字母位置
     */
    private _stopBreathingAnimationForLetter(pos: LetterPosition): void {
        if (!this._animationManager) return;

        const key = `${pos.row}-${pos.col}`;
        const animationId = this._breathingAnimationIds.get(key);

        if (animationId) {
            this._animationManager.stopAnimation(animationId);
            this._breathingLetters.delete(key);
            this._breathingAnimationIds.delete(key);

            // 恢复字母节点的原始缩放
            const letterNode = this._getLetterNode(pos);
            if (letterNode && letterNode.isValid) {
                letterNode.setScale(1.0, 1.0, 1.0);
            }


        }
    }

    /**
     * 停止所有呼吸动画
     */
    private _stopAllBreathingAnimations(): void {
        if (!this._animationManager) return;

        const stoppedCount = this._breathingAnimationIds.size;

        // 停止所有呼吸动画
        for (const [key, animationId] of this._breathingAnimationIds) {
            this._animationManager.stopAnimation(animationId);

            // 恢复字母节点的原始缩放
            const [row, col] = key.split('-').map(Number);
            const letterNode = this._getLetterNode({ row, col });
            if (letterNode && letterNode.isValid) {
                letterNode.setScale(1.0, 1.0, 1.0);
            }
        }

        // 清理记录
        this._breathingLetters.clear();
        this._breathingAnimationIds.clear();


    }

    /**
     * 获取字母节点
     * @param pos 字母位置
     * @returns 字母节点或null
     */
    private _getLetterNode(pos: LetterPosition): Node | null {
        if (!this.letterGridNode || pos.row < 0 || pos.row >= 9 || pos.col < 0 || pos.col >= 8) {
            return null;
        }

        const rowNode = this.letterGridNode.children[pos.row];
        if (!rowNode) return null;

        const cellNode = rowNode.children[pos.col];
        if (!cellNode) return null;

        // 返回Cell节点，因为缩放动画应该应用到Cell级别
        return cellNode;
    }

    /**
     * 绘制当前连线 - 使用Sprite高亮系统
     */
    private _drawCurrentConnection(): void {
        // 使用Sprite高亮系统显示当前连线路径
        if (this._currentPath.length > 0) {
            this.highlightCurrentPath(this._currentPath);
        }
    }





    /**
     * 获取路径的唯一键
     * @param path 路径
     * @returns 路径键
     */
    private _getPathKey(path: LetterPosition[]): string {
        return path.map(p => `${p.row}-${p.col}`).join('|');
    }



    // ==================== 组件生命周期清理 ====================

    /**
     * 组件销毁时清理资源
     * 确保所有动画都被正确停止和清理
     */
    onDestroy(): void {
        // 清理所有活跃的定时器
        this._activeTimers.forEach(timer => {
            if (timer && typeof timer.cancel === 'function') {
                timer.cancel();
            }
        });
        this._activeTimers.clear();

        // 停止所有呼吸动画
        this._stopAllBreathingAnimations();

        // 最终确保所有缩放重置
        this._ensureAllLettersScaleReset();

        // 清理所有缓存
        this._letterSprites.clear();
        this._originalSpriteColors.clear();
        this._completedHighlights.clear();
        this._completedLetterColors.clear();
        this._currentHintPath = [];
        this._currentPath = [];


    }

}
