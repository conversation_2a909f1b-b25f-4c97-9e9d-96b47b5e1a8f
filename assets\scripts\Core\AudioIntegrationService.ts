import { _decorator, Component } from 'cc';
import { AudioManager } from './AudioManager';
import { AudioConfigManager } from './AudioConfig';
import { Logger } from '../Utils/Logger';

const { ccclass } = _decorator;

/**
 * 音效集成服务 - 负责将音效系统集成到游戏逻辑中
 * 遵循单一职责原则，专门处理音效播放的业务逻辑集成
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-17
 */
@ccclass('AudioIntegrationService')
export class AudioIntegrationService extends Component {

    /**
     * 单例实例
     */
    private static _instance: AudioIntegrationService | null = null;

    /**
     * 音频管理器引用
     */
    private _audioManager: AudioManager | null = null;

    /**
     * 音效播放间隔控制
     */
    private _lastPlayTimes: Map<string, number> = new Map();

    /**
     * 获取单例实例
     */
    public static getInstance(): AudioIntegrationService | null {
        return AudioIntegrationService._instance;
    }

    /**
     * 组件初始化
     */
    onLoad(): void {
        Logger.info('AudioIntegrationService', '音效集成服务开始初始化');

        // 设置单例实例
        if (AudioIntegrationService._instance === null) {
            AudioIntegrationService._instance = this;
            Logger.info('AudioIntegrationService', '音效集成服务单例实例设置成功');
        } else {
            Logger.warn('AudioIntegrationService', '音效集成服务单例实例已存在，销毁重复实例');
            this.node.destroy();
            return;
        }
    }

    /**
     * 组件启动时初始化服务（确保AudioManager已初始化）
     */
    start(): void {
        this._initializeService();
    }

    /**
     * 初始化服务
     */
    private _initializeService(): void {
        // 获取音频管理器实例
        this._audioManager = AudioManager.getInstance();
        if (!this._audioManager) {
            Logger.warn('AudioIntegrationService', 'AudioManager未初始化，尝试延迟重试');
            // 延迟重试，给AudioManager更多时间初始化
            this.scheduleOnce(() => {
                this._retryInitializeService();
            }, 0.1);
        } else {
            Logger.info('AudioIntegrationService', '音频管理器连接成功');
            Logger.info('AudioIntegrationService', '音效集成服务初始化完成');
        }
    }

    /**
     * 重试初始化服务
     */
    private _retryInitializeService(): void {
        this._audioManager = AudioManager.getInstance();
        if (!this._audioManager) {
            Logger.warn('AudioIntegrationService', 'AudioManager仍未初始化，音效功能将不可用');
            Logger.warn('AudioIntegrationService', '请确保AudioManager组件已手动添加到管理器节点');
        } else {
            Logger.info('AudioIntegrationService', '音频管理器连接成功（重试后）');
            Logger.info('AudioIntegrationService', '音效集成服务初始化完成');
        }
    }

    // ==================== 字母连接相关音效 ====================

    /**
     * 播放字母连接音效
     * 在用户开始连接字母时播放
     */
    public playLetterConnectSound(): void {
        this._playSound(AudioConfigManager.AUDIO_TRIGGERS.LETTER_TOUCH_START);
    }

    /**
     * 播放字母拖拽音效
     * 在拖拽连线经过字母时播放
     */
    public playLetterDragSound(): void {
        this._playSound(AudioConfigManager.AUDIO_TRIGGERS.LETTER_DRAG_OVER, 100); // 100ms间隔，避免过于频繁
    }

    // ==================== UI按钮相关音效 ====================

    /**
     * 播放按钮点击音效
     * 在UI按钮被点击时播放
     */
    public playButtonClickSound(): void {
        this._playSound(AudioConfigManager.AUDIO_TRIGGERS.BUTTON_CLICK);
    }



    // ==================== 单词验证相关音效 ====================

    /**
     * 播放单词验证成功音效
     * 在单词验证成功时播放
     */
    public playWordSuccessSound(): void {
        this._playSound(AudioConfigManager.AUDIO_TRIGGERS.WORD_VALIDATION_SUCCESS);
    }

    /**
     * 播放单词验证失败音效
     * 在单词验证失败时播放
     */
    public playWordErrorSound(): void {
        this._playSound(AudioConfigManager.AUDIO_TRIGGERS.WORD_VALIDATION_ERROR);
    }



    // ==================== 提示相关音效 ====================

    /**
     * 播放提示显示音效
     * 在显示提示时播放
     */
    public playHintShowSound(): void {
        this._playSound(AudioConfigManager.AUDIO_TRIGGERS.HINT_SHOW);
    }

    // ==================== 关卡相关音效 ====================

    /**
     * 播放关卡完成音效
     * 在关卡完成时播放
     */
    public playLevelCompleteSound(): void {
        this._playSound(AudioConfigManager.AUDIO_TRIGGERS.LEVEL_COMPLETE);
    }

    // ==================== 音量和设置控制 ====================

    /**
     * 设置主音量
     * @param volume 音量值 (0-1)
     */
    public setMasterVolume(volume: number): void {
        if (this._audioManager) {
            this._audioManager.setMasterVolume(volume);
        }
    }

    /**
     * 获取主音量
     * @returns 主音量值
     */
    public getMasterVolume(): number {
        return this._audioManager ? this._audioManager.getMasterVolume() : 0;
    }

    /**
     * 设置分类音量
     * @param category 音效分类
     * @param volume 音量值 (0-1)
     */
    public setCategoryVolume(category: AudioCategory, volume: number): void {
        if (this._audioManager) {
            this._audioManager.setCategoryVolume(category, volume);
        }
    }

    /**
     * 获取分类音量
     * @param category 音效分类
     * @returns 分类音量值
     */
    public getCategoryVolume(category: AudioCategory): number {
        return this._audioManager ? this._audioManager.getCategoryVolume(category) : 0;
    }

    /**
     * 静音/取消静音
     * @param muted 是否静音
     */
    public setMuted(muted: boolean): void {
        if (this._audioManager) {
            this._audioManager.setMuted(muted);
        }
    }

    /**
     * 获取静音状态
     * @returns 是否静音
     */
    public isMuted(): boolean {
        return this._audioManager ? this._audioManager.isMuted() : false;
    }

    /**
     * 设置静音状态
     * @param muted 是否静音
     */
    public setMuted(muted: boolean): void {
        if (this._audioManager) {
            this._audioManager.setMuted(muted);
        }
    }

    /**
     * 获取静音状态
     * @returns 是否静音
     */
    public isMuted(): boolean {
        return this._audioManager ? this._audioManager.isMuted() : true;
    }

    /**
     * 应用音量预设
     * @param preset 预设名称
     */
    public applyVolumePreset(preset: keyof typeof AudioConfigManager.VOLUME_PRESETS): void {
        if (!this._audioManager) return;

        const presetConfig = AudioConfigManager.VOLUME_PRESETS[preset];
        
        // 设置主音量
        this._audioManager.setMasterVolume(presetConfig.master);

        // 设置分类音量
        Object.entries(presetConfig.categories).forEach(([category, volume]) => {
            this._audioManager!.setCategoryVolume(category as any, volume);
        });

        Logger.info('AudioIntegrationService', `应用音量预设: ${preset}`);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 播放音效（带播放间隔控制）
     * @param soundId 音效ID
     * @param minInterval 最小播放间隔（毫秒）
     */
    private _playSound(soundId: string, minInterval: number = 50): void {
        if (!this._audioManager) {
            return;
        }

        // 检查音频管理器是否已完全初始化
        if (!this._audioManager.isFullyInitialized()) {
            Logger.debug('AudioIntegrationService', '音频管理器未完全初始化，跳过音效播放');
            return;
        }

        // 检查播放间隔
        const currentTime = Date.now();
        const lastPlayTime = this._lastPlayTimes.get(soundId) || 0;
        
        if (currentTime - lastPlayTime < minInterval) {
            return; // 间隔太短，跳过播放
        }

        // 播放音效
        const success = this._audioManager.playSound(soundId);
        if (success) {
            this._lastPlayTimes.set(soundId, currentTime);
        }
    }

    /**
     * 预加载游戏音效
     * 在游戏开始前预加载必要的音效
     */
    public async preloadGameAudio(): Promise<void> {
        if (!this._audioManager) {
            Logger.warn('AudioIntegrationService', 'AudioManager未初始化，跳过音效预加载');
            return;
        }

        try {
            // 预加载所有需要预加载的音效
            const preloadConfigs = AudioConfigManager.getPreloadConfigs();
            const loadPromises = preloadConfigs.map(config => 
                this._audioManager!.preloadSound(config.id)
            );

            const results = await Promise.all(loadPromises);
            const successCount = results.filter(result => result).length;
            
            Logger.info('AudioIntegrationService', 
                `游戏音效预加载完成: ${successCount}/${results.length}`);
        } catch (error) {
            Logger.warn('AudioIntegrationService', '游戏音效预加载失败', error as Error);
        }
    }

    /**
     * 获取音效系统状态
     * @returns 状态信息
     */
    public getAudioSystemStatus(): {
        isAvailable: boolean;
        isMuted: boolean;
        masterVolume: number;
    } {
        return {
            isAvailable: this._audioManager !== null,
            isMuted: this.isMuted(),
            masterVolume: this.getMasterVolume()
        };
    }

    /**
     * 组件销毁时清理
     */
    onDestroy(): void {
        // 清理播放时间记录
        this._lastPlayTimes.clear();

        // 清理单例引用
        if (AudioIntegrationService._instance === this) {
            AudioIntegrationService._instance = null;
        }

        Logger.info('AudioIntegrationService', '音效集成服务已销毁');
    }
}
