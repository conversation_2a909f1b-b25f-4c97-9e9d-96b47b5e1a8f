import { AudioCategory, AudioConfig } from './AudioManager';

/**
 * 音效配置管理器 - 负责管理游戏音效的配置信息
 * 遵循单一职责原则，专门处理音效配置相关功能
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-17
 */
export class AudioConfigManager {

    /**
     * 游戏音效配置表 - 简化版本，只使用4个核心音效文件
     * 根据实际下载的音效文件进行配置
     */
    public static readonly GAME_AUDIO_CONFIGS: AudioConfig[] = [
        // UI交互音效 - 使用 ui_feedback 文件
        {
            id: 'letter_connect',
            category: AudioCategory.UI_FEEDBACK,
            filePath: 'audio/ui_feedback',
            volume: 0.7,
            loop: false,
            preload: true
        },
        {
            id: 'letter_drag',
            category: AudioCategory.UI_FEEDBACK,
            filePath: 'audio/ui_feedback',
            volume: 0.5,
            loop: false,
            preload: true
        },
        {
            id: 'button_click',
            category: AudioCategory.UI_FEEDBACK,
            filePath: 'audio/ui_feedback',
            volume: 0.8,
            loop: false,
            preload: true
        },
        {
            id: 'hint_show',
            category: AudioCategory.UI_FEEDBACK,
            filePath: 'audio/ui_feedback',
            volume: 0.6,
            loop: false,
            preload: true
        },

        // 成功反馈音效 - 使用 game_success 文件
        {
            id: 'word_success',
            category: AudioCategory.GAME_SUCCESS,
            filePath: 'audio/game_success',
            volume: 0.9,
            loop: false,
            preload: true
        },

        // 错误反馈音效 - 使用 game_error 文件
        {
            id: 'word_error',
            category: AudioCategory.GAME_ERROR,
            filePath: 'audio/game_error',
            volume: 0.5,
            loop: false,
            preload: true
        },

        // 胜利庆祝音效 - 使用 game_victory 文件
        {
            id: 'level_complete',
            category: AudioCategory.GAME_VICTORY,
            filePath: 'audio/game_victory',
            volume: 1.0,
            loop: false,
            preload: true
        }
    ];

    /**
     * 音效播放时机配置 - 优化版本
     * 定义各种游戏事件对应的音效ID
     */
    public static readonly AUDIO_TRIGGERS = {
        // 字母连接相关
        LETTER_TOUCH_START: 'letter_connect',
        LETTER_DRAG_OVER: 'letter_drag',

        // UI按钮相关
        BUTTON_CLICK: 'button_click',

        // 单词验证相关
        WORD_VALIDATION_SUCCESS: 'word_success',
        WORD_VALIDATION_ERROR: 'word_error',

        // 提示相关
        HINT_SHOW: 'hint_show',

        // 关卡相关
        LEVEL_COMPLETE: 'level_complete'
    } as const;

    /**
     * 音量配置预设
     */
    public static readonly VOLUME_PRESETS = {
        SILENT: {
            master: 0.0,
            categories: {
                [AudioCategory.UI_FEEDBACK]: 0.0,
                [AudioCategory.GAME_SUCCESS]: 0.0,
                [AudioCategory.GAME_ERROR]: 0.0,
                [AudioCategory.GAME_VICTORY]: 0.0
            }
        },
        QUIET: {
            master: 0.3,
            categories: {
                [AudioCategory.UI_FEEDBACK]: 0.4,
                [AudioCategory.GAME_SUCCESS]: 0.6,
                [AudioCategory.GAME_ERROR]: 0.3,
                [AudioCategory.GAME_VICTORY]: 0.7
            }
        },
        NORMAL: {
            master: 0.7,
            categories: {
                [AudioCategory.UI_FEEDBACK]: 0.7,
                [AudioCategory.GAME_SUCCESS]: 0.9,
                [AudioCategory.GAME_ERROR]: 0.5,
                [AudioCategory.GAME_VICTORY]: 1.0
            }
        },
        LOUD: {
            master: 1.0,
            categories: {
                [AudioCategory.UI_FEEDBACK]: 0.9,
                [AudioCategory.GAME_SUCCESS]: 1.0,
                [AudioCategory.GAME_ERROR]: 0.7,
                [AudioCategory.GAME_VICTORY]: 1.0
            }
        }
    } as const;

    /**
     * 性能配置
     */
    public static readonly PERFORMANCE_CONFIG = {
        // 最大同时播放音效数量
        MAX_CONCURRENT_SOUNDS: 8,
        
        // 音频资源缓存最大数量
        MAX_CACHED_RESOURCES: 20,
        
        // 资源释放时间阈值（毫秒）
        RESOURCE_RELEASE_THRESHOLD: 300000, // 5分钟
        
        // 音效播放间隔限制（毫秒）
        MIN_PLAY_INTERVAL: 50,
        
        // 预加载超时时间（毫秒）
        PRELOAD_TIMEOUT: 10000 // 10秒
    } as const;

    /**
     * 获取指定分类的所有音效配置
     * @param category 音效分类
     * @returns 音效配置数组
     */
    public static getConfigsByCategory(category: AudioCategory): AudioConfig[] {
        return AudioConfigManager.GAME_AUDIO_CONFIGS.filter(config => config.category === category);
    }

    /**
     * 获取需要预加载的音效配置
     * @returns 需要预加载的音效配置数组
     */
    public static getPreloadConfigs(): AudioConfig[] {
        return AudioConfigManager.GAME_AUDIO_CONFIGS.filter(config => config.preload);
    }

    /**
     * 根据音效ID获取配置
     * @param soundId 音效ID
     * @returns 音效配置或undefined
     */
    public static getConfigById(soundId: string): AudioConfig | undefined {
        return AudioConfigManager.GAME_AUDIO_CONFIGS.find(config => config.id === soundId);
    }

    /**
     * 验证音效配置的完整性
     * @returns 验证结果
     */
    public static validateConfigs(): {
        isValid: boolean;
        errors: string[];
        warnings: string[];
    } {
        const errors: string[] = [];
        const warnings: string[] = [];
        const usedIds = new Set<string>();

        AudioConfigManager.GAME_AUDIO_CONFIGS.forEach((config, index) => {
            // 检查必填字段
            if (!config.id) {
                errors.push(`配置${index}: 缺少音效ID`);
            }
            if (!config.filePath) {
                errors.push(`配置${index}: 缺少文件路径`);
            }

            // 检查ID重复
            if (config.id && usedIds.has(config.id)) {
                errors.push(`配置${index}: 音效ID重复 - ${config.id}`);
            } else if (config.id) {
                usedIds.add(config.id);
            }

            // 检查音量范围
            if (config.volume < 0 || config.volume > 1) {
                warnings.push(`配置${index}: 音量值超出范围 [0,1] - ${config.volume}`);
            }

            // 检查分类有效性
            if (!Object.values(AudioCategory).includes(config.category)) {
                errors.push(`配置${index}: 无效的音效分类 - ${config.category}`);
            }
        });

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 获取音效统计信息
     * @returns 统计信息
     */
    public static getStatistics(): {
        totalConfigs: number;
        preloadConfigs: number;
        categoryCounts: Record<AudioCategory, number>;
    } {
        const categoryCounts = {} as Record<AudioCategory, number>;
        
        // 初始化分类计数
        Object.values(AudioCategory).forEach(category => {
            categoryCounts[category] = 0;
        });

        // 统计各分类数量
        AudioConfigManager.GAME_AUDIO_CONFIGS.forEach(config => {
            categoryCounts[config.category]++;
        });

        return {
            totalConfigs: AudioConfigManager.GAME_AUDIO_CONFIGS.length,
            preloadConfigs: AudioConfigManager.getPreloadConfigs().length,
            categoryCounts
        };
    }
}
