
import { LogLevel } from './LoggerEnums';

/**
 * 统一日志管理器
 *
 * 提供统一的日志输出接口和格式化功能，支持多级别日志控制和性能优化。
 * 遵循单一职责原则，专注于日志管理、输出控制和格式化处理。
 *
 * 核心功能：
 * - 多级别日志支持：ERROR、WARN、INFO、DEBUG四个级别
 * - 统一日志格式：包含时间戳、组件名称、日志级别和消息内容
 * - 性能优化：支持日志级别控制，生产环境可关闭详细日志
 * - 组件标识：为每条日志添加组件来源标识，便于调试
 * - 错误处理：支持Error对象的详细信息输出
 *
 * 日志级别说明：
 * - ERROR (0): 错误日志，系统异常和严重问题
 * - WARN (1): 警告日志，潜在问题和异常情况
 * - INFO (2): 信息日志，重要的系统状态和操作记录
 * - DEBUG (3): 调试日志，详细的执行流程和状态信息
 *
 * 性能考虑：
 * - 默认日志级别设为WARN，只输出错误和警告，提升游戏性能
 * - 支持运行时动态调整日志级别
 * - 低级别日志在高级别设置下不会执行，避免性能开销
 *
 * 使用示例：
 * ```typescript
 * Logger.error('ComponentName', '发生严重错误', error);
 * Logger.warn('ComponentName', '检测到潜在问题');
 * Logger.info('ComponentName', '系统状态更新');
 * Logger.debug('ComponentName', '详细执行信息');
 * ```
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-07
 */
export class Logger {
    
    /**
     * 日志级别枚举（使用统一的枚举定义）
     * 数值越小优先级越高，ERROR级别最高，DEBUG级别最低
     */
    public static readonly LogLevel = LogLevel;

    /**
     * 当前日志级别 - 开发环境默认WARN级别，减少日志输出
     */
    private static _currentLevel: number = Logger.LogLevel.WARN;

    /**
     * 是否启用彩色输出
     */
    private static _enableColors: boolean = true;

    /**
     * 组件名称缓存，避免重复字符串创建
     */
    private static _componentCache: Map<string, string> = new Map();

    /**
     * 组件级别的日志控制
     * 可以为特定组件设置不同的日志级别
     */
    private static _componentLevels: Map<string, number> = new Map();

    /**
     * 静默组件列表 - 这些组件的日志将被完全屏蔽
     */
    private static _silentComponents: Set<string> = new Set([
        'TranslationCache',
        'GridSpatialAnalyzer',
        'WordPlacementEngine'
    ]);

    /**
     * 设置日志级别
     * @param level 日志级别
     */
    public static setLevel(level: number): void {
        Logger._currentLevel = level;
    }

    /**
     * 设置是否启用彩色输出
     * @param enabled 是否启用
     */
    public static setColorEnabled(enabled: boolean): void {
        Logger._enableColors = enabled;
    }

    /**
     * 设置组件的日志级别
     * @param component 组件名称
     * @param level 日志级别
     */
    public static setComponentLevel(component: string, level: number): void {
        Logger._componentLevels.set(component, level);
    }

    /**
     * 静默指定组件的所有日志
     * @param component 组件名称
     */
    public static silenceComponent(component: string): void {
        Logger._silentComponents.add(component);
    }

    /**
     * 取消静默指定组件
     * @param component 组件名称
     */
    public static unsilenceComponent(component: string): void {
        Logger._silentComponents.delete(component);
    }

    /**
     * 批量设置开发模式（显示更多日志）
     */
    public static setDevelopmentMode(): void {
        Logger._currentLevel = Logger.LogLevel.INFO;
        Logger._silentComponents.clear();
    }

    /**
     * 批量设置生产模式（只显示错误和警告）
     */
    public static setProductionMode(): void {
        Logger._currentLevel = Logger.LogLevel.WARN;
        Logger._silentComponents.add('TranslationCache');
        Logger._silentComponents.add('GridSpatialAnalyzer');
        Logger._silentComponents.add('WordPlacementEngine');
        Logger._silentComponents.add('TranslationApiClient');
        Logger._silentComponents.add('WordTranslationService');
    }

    /**
     * 检查组件是否应该输出日志
     * @param component 组件名称
     * @param level 日志级别
     * @returns 是否应该输出
     */
    private static _shouldLog(component: string, level: number): boolean {
        // 检查是否被静默
        if (Logger._silentComponents.has(component)) {
            return false;
        }

        // 检查组件特定级别
        const componentLevel = Logger._componentLevels.get(component);
        if (componentLevel !== undefined) {
            return componentLevel >= level;
        }

        // 使用全局级别
        return Logger._currentLevel >= level;
    }

    /**
     * 输出错误级别日志
     *
     * 用于记录系统错误、异常情况和严重问题。错误日志具有最高优先级，
     * 在任何日志级别设置下都会被输出，确保重要错误信息不会被遗漏。
     *
     * @param {string} component - 产生日志的组件名称，用于标识日志来源
     * @param {string} message - 错误消息内容，描述具体的错误情况
     * @param {Error} [error] - 可选的Error对象，包含详细的错误堆栈信息
     */
    public static error(component: string, message: string, error?: Error): void {
        if (Logger._shouldLog(component, Logger.LogLevel.ERROR)) {
            const formattedMessage = Logger._formatMessage(component, '❌', message);
            console.error(formattedMessage);
            if (error) {
                console.error(error);
            }
        }
    }

    /**
     * 警告日志 - 重要信息，通常需要关注
     * @param component 组件名称
     * @param message 消息内容
     */
    public static warn(component: string, message: string): void {
        if (Logger._shouldLog(component, Logger.LogLevel.WARN)) {
            const formattedMessage = Logger._formatMessage(component, '⚠️', message);
            console.warn(formattedMessage);
        }
    }

    /**
     * 信息日志 - 一般信息，正常运行时的关键状态
     * @param component 组件名称
     * @param message 消息内容
     */
    public static info(component: string, message: string): void {
        if (Logger._shouldLog(component, Logger.LogLevel.INFO)) {
            const formattedMessage = Logger._formatMessage(component, 'ℹ️', message);
            console.log(formattedMessage);
        }
    }

    /**
     * 调试日志 - 详细信息，仅在开发模式下输出
     * @param component 组件名称
     * @param message 消息内容
     */
    public static debug(component: string, message: string): void {
        if (Logger._shouldLog(component, Logger.LogLevel.DEBUG)) {
            const formattedMessage = Logger._formatMessage(component, '🔍', message);
            console.log(formattedMessage);
        }
    }

    /**
     * 成功日志 - 操作成功的反馈信息
     * @param component 组件名称
     * @param message 消息内容
     */
    public static success(component: string, message: string): void {
        if (Logger._shouldLog(component, Logger.LogLevel.INFO)) {
            const formattedMessage = Logger._formatMessage(component, '✅', message);
            console.log(formattedMessage);
        }
    }





    /**
     * 格式化日志消息
     * @param component 组件名称
     * @param icon 图标
     * @param message 消息内容
     * @returns 格式化后的消息
     */
    private static _formatMessage(component: string, icon: string, message: string): string {
        // 使用缓存避免重复字符串创建
        let formattedComponent = Logger._componentCache.get(component);
        if (!formattedComponent) {
            formattedComponent = `[${component}]`;
            Logger._componentCache.set(component, formattedComponent);
        }

        if (Logger._enableColors) {
            return `${formattedComponent} ${icon} ${message}`;
        } else {
            return `${formattedComponent} ${message}`;
        }
    }

    /**
     * 清理组件名称缓存
     */
    public static clearCache(): void {
        Logger._componentCache.clear();
    }

    /**
     * 获取当前日志级别
     */
    public static getCurrentLevel(): number {
        return Logger._currentLevel;
    }




}
