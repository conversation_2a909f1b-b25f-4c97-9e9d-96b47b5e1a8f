import { _decorator, Component } from 'cc';
import { LetterGenerationEngine } from '../Utils/LetterGeneration/LetterGenerationEngine';
import { WordDatabase } from '../Data/WordDatabase';
import { IWordData } from '../Data/WordDatabase';
import { Logger } from '../Utils/Logger';
const { ccclass } = _decorator;

/**
 * 游戏数据管理器
 *
 * 负责游戏数据的预生成、缓存和管理，提供高效的数据访问接口。
 * 通过预生成机制消除游戏中的数据生成延迟，提升用户体验。
 *
 * 核心功能：
 * - 游戏数据预生成：在场景切换时预先生成下一关的数据
 * - 数据缓存管理：缓存已生成的字母网格和单词数据
 * - 关卡数据管理：管理不同关卡的单词配置和难度设置
 * - 性能优化：通过预生成避免游戏中的计算延迟
 * - 内存管理：合理控制缓存大小，避免内存溢出
 *
 * 数据类型：
 * - 字母网格数据：9×8网格的字母布局
 * - 单词数据：目标单词列表和位置信息
 * - 关卡配置：关卡难度、单词数量等配置信息
 *
 * 预生成策略：
 * - 场景切换时预生成：在用户进入游戏前完成数据准备
 * - 智能缓存：根据内存情况动态调整缓存策略
 * - 数据验证：确保预生成的数据符合游戏规则
 * - 错误恢复：预生成失败时的实时生成备用方案
 *
 * 技术特性：
 * - 单例模式确保全局数据一致性
 * - 异步数据生成，避免阻塞主线程
 * - 内存优化的缓存机制
 * - 详细的数据生成日志
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-03
 */
@ccclass('GameDataManager')
export class GameDataManager extends Component {

    /**
     * 单例实例
     */
    private static _instance: GameDataManager | null = null;

    /**
     * 预生成的字母网格数据缓存
     */
    private _pregeneratedLetters: Map<number, string[]> = new Map();

    /**
     * 预生成的单词数据缓存
     */
    private _pregeneratedWords: Map<number, IWordData[]> = new Map();

    /**
     * 当前预生成的关卡ID
     */
    private _currentPregeneratedLevel: number = -1;

    /**
     * 预生成状态标记
     */
    private _isPregenerated: boolean = false;

    /**
     * 获取单例实例
     */
    public static getInstance(): GameDataManager | null {
        return GameDataManager._instance;
    }

    /**
     * 组件生命周期 - 初始化
     */
    onLoad() {
        // 设置单例实例
        if (GameDataManager._instance === null) {
            GameDataManager._instance = this;

            // 初始化数据结构
            this._initializeDataStructures();
        } else {
            Logger.warn('GameDataManager', '单例实例已存在，销毁重复实例');
            // 延迟销毁，避免在onLoad中立即销毁导致的问题
            this.scheduleOnce(() => {
                this.node.destroy();
            }, 0);
            return;
        }
    }

    /**
     * 初始化数据结构
     */
    private _initializeDataStructures(): void {
        this._pregeneratedLetters = new Map();
        this._pregeneratedWords = new Map();
        this._currentPregeneratedLevel = -1;
        this._isPregenerated = false;
    }

    /**
     * 组件销毁时清理单例引用
     */
    onDestroy() {
        if (GameDataManager._instance === this) {
            GameDataManager._instance = null;
        }
    }

    /**
     * 预生成指定关卡的游戏数据
     * @param levelId 关卡ID
     * @returns Promise<boolean> 预生成是否成功
     */
    public async pregenerateGameData(levelId: number): Promise<boolean> {
        try {
            // 验证关卡数据
            if (!WordDatabase.hasLevel(levelId)) {
                Logger.error('GameDataManager', `关卡 ${levelId} 不存在`);
                return false;
            }

            // 获取单词数据
            const words = WordDatabase.getLevelWords(levelId);
            if (!words || words.length === 0) {
                Logger.error('GameDataManager', `关卡 ${levelId} 没有单词数据`);
                return false;
            }

            // 生成字母网格（使用优化后的算法）
            const letters = LetterGenerationEngine.generateLettersFromWords(words);
            if (!letters || letters.length !== 72) {
                Logger.error('GameDataManager', `字母网格生成失败，期望72个字母，实际${letters?.length || 0}个`);
                return false;
            }

            // 验证生成的字母
            const isValid = LetterGenerationEngine.validateLettersCanFormWords(letters, words);
            if (!isValid) {
                Logger.error('GameDataManager', '生成的字母无法形成目标单词');
                return false;
            }

            // 缓存数据
            this._pregeneratedLetters.set(levelId, letters);
            this._pregeneratedWords.set(levelId, words);
            this._currentPregeneratedLevel = levelId;
            this._isPregenerated = true;

            return true;
        } catch (error) {
            Logger.error('GameDataManager', `预生成关卡 ${levelId} 数据时发生异常`, error as Error);
            return false;
        }
    }

    /**
     * 获取预生成的字母网格数据
     * @param levelId 关卡ID
     * @returns 字母数组，如果未预生成则返回null
     */
    public getPregeneratedLetters(levelId: number): string[] | null {
        const letters = this._pregeneratedLetters.get(levelId);
        if (letters) {
            Logger.info('GameDataManager', `获取预生成的字母数据，关卡: ${levelId}, 字母数量: ${letters.length}`);
            return [...letters]; // 返回副本，避免外部修改
        }
        
        Logger.warn('GameDataManager', `关卡 ${levelId} 的字母数据未预生成`);
        return null;
    }

    /**
     * 获取预生成的单词数据
     * @param levelId 关卡ID
     * @returns 单词数组，如果未预生成则返回null
     */
    public getPregeneratedWords(levelId: number): IWordData[] | null {
        const words = this._pregeneratedWords.get(levelId);
        if (words) {
            Logger.info('GameDataManager', `获取预生成的单词数据，关卡: ${levelId}, 单词数量: ${words.length}`);
            return [...words]; // 返回副本，避免外部修改
        }
        
        Logger.warn('GameDataManager', `关卡 ${levelId} 的单词数据未预生成`);
        return null;
    }

    /**
     * 检查指定关卡是否已预生成
     * @param levelId 关卡ID
     * @returns 是否已预生成
     */
    public isLevelPregenerated(levelId: number): boolean {
        const hasLetters = this._pregeneratedLetters.has(levelId);
        const hasWords = this._pregeneratedWords.has(levelId);
        return hasLetters && hasWords;
    }



    /**
     * 获取当前预生成状态
     * @returns 预生成状态信息
     */
    public getPregenerationStatus(): {
        isPregenerated: boolean;
        currentLevel: number;
        cachedLevels: number[];
    } {
        return {
            isPregenerated: this._isPregenerated,
            currentLevel: this._currentPregeneratedLevel,
            cachedLevels: Array.from(this._pregeneratedLetters.keys())
        };
    }

    /**
     * 强制重新生成指定关卡的数据（用于获取不同的随机布局）
     * @param levelId 关卡ID
     * @returns Promise<boolean> 重新生成是否成功
     */
    public async regenerateGameData(levelId: number): Promise<boolean> {
        Logger.info('GameDataManager', `强制重新生成关卡 ${levelId} 的数据`);
        
        // 清除现有数据
        this.clearPregeneratedData(levelId);
        
        // 重新生成
        return await this.pregenerateGameData(levelId);
    }
}
