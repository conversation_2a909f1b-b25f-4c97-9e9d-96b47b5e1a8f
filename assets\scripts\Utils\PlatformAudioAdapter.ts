import { sys } from 'cc';
import { Logger } from './Logger';
import { PlatformType, AudioStatus } from '../Game/Constants/PronunciationConstants';

/**
 * 音频能力接口
 */
export interface AudioCapabilities {
    /** 是否支持网络音频播放 */
    supportsNetworkAudio: boolean;
    /** 是否支持音频预加载 */
    supportsPreload: boolean;
    /** 是否需要用户交互才能播放 */
    requiresUserInteraction: boolean;
    /** 支持的音频格式 */
    supportedFormats: string[];
}

/**
 * 音频播放结果接口
 */
export interface AudioPlayResult {
    /** 播放是否成功 */
    success: boolean;
    /** 错误信息（如果失败） */
    error?: string;
    /** 音频时长（秒） */
    duration?: number;
}

/**
 * 平台音频适配器
 * 
 * 负责处理不同平台（微信小游戏、抖音小游戏、Web）的音频播放兼容性。
 * 遵循单一职责原则，专注于平台差异的抽象和适配。
 * 
 * 核心功能：
 * - 平台检测和能力识别
 * - 统一的音频播放接口
 * - 平台特定的错误处理
 * - 用户交互权限管理
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22
 */
export class PlatformAudioAdapter {
    
    /**
     * 当前平台类型
     */
    private static _currentPlatform: PlatformType | null = null;
    
    /**
     * 音频能力缓存
     */
    private static _audioCapabilities: AudioCapabilities | null = null;
    
    /**
     * 是否已请求用户交互权限
     */
    private static _userInteractionGranted: boolean = false;

    /**
     * 检测当前运行平台
     * @returns 平台类型
     */
    public static detectPlatform(): PlatformType {
        if (PlatformAudioAdapter._currentPlatform) {
            return PlatformAudioAdapter._currentPlatform;
        }

        try {
            // 检测微信小游戏
            if (typeof wx !== 'undefined' && wx.getSystemInfo) {
                PlatformAudioAdapter._currentPlatform = PlatformType.WECHAT;
                Logger.info('PlatformAudioAdapter', '检测到微信小游戏平台');
                return PlatformType.WECHAT;
            }

            // 检测抖音小游戏
            if (typeof tt !== 'undefined' && tt.getSystemInfo) {
                PlatformAudioAdapter._currentPlatform = PlatformType.DOUYIN;
                Logger.info('PlatformAudioAdapter', '检测到抖音小游戏平台');
                return PlatformType.DOUYIN;
            }

            // Web浏览器
            if (sys.isBrowser) {
                PlatformAudioAdapter._currentPlatform = PlatformType.WEB;
                Logger.info('PlatformAudioAdapter', '检测到Web浏览器平台');
                return PlatformType.WEB;
            }

            // 未知平台
            PlatformAudioAdapter._currentPlatform = PlatformType.UNKNOWN;
            Logger.warn('PlatformAudioAdapter', '未能识别当前平台，使用默认配置');
            return PlatformType.UNKNOWN;

        } catch (error) {
            Logger.error('PlatformAudioAdapter', '平台检测失败', error as Error);
            PlatformAudioAdapter._currentPlatform = PlatformType.UNKNOWN;
            return PlatformType.UNKNOWN;
        }
    }

    /**
     * 获取当前平台的音频播放能力
     * @returns 音频能力信息
     */
    public static getAudioCapabilities(): AudioCapabilities {
        if (PlatformAudioAdapter._audioCapabilities) {
            return PlatformAudioAdapter._audioCapabilities;
        }

        const platform = PlatformAudioAdapter.detectPlatform();
        let capabilities: AudioCapabilities;

        switch (platform) {
            case PlatformType.WECHAT:
                capabilities = {
                    supportsNetworkAudio: true,
                    supportsPreload: true,
                    requiresUserInteraction: true,
                    supportedFormats: ['mp3', 'wav', 'm4a']
                };
                break;

            case PlatformType.DOUYIN:
                capabilities = {
                    supportsNetworkAudio: true,
                    supportsPreload: true,
                    requiresUserInteraction: true,
                    supportedFormats: ['mp3', 'wav', 'm4a']
                };
                break;

            case PlatformType.WEB:
                capabilities = {
                    supportsNetworkAudio: true,
                    supportsPreload: false,
                    requiresUserInteraction: true,
                    supportedFormats: ['mp3', 'wav', 'ogg']
                };
                break;

            default:
                capabilities = {
                    supportsNetworkAudio: false,
                    supportsPreload: false,
                    requiresUserInteraction: true,
                    supportedFormats: []
                };
                break;
        }

        PlatformAudioAdapter._audioCapabilities = capabilities;
        Logger.info('PlatformAudioAdapter', `音频能力检测完成: ${JSON.stringify(capabilities)}`);
        return capabilities;
    }

    /**
     * 播放网络音频
     * @param audioUrl 音频URL
     * @param volume 音量控制 (0-1)，可选参数
     * @returns 播放结果Promise
     */
    public static async playNetworkAudio(audioUrl: string, volume: number = 1.0): Promise<AudioPlayResult> {
        const platform = PlatformAudioAdapter.detectPlatform();
        const capabilities = PlatformAudioAdapter.getAudioCapabilities();

        // 检查平台是否支持网络音频
        if (!capabilities.supportsNetworkAudio) {
            const error = '当前平台不支持网络音频播放';
            Logger.warn('PlatformAudioAdapter', error);
            return { success: false, error };
        }

        // 检查用户交互权限
        if (capabilities.requiresUserInteraction && !PlatformAudioAdapter._userInteractionGranted) {
            Logger.warn('PlatformAudioAdapter', '需要用户交互后才能播放音频');
            return { success: false, error: '需要用户交互权限' };
        }

        try {
            // 确保音量在有效范围内
            const clampedVolume = Math.max(0, Math.min(1, volume));

            switch (platform) {
                case PlatformType.WECHAT:
                    return await PlatformAudioAdapter._playWechatAudio(audioUrl, clampedVolume);

                case PlatformType.DOUYIN:
                    return await PlatformAudioAdapter._playDouyinAudio(audioUrl, clampedVolume);

                case PlatformType.WEB:
                    return await PlatformAudioAdapter._playWebAudio(audioUrl, clampedVolume);

                default:
                    const error = '不支持的平台类型';
                    Logger.error('PlatformAudioAdapter', error);
                    return { success: false, error };
            }
        } catch (error) {
            Logger.error('PlatformAudioAdapter', '音频播放异常', error as Error);
            return { success: false, error: (error as Error).message };
        }
    }

    /**
     * 请求用户交互权限
     * @returns 是否成功获取权限
     */
    public static async requestUserInteraction(): Promise<boolean> {
        if (PlatformAudioAdapter._userInteractionGranted) {
            return true;
        }

        try {
            const platform = PlatformAudioAdapter.detectPlatform();
            
            // 在小游戏平台，通常在用户首次点击后自动获得权限
            // 这里我们标记为已获得权限，实际权限由平台管理
            PlatformAudioAdapter._userInteractionGranted = true;
            
            Logger.info('PlatformAudioAdapter', '用户交互权限已获取');
            return true;
        } catch (error) {
            Logger.error('PlatformAudioAdapter', '获取用户交互权限失败', error as Error);
            return false;
        }
    }

    /**
     * 微信小游戏音频播放实现
     */
    private static async _playWechatAudio(audioUrl: string, volume: number = 1.0): Promise<AudioPlayResult> {
        return new Promise((resolve) => {
            const audio = wx.createInnerAudioContext();

            audio.src = audioUrl;
            audio.volume = volume; // 设置音量
            audio.autoplay = true;

            audio.onPlay(() => {
                Logger.debug('PlatformAudioAdapter', `微信音频开始播放，音量: ${volume.toFixed(2)}`);
            });

            audio.onEnded(() => {
                audio.destroy();
                resolve({ success: true, duration: audio.duration });
            });

            audio.onError((error) => {
                Logger.error('PlatformAudioAdapter', '微信音频播放失败', error);
                audio.destroy();
                resolve({ success: false, error: '音频播放失败' });
            });
        });
    }

    /**
     * 抖音小游戏音频播放实现
     */
    private static async _playDouyinAudio(audioUrl: string, volume: number = 1.0): Promise<AudioPlayResult> {
        return new Promise((resolve) => {
            const audio = tt.createInnerAudioContext();

            audio.src = audioUrl;
            audio.volume = volume; // 设置音量
            audio.autoplay = true;

            audio.onPlay(() => {
                Logger.debug('PlatformAudioAdapter', `抖音音频开始播放，音量: ${volume.toFixed(2)}`);
            });

            audio.onEnded(() => {
                audio.destroy();
                resolve({ success: true, duration: audio.duration });
            });

            audio.onError((error) => {
                Logger.error('PlatformAudioAdapter', '抖音音频播放失败', error);
                audio.destroy();
                resolve({ success: false, error: '音频播放失败' });
            });
        });
    }

    /**
     * Web浏览器音频播放实现
     */
    private static async _playWebAudio(audioUrl: string, volume: number = 1.0): Promise<AudioPlayResult> {
        return new Promise((resolve) => {
            const audio = new Audio(audioUrl);

            audio.volume = volume; // 设置音量

            audio.onloadeddata = () => {
                Logger.debug('PlatformAudioAdapter', `Web音频数据加载完成，音量: ${volume.toFixed(2)}`);
            };

            audio.onended = () => {
                resolve({ success: true, duration: audio.duration });
            };

            audio.onerror = (error) => {
                Logger.error('PlatformAudioAdapter', 'Web音频播放失败', error as any);
                resolve({ success: false, error: '音频播放失败' });
            };

            audio.play().catch((error) => {
                Logger.error('PlatformAudioAdapter', 'Web音频播放启动失败', error);
                resolve({ success: false, error: '音频播放启动失败' });
            });
        });
    }

    /**
     * 重置适配器状态（用于测试）
     */
    public static reset(): void {
        PlatformAudioAdapter._currentPlatform = null;
        PlatformAudioAdapter._audioCapabilities = null;
        PlatformAudioAdapter._userInteractionGranted = false;
    }
}
