import { _decorator, Component, AudioSource, AudioClip, resources, Node, assetManager } from 'cc';
import { Logger } from '../Utils/Logger';
import { AudioConfigManager } from './AudioConfig';

const { ccclass, property } = _decorator;

/**
 * 音效分类枚举
 */
export enum AudioCategory {
    UI_FEEDBACK = 'ui_feedback',      // UI交互音效（连接字母）
    GAME_SUCCESS = 'game_success',    // 成功反馈音效
    GAME_ERROR = 'game_error',        // 错误反馈音效
    GAME_VICTORY = 'game_victory'     // 胜利庆祝音效
}

/**
 * 音效配置接口
 */
export interface AudioConfig {
    id: string;                    // 音效唯一标识
    category: AudioCategory;       // 音效分类
    filePath: string;             // 文件路径
    volume: number;               // 默认音量 (0-1)
    loop: boolean;                // 是否循环播放
    preload: boolean;             // 是否预加载
}

/**
 * 音频资源信息
 */
interface AudioResource {
    clip: AudioClip;              // 音频剪辑
    config: AudioConfig;          // 配置信息
    lastUsed: number;             // 最后使用时间
}

/**
 * 音频管理器
 *
 * 负责游戏中所有音效的统一管理、播放控制和资源优化。
 * 采用单例模式确保全局音频系统的一致性，提供音效池管理和性能优化功能。
 *
 * 核心功能：
 * - 音效资源加载和缓存管理
 * - 音效播放控制（播放、停止、暂停、音量调节）
 * - 音效分类管理（UI反馈、游戏成功、错误提示等）
 * - 音效池管理（复用AudioSource组件，提升性能）
 * - 音量控制（全局音量、分类音量、单个音效音量）
 * - 资源清理和内存优化
 *
 * 音效分类：
 * - UI_FEEDBACK: UI交互音效（如字母连接音效）
 * - GAME_SUCCESS: 成功反馈音效（如单词完成音效）
 * - GAME_ERROR: 错误反馈音效（如连接失败音效）
 * - GAME_VICTORY: 胜利庆祝音效（如关卡完成音效）
 *
 * 技术特性：
 * - 音效池技术：预创建AudioSource组件，避免运行时创建开销
 * - 智能资源管理：自动清理长时间未使用的音效资源
 * - 并发控制：限制同时播放的音效数量，避免性能问题
 * - 错误恢复：音效播放失败时的自动恢复机制
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-17
 */
@ccclass('AudioManager')
export class AudioManager extends Component {

    // ==================== 编辑器配置属性 ====================

    /**
     * 主音量设置
     */
    @property({ displayName: "主音量", tooltip: "游戏音效的主音量控制", range: [0, 1, 0.1] })
    masterVolume: number = 1.0;

    /**
     * UI音效音量
     */
    @property({ displayName: "UI音效音量", tooltip: "UI交互音效的音量控制", range: [0, 1, 0.1] })
    uiFeedbackVolume: number = 0.8;

    /**
     * 成功音效音量
     */
    @property({ displayName: "成功音效音量", tooltip: "游戏成功反馈音效的音量控制", range: [0, 1, 0.1] })
    gameSuccessVolume: number = 1.0;

    /**
     * 错误音效音量
     */
    @property({ displayName: "错误音效音量", tooltip: "游戏错误反馈音效的音量控制", range: [0, 1, 0.1] })
    gameErrorVolume: number = 0.9;

    /**
     * 胜利音效音量
     */
    @property({ displayName: "胜利音效音量", tooltip: "关卡胜利音效的音量控制", range: [0, 1, 0.1] })
    gameVictoryVolume: number = 1.0;

    /**
     * 单例实例
     */
    private static _instance: AudioManager | null = null;

    /**
     * 音频源组件池（动态创建，不使用属性配置）
     */
    private audioSources: AudioSource[] = [];

    /**
     * 音频资源缓存
     */
    private _audioResources: Map<string, AudioResource> = new Map();

    /**
     * 音效配置表
     */
    private _audioConfigs: Map<string, AudioConfig> = new Map();

    /**
     * 主音量 (0-1)
     */
    private _masterVolume: number = 1.0;

    /**
     * 分类音量设置
     */
    private _categoryVolumes: Map<AudioCategory, number> = new Map();

    /**
     * 是否静音
     */
    private _isMuted: boolean = false;

    /**
     * 音频源使用状态
     */
    private _audioSourceUsage: Map<AudioSource, boolean> = new Map();

    /**
     * 音频管理器是否已完全初始化
     */
    private _isFullyInitialized: boolean = false;

    /**
     * 最大同时播放音效数量
     */
    private static readonly MAX_CONCURRENT_SOUNDS: number = 3;

    /**
     * 音频资源缓存最大数量
     */
    private static readonly MAX_CACHED_RESOURCES: number = 20;

    /**
     * 资源释放时间阈值（毫秒）
     */
    private static readonly RESOURCE_RELEASE_THRESHOLD: number = 300000; // 5分钟

    /**
     * 获取单例实例
     */
    public static getInstance(): AudioManager | null {
        return AudioManager._instance;
    }

    /**
     * 检查音频管理器是否已完全初始化
     * @returns 是否已完全初始化
     */
    public isFullyInitialized(): boolean {
        return this._isFullyInitialized;
    }

    /**
     * 验证音频系统状态
     * @returns 系统状态信息
     */
    public validateAudioSystem(): {
        isInitialized: boolean;
        audioSourceCount: number;
        validAudioSources: number;
        loadedResources: number;
        configuredSounds: number;
    } {
        const validSources = this.audioSources.filter(source =>
            source && source.node && source.node.isValid
        ).length;

        return {
            isInitialized: this._isFullyInitialized,
            audioSourceCount: this.audioSources.length,
            validAudioSources: validSources,
            loadedResources: this._audioResources.size,
            configuredSounds: this._audioConfigs.size
        };
    }

    /**
     * 手动重新初始化音频源池
     * 用于修复跨场景问题
     */
    public reinitializeAudioSources(): void {
        this._initializeAudioSourcePool();
    }



    /**
     * 重新构建音频源池
     */
    private _rebuildAudioSourcePool(): void {
        this.audioSources = [];
        this._audioSourceUsage.clear();

        // 收集所有有效的AudioSource组件
        this.node.children.forEach(child => {
            if (child.name.startsWith('AudioSource')) {
                const audioSource = child.getComponent(AudioSource);
                if (audioSource) {
                    this.audioSources.push(audioSource);
                    this._audioSourceUsage.set(audioSource, false);
                }
            }
        });

        // 如果音频源数量不足，创建新的音频源
        const requiredSources = AudioManager.MAX_CONCURRENT_SOUNDS;
        while (this.audioSources.length < requiredSources) {
            this._createAudioSource();
        }

        Logger.info('AudioManager', `音频源池重建完成，当前音频源数量: ${this.audioSources.length}`);
    }

    /**
     * 创建新的音频源组件
     */
    private _createAudioSource(): void {
        const audioSourceNode = new Node(`AudioSource_${this.audioSources.length}`);
        audioSourceNode.setParent(this.node);

        const audioSource = audioSourceNode.addComponent(AudioSource);
        if (audioSource) {
            this.audioSources.push(audioSource);
            this._audioSourceUsage.set(audioSource, false);
            Logger.debug('AudioManager', `创建新音频源: ${audioSourceNode.name}`);
        }
    }



    /**
     * 组件初始化
     */
    onLoad(): void {
        // 设置单例实例
        if (AudioManager._instance === null) {
            AudioManager._instance = this;
            this._initializeAudioManager();
        } else {
            Logger.warn('AudioManager', '音频管理器单例实例已存在，销毁重复实例');
            // 延迟销毁，避免在onLoad中立即销毁导致的问题
            this.scheduleOnce(() => {
                this.node.destroy();
            }, 0);
            return;
        }
    }

    /**
     * 组件启用时验证音频源池
     */
    onEnable(): void {
        // 在组件启用时只进行清理，不自动重新初始化
        if (this._isFullyInitialized) {
            this._cleanupInvalidAudioSources();
        }
    }

    /**
     * 初始化音频管理器
     */
    private _initializeAudioManager(): void {
        // 初始化分类音量
        this._initializeCategoryVolumes();

        // 初始化音频源池
        this._initializeAudioSourcePool();

        // 注册默认音效配置
        this._registerDefaultAudioConfigs();

        // 标记为已完全初始化
        this._isFullyInitialized = true;

        // 异步预加载核心音效（不阻塞初始化）
        this._preloadCoreAudioEffects().catch(error => {
            Logger.warn('AudioManager', '核心音效预加载失败', error as Error);
        });
    }

    /**
     * 初始化分类音量（使用编辑器配置的值）
     */
    private _initializeCategoryVolumes(): void {
        // 应用编辑器配置的主音量
        this._masterVolume = this.masterVolume;

        // 应用编辑器配置的分类音量
        this._categoryVolumes.set(AudioCategory.UI_FEEDBACK, this.uiFeedbackVolume);
        this._categoryVolumes.set(AudioCategory.GAME_SUCCESS, this.gameSuccessVolume);
        this._categoryVolumes.set(AudioCategory.GAME_ERROR, this.gameErrorVolume);
        this._categoryVolumes.set(AudioCategory.GAME_VICTORY, this.gameVictoryVolume);

        Logger.info('AudioManager', `音量配置完成 - 主音量: ${this._masterVolume.toFixed(2)}`);
    }

    /**
     * 初始化音频源池
     */
    private _initializeAudioSourcePool(): void {
        // 如果已经有足够的有效音频源，不需要重新创建
        const validSources = this.audioSources.filter(source =>
            source && source.node && source.node.isValid
        );

        if (validSources.length >= AudioManager.MAX_CONCURRENT_SOUNDS) {
            Logger.info('AudioManager', `音频源池已存在${validSources.length}个有效音频源，跳过初始化`);
            return;
        }

        // 清理无效的音频源
        this._cleanupInvalidAudioSources();

        // 只创建需要的音频源数量
        const needCount = AudioManager.MAX_CONCURRENT_SOUNDS - this.audioSources.length;
        if (needCount > 0) {
            for (let i = 0; i < needCount; i++) {
                const nodeIndex = this.audioSources.length;
                const audioNode = new Node(`AudioSource_${nodeIndex}`);
                const audioSource = audioNode.addComponent(AudioSource);

                // 确保音频源节点跟随AudioManager节点持久化
                audioNode.setParent(this.node);

                this.audioSources.push(audioSource);
                this._audioSourceUsage.set(audioSource, false);
            }
        }

        Logger.info('AudioManager', `音频源池初始化完成，共${this.audioSources.length}个音频源`);
    }

    /**
     * 清理无效的音频源
     */
    private _cleanupInvalidAudioSources(): void {
        const validSources: AudioSource[] = [];
        const newUsageMap = new Map<AudioSource, boolean>();

        this.audioSources.forEach(source => {
            if (source && source.node && source.node.isValid) {
                validSources.push(source);
                newUsageMap.set(source, this._audioSourceUsage.get(source) || false);
            } else {
                // 清理无效的音频源
                if (source && source.node) {
                    source.node.destroy();
                }
            }
        });

        this.audioSources = validSources;
        this._audioSourceUsage = newUsageMap;
    }

    /**
     * 注册默认音效配置
     * 使用实际下载的音效文件路径
     */
    private _registerDefaultAudioConfigs(): void {
        // 使用AudioConfigManager中的完整配置
        const allConfigs = AudioConfigManager.GAME_AUDIO_CONFIGS;

        allConfigs.forEach(config => {
            this._audioConfigs.set(config.id, config);
        });

        Logger.info('AudioManager', `注册了${allConfigs.length}个音效配置`);
    }

    /**
     * 预加载核心音效
     */
    private async _preloadCoreAudioEffects(): Promise<void> {
        const preloadPromises: Promise<boolean>[] = [];

        this._audioConfigs.forEach((config, soundId) => {
            if (config.preload) {
                preloadPromises.push(this.preloadSound(soundId));
            }
        });

        try {
            const results = await Promise.all(preloadPromises);
            const successCount = results.filter(result => result).length;
            Logger.info('AudioManager', `核心音效预加载完成，成功加载${successCount}/${results.length}个音效`);
        } catch (error) {
            Logger.warn('AudioManager', '部分核心音效预加载失败', error as Error);
        }
    }

    /**
     * 播放音效
     * @param soundId 音效ID
     * @param volume 音量覆盖 (可选)
     * @returns 是否成功播放
     */
    public playSound(soundId: string, volume?: number): boolean {
        // 检查音频管理器是否已完全初始化
        if (!this._isFullyInitialized) {
            Logger.debug('AudioManager', '音频管理器未完全初始化，跳过音效播放');
            return false;
        }

        if (this._isMuted) {
            return false;
        }

        const config = this._audioConfigs.get(soundId);
        if (!config) {
            Logger.warn('AudioManager', `音效配置不存在: ${soundId}`);
            return false;
        }

        // 获取可用的音频源
        const audioSource = this._getAvailableAudioSource();
        if (!audioSource) {
            Logger.warn('AudioManager', '没有可用的音频源');
            return false;
        }

        // 获取音频资源
        const resource = this._audioResources.get(soundId);
        if (!resource) {
            Logger.warn('AudioManager', `音频资源未加载: ${soundId}，跳过播放`);
            // 不进行动态加载，避免递归调用和时序问题
            // 音频资源应该在预加载阶段完成
            return false;
        }

        // 计算最终音量
        const categoryVolume = this._categoryVolumes.get(config.category) || 1.0;
        const finalVolume = (volume || config.volume) * categoryVolume * this._masterVolume;

        // 播放音效
        audioSource.clip = resource.clip;
        audioSource.volume = finalVolume;
        audioSource.loop = config.loop;
        audioSource.play();

        // 标记音频源为使用中
        this._audioSourceUsage.set(audioSource, true);

        // 更新资源使用时间
        resource.lastUsed = Date.now();

        // 设置播放完成回调
        if (!config.loop) {
            this.scheduleOnce(() => {
                this._audioSourceUsage.set(audioSource, false);
            }, resource.clip.getDuration());
        }

        Logger.debug('AudioManager', `播放音效: ${soundId}, 音量: ${finalVolume.toFixed(2)}`);
        return true;
    }

    /**
     * 设置主音量
     * @param volume 音量值 (0-1)
     */
    public setMasterVolume(volume: number): void {
        this._masterVolume = Math.max(0, Math.min(1, volume));
        Logger.info('AudioManager', `设置主音量: ${this._masterVolume.toFixed(2)}`);
    }

    /**
     * 获取主音量
     * @returns 主音量值
     */
    public getMasterVolume(): number {
        return this._masterVolume;
    }

    /**
     * 设置分类音量
     * @param category 音效分类
     * @param volume 音量值 (0-1)
     */
    public setCategoryVolume(category: AudioCategory, volume: number): void {
        const clampedVolume = Math.max(0, Math.min(1, volume));
        this._categoryVolumes.set(category, clampedVolume);
        Logger.info('AudioManager', `设置${category}分类音量: ${clampedVolume.toFixed(2)}`);
    }

    /**
     * 获取分类音量
     * @param category 音效分类
     * @returns 分类音量值
     */
    public getCategoryVolume(category: AudioCategory): number {
        return this._categoryVolumes.get(category) || 1.0;
    }

    /**
     * 设置静音状态
     * @param muted 是否静音
     */
    public setMuted(muted: boolean): void {
        this._isMuted = muted;

        if (muted) {
            // 停止所有正在播放的音效
            this._stopAllPlayingSounds();
        }

        Logger.info('AudioManager', `设置静音状态: ${muted ? '开启' : '关闭'}`);
    }

    /**
     * 获取静音状态
     * @returns 是否静音
     */
    public isMuted(): boolean {
        return this._isMuted;
    }

    /**
     * 预加载音效
     * @param soundId 音效ID
     * @returns 是否成功加载
     */
    public async preloadSound(soundId: string): Promise<boolean> {
        const config = this._audioConfigs.get(soundId);
        if (!config) {
            Logger.warn('AudioManager', `音效配置不存在: ${soundId}`);
            return false;
        }

        // 检查是否已经加载
        if (this._audioResources.has(soundId)) {
            return true;
        }

        return await this._loadAudioResource(soundId);
    }

    /**
     * 预加载分类音效
     * @param category 音效分类
     * @returns 是否全部成功加载
     */
    public async preloadCategory(category: AudioCategory): Promise<boolean> {
        const categoryConfigs = Array.from(this._audioConfigs.entries())
            .filter(([_, config]) => config.category === category);

        const loadPromises = categoryConfigs.map(([soundId, _]) => this.preloadSound(soundId));

        try {
            const results = await Promise.all(loadPromises);
            const successCount = results.filter(result => result).length;

            Logger.info('AudioManager', `分类${category}预加载完成: ${successCount}/${results.length}`);
            return successCount === results.length;
        } catch (error) {
            Logger.warn('AudioManager', `分类${category}预加载失败`, error as Error);
            return false;
        }
    }

    /**
     * 释放音效资源
     * @param soundId 音效ID
     */
    public releaseSound(soundId: string): void {
        const resource = this._audioResources.get(soundId);
        if (resource) {
            this._audioResources.delete(soundId);
            Logger.debug('AudioManager', `释放音效资源: ${soundId}`);
        }
    }

    /**
     * 释放分类音效资源
     * @param category 音效分类
     */
    public releaseCategory(category: AudioCategory): void {
        const toRelease: string[] = [];

        this._audioResources.forEach((resource, soundId) => {
            if (resource.config.category === category) {
                toRelease.push(soundId);
            }
        });

        toRelease.forEach(soundId => this.releaseSound(soundId));
        Logger.info('AudioManager', `释放分类${category}资源，共${toRelease.length}个音效`);
    }

    /**
     * 获取可用的音频源
     * @returns 可用的音频源或null
     */
    private _getAvailableAudioSource(): AudioSource | null {
        // 首先清理无效的音频源
        this._cleanupInvalidAudioSources();

        // 验证音频源池是否有效，只在真正需要时才重新初始化
        if (!this._validateAudioSourcePool()) {
            Logger.warn('AudioManager', '音频源池无效，尝试重新初始化');
            this._initializeAudioSourcePool();
        }

        // 查找未使用的音频源
        for (const [audioSource, inUse] of this._audioSourceUsage) {
            if (!inUse && audioSource && audioSource.node && audioSource.node.isValid) {
                return audioSource;
            }
        }

        // 如果所有音频源都在使用，返回第一个有效的（覆盖播放）
        for (const audioSource of this.audioSources) {
            if (audioSource && audioSource.node && audioSource.node.isValid) {
                Logger.debug('AudioManager', '所有音频源都在使用中，将覆盖播放');
                return audioSource;
            }
        }

        Logger.error('AudioManager', '没有可用的音频源');
        return null;
    }

    /**
     * 验证音频源池是否有效
     * @returns 是否有效
     */
    private _validateAudioSourcePool(): boolean {
        if (this.audioSources.length === 0) {
            return false;
        }

        // 计算有效音频源数量
        const validCount = this.audioSources.filter(source =>
            source && source.node && source.node.isValid
        ).length;

        // 只有当有效音频源数量少于最小需求时才认为无效
        const minRequired = Math.min(2, AudioManager.MAX_CONCURRENT_SOUNDS);
        return validCount >= minRequired;
    }

    /**
     * 停止所有正在播放的音效
     */
    private _stopAllPlayingSounds(): void {
        this.audioSources.forEach(audioSource => {
            if (audioSource.playing) {
                audioSource.stop();
                this._audioSourceUsage.set(audioSource, false);
            }
        });
    }

    /**
     * 加载音频资源
     * @param soundId 音效ID
     * @returns 是否成功加载
     */
    private async _loadAudioResource(soundId: string): Promise<boolean> {
        const config = this._audioConfigs.get(soundId);
        if (!config) {
            return false;
        }

        return new Promise<boolean>((resolve) => {
            // 首先尝试从resources目录加载
            resources.load(config.filePath, AudioClip, (err, clip) => {
                if (!err && clip) {
                    const resource: AudioResource = {
                        clip: clip,
                        config: config,
                        lastUsed: Date.now()
                    };

                    this._audioResources.set(soundId, resource);
                    Logger.debug('AudioManager', `音频资源加载成功(resources): ${soundId}`);
                    resolve(true);
                    return;
                }

                // 如果resources加载失败，尝试从bundle加载
                const bundle = assetManager.getBundle('main');
                if (bundle) {
                    bundle.load(config.filePath, AudioClip, (bundleErr, bundleClip) => {
                        if (bundleErr || !bundleClip) {
                            Logger.warn('AudioManager', `音频资源加载失败: ${soundId}`, bundleErr || err);
                            resolve(false);
                            return;
                        }

                        const resource: AudioResource = {
                            clip: bundleClip,
                            config: config,
                            lastUsed: Date.now()
                        };

                        this._audioResources.set(soundId, resource);
                        resolve(true);
                    });
                } else {
                    Logger.warn('AudioManager', `音频资源加载失败: ${soundId}`, err);
                    resolve(false);
                }
            });
        });
    }

    /**
     * 清理过期的音频资源
     */
    private _cleanupExpiredResources(): void {
        const currentTime = Date.now();
        const toRelease: string[] = [];

        this._audioResources.forEach((resource, soundId) => {
            if (currentTime - resource.lastUsed > AudioManager.RESOURCE_RELEASE_THRESHOLD) {
                toRelease.push(soundId);
            }
        });

        toRelease.forEach(soundId => this.releaseSound(soundId));

        if (toRelease.length > 0) {
            Logger.info('AudioManager', `清理过期音频资源: ${toRelease.length}个`);
        }
    }

    /**
     * 组件销毁时清理
     */
    onDestroy(): void {
        try {
            // 标记为未初始化，防止在销毁过程中被调用
            this._isFullyInitialized = false;

            // 清理定时任务
            this.unscheduleAllCallbacks();

            // 停止所有音效
            this._stopAllPlayingSounds();

            // 清理所有资源
            this._audioResources.clear();
            this._audioConfigs.clear();
            this._categoryVolumes.clear();
            this._audioSourceUsage.clear();

            // 清理单例引用
            if (AudioManager._instance === this) {
                AudioManager._instance = null;
            }

            Logger.info('AudioManager', '音频管理器已销毁');
        } catch (error) {
            Logger.warn('AudioManager', '音频管理器销毁时发生错误', error as Error);
        }
    }

    /**
     * 定期清理任务
     */
    start(): void {
        // 每5分钟清理一次过期资源
        this.schedule(this._cleanupExpiredResources, 300);
    }
}
