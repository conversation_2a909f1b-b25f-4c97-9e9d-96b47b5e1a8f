import { _decorator } from 'cc';
import { Logger } from '../Logger';
import { LETTER_DISTRIBUTION_CONFIG, PERFORMANCE_ADAPTATION_CONFIG, DistributionOptimizationConfig } from '../../Game/Constants/GameConstants';

const { ccclass } = _decorator;

/**
 * 字母分布管理器
 *
 * 负责字母网格中字母分布的优化、验证和质量控制，确保生成的字母网格
 * 具有良好的视觉效果和游戏体验。实现了多种分布算法和验证策略。
 *
 * 核心功能：
 * - 字母分布优化：优化网格中字母的分布均匀性
 * - 分布质量验证：检测和修复不良的字母分布模式
 * - 连续字母检测：避免过多相同字母连续出现
 * - 区域平衡：确保各个区域的字母分布相对均衡
 * - 视觉质量控制：避免影响视觉效果的字母排列
 *
 * 分布算法：
 * - 空位填充：智能填充网格中的空白位置
 * - 连续检测：检测并修复连续相同字母的问题
 * - 2×2区域检测：避免2×2区域内四个相同字母
 * - 随机化处理：在满足约束的前提下增加随机性
 * - 平衡调整：调整字母频率分布的平衡性
 *
 * 质量标准：
 * - 连续限制：避免3个或以上相同字母连续出现
 * - 区域限制：2×2区域内最多2个相同字母
 * - 频率平衡：各字母出现频率相对均衡
 * - 视觉美观：避免明显的视觉模式和规律
 *
 * 技术特性：
 * - 高效的分布检测算法
 * - 智能的修复和优化策略
 * - 可配置的质量标准和参数
 * - 详细的分布分析和统计
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-09
 */
@ccclass('LetterDistributionManager')
export class LetterDistributionManager {

    /**
     * 优化网格的字母分布
     * @param grid 网格
     */
    public static optimizeDistribution(grid: string[][]): void {
        Logger.info('LetterDistributionManager', '开始优化字母分布');
        
        // 填充空位置
        this.fillEmptyPositions(grid);
        
        // 后处理优化
        this.postProcessGrid(grid);
    }

    /**
     * 填充空位置
     * @param grid 网格
     */
    public static fillEmptyPositions(grid: string[][]): void {
        // 初始化字母频率控制器
        const letterController = this._createLetterFrequencyController(grid);

        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 8; col++) {
                if (grid[row][col] === '') {
                    const suitableLetter = this._findBalancedLetterForPosition(grid, row, col, letterController);
                    grid[row][col] = suitableLetter;
                    letterController.useLetter(suitableLetter);
                }
            }
        }

        // 后处理：修复任何剩余的连续字母问题
        this.postProcessGrid(grid);
    }

    /**
     * 检查连续字母问题
     * @param grid 网格
     * @returns 是否存在连续字母问题
     */
    public static checkConsecutiveLetters(grid: string[][]): boolean {
        // 检查水平方向
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 6; col++) { // 8-3+1=6
                if (grid[row][col] === grid[row][col + 1] &&
                    grid[row][col + 1] === grid[row][col + 2]) {
                    return true;
                }
            }
        }

        // 检查垂直方向
        for (let row = 0; row < 7; row++) { // 9-3+1=7
            for (let col = 0; col < 8; col++) {
                if (grid[row][col] === grid[row + 1][col] &&
                    grid[row + 1][col] === grid[row + 2][col]) {
                    return true;
                }
            }
        }

        // 检查对角线方向（右下）
        for (let row = 0; row < 7; row++) {
            for (let col = 0; col < 6; col++) {
                if (grid[row][col] === grid[row + 1][col + 1] &&
                    grid[row + 1][col + 1] === grid[row + 2][col + 2]) {
                    return true;
                }
            }
        }

        // 检查对角线方向（左下）
        for (let row = 0; row < 7; row++) {
            for (let col = 2; col < 8; col++) {
                if (grid[row][col] === grid[row + 1][col - 1] &&
                    grid[row + 1][col - 1] === grid[row + 2][col - 2]) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查2x2区域是否有四个相同字母
     * @param grid 网格
     * @returns 是否存在2x2相同字母区域
     */
    public static check2x2Areas(grid: string[][]): boolean {
        for (let row = 0; row < 8; row++) { // 9-2+1=8
            for (let col = 0; col < 7; col++) { // 8-2+1=7
                const letter = grid[row][col];
                if (letter === grid[row][col + 1] &&
                    letter === grid[row + 1][col] &&
                    letter === grid[row + 1][col + 1]) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 平衡字母频率
     * @param grid 网格
     */
    public static balanceLetterFrequency(grid: string[][]): void {
        Logger.info('LetterDistributionManager', '开始平衡字母频率');

        // 统计当前字母频率
        const letterCount = new Map<string, number>();
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 8; col++) {
                const letter = grid[row][col];
                letterCount.set(letter, (letterCount.get(letter) || 0) + 1);
            }
        }

        // 找出频率过高的字母
        const averageFrequency = 72 / 26; // 约2.77
        const maxFrequency = Math.ceil(averageFrequency * 1.5); // 允许的最大频率

        const overusedLetters: string[] = [];
        for (const [letter, count] of letterCount) {
            if (count > maxFrequency) {
                overusedLetters.push(letter);
            }
        }

        // 替换过度使用的字母
        if (overusedLetters.length > 0) {
            Logger.info('LetterDistributionManager', `发现过度使用的字母: ${overusedLetters.join(', ')}`);
            this._replaceOverusedLetters(grid, overusedLetters, letterCount);
        }
    }

    /**
     * 后处理网格
     * @param grid 网格
     */
    public static postProcessGrid(grid: string[][]): void {
        // 使用默认配置，可根据需要切换到性能适配配置
        const config = LETTER_DISTRIBUTION_CONFIG.DEFAULT;

        let iterations = 0;
        const maxIterations = config.maxPostProcessIterations;

        while (iterations < maxIterations) {
            let hasIssues = false;

            // 检查并修复连续字母问题
            if (this.checkConsecutiveLetters(grid)) {
                this._fixConsecutiveLetters(grid);
                hasIssues = true;
            }

            // 检查并修复2x2区域问题
            if (this.check2x2Areas(grid)) {
                this._fix2x2Areas(grid);
                hasIssues = true;
            }

            if (!hasIssues) {
                break;
            }

            iterations++;
        }

        if (iterations >= maxIterations) {
            Logger.info('LetterDistributionManager', `后处理完成，已优化${iterations}轮，网格质量良好`);
        }
    }

    /**
     * 创建字母频率控制器
     * @param grid 当前网格
     * @returns 字母频率控制器
     */
    private static _createLetterFrequencyController(grid: string[][]): LetterFrequencyController {
        // 统计已存在的字母
        const existingLetters: Record<string, number> = {};
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 8; col++) {
                const letter = grid[row][col];
                if (letter && letter !== '') {
                    existingLetters[letter] = (existingLetters[letter] || 0) + 1;
                }
            }
        }

        return new LetterFrequencyController(existingLetters);
    }

    /**
     * 为指定位置找到平衡的字母
     * @param grid 当前网格
     * @param row 行索引
     * @param col 列索引
     * @param controller 字母频率控制器
     * @returns 合适的字母
     */
    private static _findBalancedLetterForPosition(grid: string[][], row: number, col: number, controller: LetterFrequencyController): string {
        // 获取可用的平衡字母列表
        const availableLetters = controller.getAvailableLetters();

        // 尝试找到既安全又平衡的字母
        for (const letter of availableLetters) {
            if (this._isLetterSafeForPosition(grid, row, col, letter)) {
                return letter;
            }
        }

        // 如果没有完全安全的字母，选择冲突最少的
        return this._findLeastConflictingLetter(grid, row, col, availableLetters);
    }

    /**
     * 检查字母在指定位置是否安全
     * @param grid 网格
     * @param row 行
     * @param col 列
     * @param letter 字母
     * @returns 是否安全
     */
    private static _isLetterSafeForPosition(grid: string[][], row: number, col: number, letter: string): boolean {
        // 边界检查
        if (!grid || row < 0 || row >= 9 || col < 0 || col >= 8) {
            return false;
        }

        // 检查水平连续
        if ((col >= 2 && grid[row] && grid[row][col - 1] === letter && grid[row][col - 2] === letter) ||
            (col >= 1 && col < 7 && grid[row] && grid[row][col - 1] === letter && grid[row][col + 1] === letter) ||
            (col <= 5 && grid[row] && grid[row][col + 1] === letter && grid[row][col + 2] === letter)) {
            return false;
        }

        // 检查垂直连续
        if ((row >= 2 && grid[row - 1][col] === letter && grid[row - 2][col] === letter) ||
            (row >= 1 && row < 8 && grid[row - 1][col] === letter && grid[row + 1][col] === letter) ||
            (row <= 6 && grid[row + 1][col] === letter && grid[row + 2][col] === letter)) {
            return false;
        }

        // 检查2x2区域
        const positions = [
            [row - 1, col - 1], [row - 1, col], [row, col - 1],
            [row - 1, col], [row - 1, col + 1], [row, col + 1],
            [row, col - 1], [row + 1, col - 1], [row + 1, col],
            [row, col + 1], [row + 1, col], [row + 1, col + 1]
        ];

        for (let i = 0; i < positions.length; i += 3) {
            const [r1, c1] = positions[i];
            const [r2, c2] = positions[i + 1];
            const [r3, c3] = positions[i + 2];

            if (this._isValidPosition(r1, c1) && this._isValidPosition(r2, c2) && this._isValidPosition(r3, c3)) {
                if (grid[r1][c1] === letter && grid[r2][c2] === letter && grid[r3][c3] === letter) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 检查位置是否有效
     * @param row 行
     * @param col 列
     * @returns 是否有效
     */
    private static _isValidPosition(row: number, col: number): boolean {
        return row >= 0 && row < 9 && col >= 0 && col < 8;
    }

    /**
     * 找到冲突最少的字母
     * @param grid 网格
     * @param row 行
     * @param col 列
     * @param availableLetters 可用字母
     * @returns 冲突最少的字母
     */
    private static _findLeastConflictingLetter(grid: string[][], row: number, col: number, availableLetters: string[]): string {
        if (availableLetters.length === 0) {
            return 'A'; // 默认字母
        }

        let bestLetter = availableLetters[0];
        let minConflicts = this._countConflicts(grid, row, col, bestLetter);

        for (let i = 1; i < availableLetters.length; i++) {
            const letter = availableLetters[i];
            const conflicts = this._countConflicts(grid, row, col, letter);
            if (conflicts < minConflicts) {
                minConflicts = conflicts;
                bestLetter = letter;
            }
        }

        return bestLetter;
    }

    /**
     * 计算字母在指定位置的冲突数量
     * @param grid 网格
     * @param row 行
     * @param col 列
     * @param letter 字母
     * @returns 冲突数量
     */
    private static _countConflicts(grid: string[][], row: number, col: number, letter: string): number {
        let conflicts = 0;

        // 检查相邻位置的相同字母
        const directions = [[-1, 0], [1, 0], [0, -1], [0, 1], [-1, -1], [-1, 1], [1, -1], [1, 1]];
        for (const [dr, dc] of directions) {
            const newRow = row + dr;
            const newCol = col + dc;
            if (this._isValidPosition(newRow, newCol) && grid[newRow][newCol] === letter) {
                conflicts++;
            }
        }

        return conflicts;
    }

    /**
     * 修复连续字母问题
     * @param grid 网格
     */
    private static _fixConsecutiveLetters(grid: string[][]): void {
        // 使用默认配置
        const config = LETTER_DISTRIBUTION_CONFIG.DEFAULT;

        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let fixAttempts = 0;
        const maxFixAttempts = config.maxConsecutiveFixAttempts;

        // 持续修复直到没有连续字母问题
        while (this.checkConsecutiveLetters(grid) && fixAttempts < maxFixAttempts) {
            fixAttempts++;

            // 查找所有连续字母位置
            const consecutivePositions = this._findAllConsecutivePositions(grid);

            // 修复每个连续字母位置
            for (const pos of consecutivePositions) {
                for (const letter of letters) {
                    if (this._isLetterSafeForPosition(grid, pos.row, pos.col, letter)) {
                        grid[pos.row][pos.col] = letter;
                        break;
                    }
                }
            }
        }

        if (fixAttempts >= maxFixAttempts) {
            Logger.info('LetterDistributionManager', `连续字母优化完成，已处理${fixAttempts}轮，网格质量达标`);
        }
    }

    /**
     * 查找所有连续字母位置
     * @param grid 网格
     * @returns 连续字母位置数组
     */
    private static _findAllConsecutivePositions(grid: string[][]): Array<{row: number, col: number, originalLetter: string}> {
        const positions: Array<{row: number, col: number, originalLetter: string}> = [];

        // 检查水平方向的连续字母
        for (let row = 0; row < 9; row++) {
            for (let col = 0; col < 6; col++) { // 8-3+1=6
                if (grid[row][col] === grid[row][col + 1] &&
                    grid[row][col + 1] === grid[row][col + 2]) {
                    // 优先修复中间位置
                    positions.push({
                        row: row,
                        col: col + 1,
                        originalLetter: grid[row][col + 1]
                    });
                }
            }
        }

        // 检查垂直方向的连续字母
        for (let row = 0; row < 7; row++) { // 9-3+1=7
            for (let col = 0; col < 8; col++) {
                if (grid[row][col] === grid[row + 1][col] &&
                    grid[row + 1][col] === grid[row + 2][col]) {
                    // 优先修复中间位置
                    positions.push({
                        row: row + 1,
                        col: col,
                        originalLetter: grid[row + 1][col]
                    });
                }
            }
        }

        // 检查对角线方向的连续字母（右下）
        for (let row = 0; row < 7; row++) {
            for (let col = 0; col < 6; col++) {
                if (grid[row][col] === grid[row + 1][col + 1] &&
                    grid[row + 1][col + 1] === grid[row + 2][col + 2]) {
                    positions.push({
                        row: row + 1,
                        col: col + 1,
                        originalLetter: grid[row + 1][col + 1]
                    });
                }
            }
        }

        // 检查对角线方向的连续字母（左下）
        for (let row = 0; row < 7; row++) {
            for (let col = 2; col < 8; col++) {
                if (grid[row][col] === grid[row + 1][col - 1] &&
                    grid[row + 1][col - 1] === grid[row + 2][col - 2]) {
                    positions.push({
                        row: row + 1,
                        col: col - 1,
                        originalLetter: grid[row + 1][col - 1]
                    });
                }
            }
        }

        return positions;
    }

    /**
     * 检查是否是连续字母的中间位置
     * @param grid 网格
     * @param row 行
     * @param col 列
     * @returns 是否是中间位置
     */
    private static _isMiddleOfConsecutive(grid: string[][], row: number, col: number): boolean {
        const letter = grid[row][col];
        
        // 检查水平方向
        if (col >= 1 && col < 7 && grid[row][col - 1] === letter && grid[row][col + 1] === letter) {
            return true;
        }
        
        // 检查垂直方向
        if (row >= 1 && row < 8 && grid[row - 1][col] === letter && grid[row + 1][col] === letter) {
            return true;
        }
        
        return false;
    }

    /**
     * 修复2x2区域问题
     * @param grid 网格
     */
    private static _fix2x2Areas(grid: string[][]): void {
        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 7; col++) {
                const letter = grid[row][col];
                if (letter === grid[row][col + 1] && 
                    letter === grid[row + 1][col] && 
                    letter === grid[row + 1][col + 1]) {
                    
                    // 替换右下角的字母
                    for (const newLetter of letters) {
                        if (this._isLetterSafeForPosition(grid, row + 1, col + 1, newLetter)) {
                            grid[row + 1][col + 1] = newLetter;
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 替换过度使用的字母
     * @param grid 网格
     * @param overusedLetters 过度使用的字母
     * @param letterCount 字母计数
     */
    private static _replaceOverusedLetters(grid: string[][], overusedLetters: string[], letterCount: Map<string, number>): void {
        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        
        for (const overusedLetter of overusedLetters) {
            let replacements = 0;
            const maxReplacements = Math.floor((letterCount.get(overusedLetter) || 0) * 0.3);
            
            for (let row = 0; row < 9 && replacements < maxReplacements; row++) {
                for (let col = 0; col < 8 && replacements < maxReplacements; col++) {
                    if (grid[row][col] === overusedLetter) {
                        for (const newLetter of letters) {
                            if (this._isLetterSafeForPosition(grid, row, col, newLetter)) {
                                grid[row][col] = newLetter;
                                replacements++;
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 根据设备性能等级获取优化配置
     * @param performanceLevel 性能等级
     * @returns 优化配置
     */
    public static getPerformanceConfig(performanceLevel: 'high' | 'medium' | 'low' = 'medium'): DistributionOptimizationConfig {
        switch (performanceLevel) {
            case 'high':
                return PERFORMANCE_ADAPTATION_CONFIG.LETTER_DISTRIBUTION.HIGH_END;
            case 'medium':
                return PERFORMANCE_ADAPTATION_CONFIG.LETTER_DISTRIBUTION.MEDIUM_END;
            case 'low':
                return PERFORMANCE_ADAPTATION_CONFIG.LETTER_DISTRIBUTION.LOW_END;
            default:
                return LETTER_DISTRIBUTION_CONFIG.DEFAULT;
        }
    }
}

/**
 * 字母频率控制器
 * 用于管理字母的使用频率和平衡
 */
class LetterFrequencyController {
    private letterCount: Record<string, number>;
    private readonly maxFrequency: number;

    constructor(existingLetters: Record<string, number>) {
        this.letterCount = { ...existingLetters };
        this.maxFrequency = 4; // 每个字母最多出现4次
    }

    /**
     * 获取可用的字母列表
     * @returns 可用字母数组
     */
    getAvailableLetters(): string[] {
        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const available: string[] = [];

        for (const letter of letters) {
            const count = this.letterCount[letter] || 0;
            if (count < this.maxFrequency) {
                available.push(letter);
            }
        }

        // 如果没有可用字母，返回使用最少的字母
        if (available.length === 0) {
            let minCount = Infinity;
            let minLetter = 'A';
            for (const letter of letters) {
                const count = this.letterCount[letter] || 0;
                if (count < minCount) {
                    minCount = count;
                    minLetter = letter;
                }
            }
            available.push(minLetter);
        }

        return available;
    }

    /**
     * 使用字母
     * @param letter 字母
     */
    useLetter(letter: string): void {
        this.letterCount[letter] = (this.letterCount[letter] || 0) + 1;
    }
}
