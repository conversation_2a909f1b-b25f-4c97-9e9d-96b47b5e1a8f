import { Logger } from '../../Utils/Logger';
import { TranslationCache } from '../../Utils/TranslationCache';

/**
 * 本地翻译词典
 * 包含当前游戏第1关的8个单词的中文翻译
 */
const LOCAL_TRANSLATION_DICT: Record<string, string> = {
    'apple': '苹果',
    'house': '房子',
    'water': '水',
    'music': '音乐',
    'happy': '快乐',
    'light': '光',
    'world': '世界',
    'peace': '和平'
};

/**
 * 单词翻译服务
 *
 * 负责单词翻译功能的业务逻辑处理，使用本地词典提供翻译服务。
 * 当前支持游戏第1关的8个单词翻译。
 *
 * 核心功能：
 * - 本地词典翻译
 * - 缓存优先策略
 * - 翻译覆盖率检查
 * - 统计信息管理
 *
 * <AUTHOR>
 * @version 2.0 (简化版)
 * @since 2025-08-04
 */
export class WordTranslationService {
    
    /**
     * 单例实例
     */
    private static _instance: WordTranslationService | null = null;
    
    /**
     * 服务是否可用
     */
    private _isAvailable: boolean = true;
    
    /**
     * 翻译队列
     */
    private _translationQueue: string[] = [];
    
    /**
     * 是否正在处理队列
     */
    private _isProcessingQueue: boolean = false;
    
    /**
     * 统计信息
     */
    private _statistics = {
        totalRequests: 0,
        cacheHits: 0,
        localDictHits: 0,
        failures: 0
    };

    /**
     * 获取单例实例
     */
    public static getInstance(): WordTranslationService {
        if (!WordTranslationService._instance) {
            WordTranslationService._instance = new WordTranslationService();
        }
        return WordTranslationService._instance;
    }

    /**
     * 私有构造函数
     */
    private constructor() {
        this._initializeService();
    }

    /**
     * 初始化服务
     */
    private _initializeService(): void {
        try {
            // 初始化缓存系统
            TranslationCache.initialize();

            this._isAvailable = true;

            // 精简日志：只显示单词数量
            const supportedWords = Object.keys(LOCAL_TRANSLATION_DICT);
            Logger.info('WordTranslationService', `翻译服务初始化完成，支持${supportedWords.length}个单词`);
        } catch (error) {
            this._isAvailable = false;
            Logger.error('WordTranslationService', '翻译服务初始化失败', error as Error);
        }
    }

    /**
     * 获取单词翻译（主要接口）
     * @param word 要翻译的单词
     * @returns 翻译结果，如果失败返回空字符串
     */
    public async getTranslation(word: string): Promise<string> {
        try {
            // 强化参数验证
            if (word === null || word === undefined) {
                Logger.warn('WordTranslationService', '单词参数为null或undefined');
                return '';
            }

            if (typeof word !== 'string') {
                Logger.warn('WordTranslationService', `单词参数类型错误: ${typeof word}, 值: ${word}`);
                return '';
            }

            if (word.trim().length === 0) {
                Logger.warn('WordTranslationService', '单词参数为空字符串');
                return '';
            }

            const cleanWord = word.trim().toLowerCase();
            this._statistics.totalRequests++;

            // 检查服务可用性
            if (!this._isAvailable) {
                Logger.warn('WordTranslationService', '翻译服务不可用');
                return '';
            }

            // 1. 优先从缓存获取
            const cached = TranslationCache.get(cleanWord);
            if (cached) {
                this._statistics.cacheHits++;
                return cached.translation;
            }

            // 2. 检查本地词典
            const localTranslation = LOCAL_TRANSLATION_DICT[cleanWord];
            if (localTranslation) {
                this._statistics.localDictHits++;
                // 缓存本地翻译结果
                TranslationCache.set(cleanWord, localTranslation);
                return localTranslation;
            }

            // 3. 本地词典中没有找到翻译
            return '';

        } catch (error) {
            this._statistics.failures++;
            Logger.error('WordTranslationService', `获取翻译异常: ${word}`, error as Error);
            return '';
        }
    }

    /**
     * 批量预加载翻译
     * @param words 单词数组
     * @returns 预加载是否成功
     */
    public async preloadTranslations(words: string[]): Promise<boolean> {
        if (!this._isAvailable) {
            Logger.warn('WordTranslationService', '翻译服务不可用，无法预加载');
            return false;
        }

        if (!Array.isArray(words) || words.length === 0) {
            Logger.warn('WordTranslationService', '预加载单词列表为空');
            return false;
        }

        try {
            Logger.info('WordTranslationService', `开始预加载翻译: ${words.length}个单词`);
            
            let successCount = 0;
            let failureCount = 0;

            // 过滤出需要翻译的单词（未缓存的）
            const wordsToTranslate = words.filter(word => {
                const cleanWord = word.trim().toLowerCase();
                return !TranslationCache.get(cleanWord);
            });

            Logger.info('WordTranslationService', `需要翻译的单词: ${wordsToTranslate.length}个`);

            // 批量翻译（本地词典，无需延迟）
            for (const word of wordsToTranslate) {
                try {
                    const translation = await this.getTranslation(word);
                    if (translation) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                } catch (error) {
                    failureCount++;
                    Logger.warn('WordTranslationService', `预加载翻译失败: ${word}`, error as Error);
                }
            }

            Logger.info('WordTranslationService', `预加载完成: 成功${successCount}个, 失败${failureCount}个`);
            return successCount > 0;

        } catch (error) {
            Logger.error('WordTranslationService', '批量预加载翻译失败', error as Error);
            return false;
        }
    }

    /**
     * 检查服务是否可用
     */
    public isAvailable(): boolean {
        return this._isAvailable;
    }

    /**
     * 获取服务统计信息
     */
    public getStatistics(): {
        totalRequests: number;
        cacheHits: number;
        localDictHits: number;
        failures: number;
        cacheHitRate: number;
        localDictHitRate: number;
    } {
        const cacheHitRate = this._statistics.totalRequests > 0
            ? (this._statistics.cacheHits / this._statistics.totalRequests) * 100
            : 0;

        const localDictHitRate = this._statistics.totalRequests > 0
            ? (this._statistics.localDictHits / this._statistics.totalRequests) * 100
            : 0;

        return {
            ...this._statistics,
            cacheHitRate: Math.round(cacheHitRate * 100) / 100,
            localDictHitRate: Math.round(localDictHitRate * 100) / 100
        };
    }

    /**
     * 获取缓存状态
     */
    public getCacheStatus(): {
        size: number;
        maxSize: number;
        hitRate: number;
    } {
        return TranslationCache.getStats();
    }

    /**
     * 清空缓存
     */
    public clearCache(): void {
        TranslationCache.clear();
        Logger.info('WordTranslationService', '翻译缓存已清空');
    }

    /**
     * 重置统计信息
     */
    public resetStatistics(): void {
        this._statistics = {
            totalRequests: 0,
            cacheHits: 0,
            localDictHits: 0,
            failures: 0
        };
        Logger.info('WordTranslationService', '统计信息已重置');
    }

    /**
     * 延迟函数
     */
    private _delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取本地词典统计信息
     */
    public getLocalDictStats(): {
        totalWords: number;
        availableWords: string[];
    } {
        const words = Object.keys(LOCAL_TRANSLATION_DICT);
        return {
            totalWords: words.length,
            availableWords: words // 返回所有8个单词
        };
    }

    /**
     * 检查单词是否在本地词典中
     * @param word 要检查的单词
     * @returns 是否存在翻译
     */
    public hasLocalTranslation(word: string): boolean {
        if (!word || typeof word !== 'string') {
            return false;
        }
        const cleanWord = word.trim().toLowerCase();
        return cleanWord in LOCAL_TRANSLATION_DICT;
    }

    /**
     * 批量检查单词的翻译覆盖率
     * @param words 单词列表
     * @returns 覆盖率统计
     */
    public checkTranslationCoverage(words: string[]): {
        total: number;
        covered: number;
        coverageRate: number;
        coveredWords: string[];
        uncoveredWords: string[];
    } {
        if (!Array.isArray(words)) {
            return {
                total: 0,
                covered: 0,
                coverageRate: 0,
                coveredWords: [],
                uncoveredWords: []
            };
        }

        const coveredWords: string[] = [];
        const uncoveredWords: string[] = [];

        for (const word of words) {
            if (this.hasLocalTranslation(word)) {
                coveredWords.push(word);
            } else {
                uncoveredWords.push(word);
            }
        }

        const coverageRate = words.length > 0 ? (coveredWords.length / words.length) * 100 : 0;

        return {
            total: words.length,
            covered: coveredWords.length,
            coverageRate: Math.round(coverageRate * 100) / 100,
            coveredWords,
            uncoveredWords
        };
    }
}
