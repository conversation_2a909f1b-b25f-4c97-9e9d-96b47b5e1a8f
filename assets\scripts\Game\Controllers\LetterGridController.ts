import { _decorator, Component, Node, Label, EventTouch, UITransform, v3, Sprite, Color } from 'cc';
import { ILetterData } from '../../Data/WordDatabase';
import { LetterEventHandler, LetterSelectionCallback } from '../../Game/Components/LetterEventHandler';
import { LetterPosition } from '../../Game/Constants/GameConstants';
import { GridDataManager } from '../../Game/Managers/GridDataManager';
import { GameUtils } from '../../Utils/GameUtils';
import { Logger } from '../../Utils/Logger';

const { ccclass, property } = _decorator;

/**
 * 字母网格控制器
 *
 * 负责管理9行8列字母网格的显示、交互和状态控制，是字母连接游戏的
 * 核心显示组件。提供字母网格的完整生命周期管理和交互接口。
 *
 * 核心功能：
 * - 字母网格显示：管理9×8=72个字母的显示和布局
 * - 字母数据绑定：将字母数据绑定到UI节点进行显示
 * - 触摸事件处理：处理字母的触摸选择和连接交互
 * - 网格状态管理：跟踪每个字母的选择状态和视觉状态
 * - 坐标转换：提供网格坐标和UI坐标的转换功能
 *
 * 网格结构：
 * - 网格大小：9行 × 8列 = 72个字母位置
 * - 层次结构：Row节点 → Cell节点 → Letter节点
 * - 组件配置：每个Letter节点包含Label组件显示字母
 * - 事件绑定：每个Cell节点绑定触摸事件处理
 *
 * 交互机制：
 * - 触摸检测：检测用户对字母的触摸操作
 * - 选择反馈：提供字母选择的视觉和触觉反馈
 * - 连接支持：支持字母间的连续选择和路径构建
 * - 状态同步：与其他组件同步字母的选择状态
 *
 * 技术特性：
 * - 高效的节点查找和缓存机制
 * - 灵活的事件委托和回调系统
 * - 完善的错误处理和边界检查
 * - 性能优化的渲染和更新策略
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-05
 */
@ccclass('LetterGridController')
export class LetterGridController extends Component {

    /**
     * 行节点引用数组 - 需要在编辑器中按顺序拖拽赋值
     * 顺序：第1行, 第2行, ..., 第9行
     * 每行包含8个单元格，每个单元格包含一个字母节点
     */
    @property([Node])
    rowNodes: Node[] = [];

    /**
     * 网格数据管理器
     */
    private _gridDataManager: GridDataManager = new GridDataManager();

    /**
     * 字母Label组件缓存 (9行8列)
     */
    private _labelGrid: Label[][] = [];

    /**
     * 是否已初始化
     */
    private _isInitialized: boolean = false;

    /**
     * 事件处理器引用
     */
    private _eventHandler: LetterEventHandler | null = null;

    /**
     * 组件生命周期 - 初始化
     */
    onLoad() {
        this._initializeGrid();
        this._gridDataManager.initialize();
    }

    /**
     * 组件生命周期 - 启动
     */
    start() {
        // 启动完成，无需日志
    }

    /**
     * 初始化9行8列字母网格
     */
    private _initializeGrid(): void {
        if (!this.rowNodes || this.rowNodes.length === 0) {
            Logger.error('LetterGridController', '行节点数组为空，请在编辑器中配置');
            return;
        }

        if (this.rowNodes.length !== GridDataManager.GRID_ROWS) {
            Logger.error('LetterGridController', `行节点数量不正确，期望${GridDataManager.GRID_ROWS}个，实际${this.rowNodes.length}个`);
            return;
        }

        // 初始化Label缓存
        this._labelGrid = [];

        // 遍历每一行
        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            const rowNode = this.rowNodes[row];
            if (!rowNode) {
                Logger.error('LetterGridController', `第${row + 1}行节点为空`);
                continue;
            }

            // 初始化当前行的Label数组
            this._labelGrid[row] = [];

            // 获取当前行的所有单元格节点
            const cellNodes = rowNode.children;
            if (cellNodes.length !== GridDataManager.GRID_COLS) {
                Logger.warn('LetterGridController', `第${row + 1}行单元格数量不正确，期望${GridDataManager.GRID_COLS}个，实际${cellNodes.length}个`);
            }

            // 遍历当前行的每个单元格
            for (let col = 0; col < GridDataManager.GRID_COLS && col < cellNodes.length; col++) {
                const cellNode = cellNodes[col];
                if (!cellNode) {
                    Logger.error('LetterGridController', `第${row + 1}行第${col + 1}列单元格节点为空`);
                    continue;
                }

                // 查找字母子节点
                let letterNode = cellNode.getChildByName('字母');
                if (!letterNode) {
                    Logger.warn('LetterGridController', `第${row + 1}行第${col + 1}列中未找到"字母"子节点，将自动创建`);
                    letterNode = this._createLetterNode(cellNode);
                }

                // 获取或创建Label组件
                let label = letterNode.getComponent(Label);
                if (!label) {
                    Logger.warn('LetterGridController', `第${row + 1}行第${col + 1}列中未找到Label组件，将自动创建`);
                    label = letterNode.addComponent(Label);
                    if (label) {
                        // 设置Label属性
                        label.fontSize = 50;
                        label.horizontalAlign = Label.HorizontalAlign.CENTER;
                        label.verticalAlign = Label.VerticalAlign.CENTER;
                        label.string = '';
                    }
                }

                if (label) {
                    this._labelGrid[row][col] = label;
                } else {
                    Logger.error('LetterGridController', `第${row + 1}行第${col + 1}列 Label组件获取失败`);
                }
            }
        }

        // 验证初始化结果
        let totalLabels = 0;
        for (let row = 0; row < this._labelGrid.length; row++) {
            totalLabels += this._labelGrid[row].length;
        }

        if (totalLabels === GridDataManager.GRID_ROWS * GridDataManager.GRID_COLS) {
            this._isInitialized = true;
        } else {
            Logger.error('LetterGridController', `网格初始化失败，Label组件数量不正确：${totalLabels}/${GridDataManager.GRID_ROWS * GridDataManager.GRID_COLS}`);
        }
    }

    /**
     * 在单元格节点中创建字母节点
     * @param cellNode 单元格节点
     * @returns 新创建的字母节点
     */
    private _createLetterNode(cellNode: Node): Node {
        const letterNode = new Node('字母');

        // 添加Label组件
        const label = letterNode.addComponent(Label);
        if (label) {
            // 设置Label属性
            label.fontSize = 50;
            label.horizontalAlign = Label.HorizontalAlign.CENTER;
            label.verticalAlign = Label.VerticalAlign.CENTER;
            label.string = '';

        }

        // 将字母节点添加到单元格中
        cellNode.addChild(letterNode);

        return letterNode;
    }

    /**
     * 显示字母网格到界面
     * @param letterGrid 字母网格数据 (9行8列)
     */
    public displayLetterGrid(letterGrid: string[][]): void {
        if (!this._isInitialized || !this._gridDataManager.isInitialized()) {
            Logger.error('LetterGridController', '控制器未初始化，无法显示字母');
            return;
        }

        // 使用数据管理器设置字母数据
        if (!this._gridDataManager.setLetterGridData(letterGrid)) {
            Logger.error('LetterGridController', '字母网格数据设置失败');
            return;
        }

        // 显示字母到界面（无闪烁）
        this._displayLettersToGrid();
    }





    /**
     * 将字母智能分布到9行8列网格中，避免相邻重复
     * @param letters 字母数组
     */
    private _distributeLettersToGrid(letters: string[]): void {
        const totalCells = GridDataManager.GRID_ROWS * GridDataManager.GRID_COLS;

        // 清空网格
        this._gridDataManager.clearGrid();

        // 确保有足够的字母填满整个网格
        const finalLetters = this._prepareLettersForGrid(letters, totalCells);

        // 使用智能分布算法避免相邻重复
        const success = this._smartDistributeLetters(finalLetters);

        if (!success) {
            Logger.warn('LetterGridController', '智能分布失败，使用备用方案');
            this._fallbackDistributeLetters(finalLetters);
        }
    }



    /**
     * 准备字母数组以填满网格
     * @param letters 原始字母数组
     * @param targetCount 目标数量
     * @returns 准备好的字母数组
     */
    private _prepareLettersForGrid(letters: string[], targetCount: number): string[] {
        let finalLetters = [...letters];

        // 如果字母数量不够，智能补充
        while (finalLetters.length < targetCount) {
            const additionalLetters = letters.slice(0, Math.min(letters.length, targetCount - finalLetters.length));
            finalLetters.push(...additionalLetters);
        }

        // 如果字母数量超过需要的数量，截取到正确长度
        if (finalLetters.length > targetCount) {
            finalLetters = finalLetters.slice(0, targetCount);
        }

        return finalLetters;
    }

    /**
     * 智能分布字母，避免相邻重复
     * @param letters 字母数组
     * @returns 是否成功分布
     */
    private _smartDistributeLetters(letters: string[]): boolean {
        const maxAttempts = 100;
        let attempts = 0;

        while (attempts < maxAttempts) {
            attempts++;

            // 随机打乱字母
            const shuffledLetters = GameUtils.shuffleArray([...letters]);

            // 尝试放置字母
            if (this._tryPlaceLetters(shuffledLetters)) {
                return true;
            }

            // 清空网格准备下次尝试
            this._gridDataManager.clearGrid();
        }

        Logger.warn('LetterGridController', `智能分布失败，已尝试${maxAttempts}次`);
        return false;
    }

    /**
     * 尝试放置字母，检查相邻重复
     * @param letters 字母数组
     * @returns 是否成功放置
     */
    private _tryPlaceLetters(letters: string[]): boolean {
        let letterIndex = 0;

        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                if (letterIndex >= letters.length) {
                    return false;
                }

                const letter = letters[letterIndex];

                // 检查是否与相邻位置重复
                if (this._hasAdjacentSameLetter(row, col, letter)) {
                    // 尝试找到不重复的字母
                    const swapIndex = this._findNonAdjacentLetter(letters, letterIndex, row, col);
                    if (swapIndex === -1) {
                        return false; // 无法找到合适的字母
                    }

                    // 交换字母位置
                    [letters[letterIndex], letters[swapIndex]] = [letters[swapIndex], letters[letterIndex]];
                }

                // 放置字母
                if (this._gridDataManager.setLetter(row, col, letters[letterIndex])) {
                    letterIndex++;
                }
            }
        }

        return true;
    }

    /**
     * 检查指定位置是否与相邻位置有相同字母
     * @param row 行索引
     * @param col 列索引
     * @param letter 要检查的字母
     * @returns 是否有相邻相同字母
     */
    private _hasAdjacentSameLetter(row: number, col: number, letter: string): boolean {
        const directions = [
            [-1, -1], [-1, 0], [-1, 1],  // 上方三个位置
            [0, -1],           [0, 1],   // 左右两个位置
            [1, -1],  [1, 0],  [1, 1]    // 下方三个位置
        ];

        for (const [dr, dc] of directions) {
            const newRow = row + dr;
            const newCol = col + dc;

            if (newRow >= 0 && newRow < GridDataManager.GRID_ROWS &&
                newCol >= 0 && newCol < GridDataManager.GRID_COLS) {

                const existingLetter = this._gridDataManager.getLetter(newRow, newCol);
                if (existingLetter === letter) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 查找不与指定位置相邻重复的字母
     * @param letters 字母数组
     * @param startIndex 开始搜索的索引
     * @param row 目标行
     * @param col 目标列
     * @returns 找到的字母索引，-1表示未找到
     */
    private _findNonAdjacentLetter(letters: string[], startIndex: number, row: number, col: number): number {
        for (let i = startIndex + 1; i < letters.length; i++) {
            if (!this._hasAdjacentSameLetter(row, col, letters[i])) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 备用分布方案（简单随机分布）
     * @param letters 字母数组
     */
    private _fallbackDistributeLetters(letters: string[]): void {
        const shuffledLetters = GameUtils.shuffleArray([...letters]);
        let letterIndex = 0;

        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                if (letterIndex < shuffledLetters.length) {
                    this._gridDataManager.setLetter(row, col, shuffledLetters[letterIndex]);
                    letterIndex++;
                }
            }
        }
    }

    /**
     * 获取网格中的所有字母
     * @returns 字母数组
     */
    private _getAllLettersFromGrid(): string[] {
        return this._gridDataManager.getLettersArray();
    }

    /**
     * 将字母显示到网格界面（无闪烁版本）
     */
    private _displayLettersToGrid(): void {
        // 使用scheduleOnce确保在下一帧执行，避免初始化时序问题
        this.scheduleOnce(() => {
            this._performLetterDisplay();
        }, 0);
    }

    /**
     * 执行字母显示（立即显示，无闪烁）
     */
    private _performLetterDisplay(): void {
        let displayedCount = 0;

        // 一次性显示所有字母，避免分批造成的闪烁
        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                if (this._labelGrid[row] && this._labelGrid[row][col]) {
                    const letterData = this._gridDataManager.getLetterData(row, col);
                    const label = this._labelGrid[row][col];

                    if (letterData && label) {
                        // 确保节点处于激活状态
                        if (label.node) {
                            label.node.active = true;
                        }

                        // 立即设置字母文本和样式
                        label.string = letterData.letter || '';
                        label.fontSize = 50;

                        if (letterData.letter) {
                            displayedCount++;
                        }
                    }
                }
            }
        }

        this._onLetterDisplayComplete();
    }

    /**
     * 字母显示完成回调
     */
    private _onLetterDisplayComplete(): void {
        // 标记显示完成，可以在这里添加后续逻辑
        // 例如：启用字母点击交互、播放完成音效等
    }

    /**
     * 获取指定位置的字母
     * @param row 行索引（0-8）
     * @param col 列索引（0-7）
     * @returns 字母字符，如果位置无效则返回null
     */
    public getLetterAt(row: number, col: number): string | null {
        const letter = this._gridDataManager.getLetter(row, col);
        return letter || null;
    }

    /**
     * 根据本地坐标获取字母位置
     * @param localX 本地X坐标
     * @param localY 本地Y坐标
     * @returns 字母位置或null
     */
    public getLetterPositionFromLocalCoord(localX: number, localY: number): LetterPosition | null {
        // 遍历所有字母单元格，找到包含该坐标的单元格
        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            const rowNode = this.node.children[row];
            if (!rowNode) continue;

            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                const cellNode = rowNode.children[col];
                if (!cellNode) continue;

                // 获取单元格的UITransform
                const cellTransform = cellNode.getComponent(UITransform);
                if (!cellTransform) continue;

                // 获取单元格的世界坐标边界
                const cellWorldPos = cellNode.worldPosition;
                const cellSize = cellTransform.contentSize;

                // 将单元格世界坐标转换为网格节点的本地坐标
                const gridTransform = this.node.getComponent(UITransform);
                if (!gridTransform) continue;

                const cellLocalPos = gridTransform.convertToNodeSpaceAR(v3(cellWorldPos.x, cellWorldPos.y, 0));

                // 检查触摸点是否在单元格范围内
                const halfWidth = cellSize.width / 2;
                const halfHeight = cellSize.height / 2;

                if (localX >= cellLocalPos.x - halfWidth && localX <= cellLocalPos.x + halfWidth &&
                    localY >= cellLocalPos.y - halfHeight && localY <= cellLocalPos.y + halfHeight) {

                    return { row, col };
                }
            }
        }
        return null;
    }

    /**
     * 设置指定位置字母的选中状态
     * @param row 行索引（0-8）
     * @param col 列索引（0-7）
     * @param selected 是否选中
     */
    public setLetterSelected(row: number, col: number, selected: boolean): void {
        this._gridDataManager.setLetterSelected(row, col, selected);
    }

    /**
     * 获取网格尺寸信息
     * @returns 网格尺寸对象
     */
    public getGridSize(): { rows: number, cols: number } {
        const size = this._gridDataManager.getGridSize();
        return {
            rows: size.rows,
            cols: size.cols
        };
    }

    /**
     * 直接设置字母数据（用于预生成模式，无闪烁显示）
     * @param letters 字母数组（应该正好72个）
     */
    public setLettersDirectly(letters: string[]): void {
        if (!this._isInitialized || !this._gridDataManager.isInitialized()) {
            Logger.error('LetterGridController', '组件未初始化，无法设置字母');
            return;
        }

        // 使用数据管理器设置字母数据
        if (!this._gridDataManager.setLettersDirectly(letters)) {
            Logger.error('LetterGridController', '字母数据设置失败');
            return;
        }

        // 立即显示字母，无闪烁
        this._performLetterDisplay();
    }

    /**
     * 设置事件处理器
     * @param eventHandler 事件处理器实例
     */
    public setEventHandler(eventHandler: LetterEventHandler): void {
        this._eventHandler = eventHandler;
        this._setupLetterEventListeners();
    }



    /**
     * 获取指定位置的单元格节点
     * @param row 行索引
     * @param col 列索引
     * @returns 单元格节点或null
     */
    private _getCellNode(row: number, col: number): Node | null {
        if (!this._isValidGridPosition(row, col)) {
            return null;
        }

        const rowNode = this.rowNodes[row];
        if (!rowNode || col >= rowNode.children.length) {
            return null;
        }

        return rowNode.children[col];
    }

    /**
     * 验证网格位置是否有效
     * @param row 行索引
     * @param col 列索引
     * @returns 是否有效
     */
    private _isValidGridPosition(row: number, col: number): boolean {
        return row >= 0 && row < GridDataManager.GRID_ROWS &&
               col >= 0 && col < GridDataManager.GRID_COLS;
    }

    /**
     * 设置字母选择回调函数（通过事件处理器）
     * @param callback 回调函数
     */
    public setLetterSelectionCallback(callback: LetterSelectionCallback): void {
        if (this._eventHandler) {
            this._eventHandler.setLetterSelectionCallback(callback);
        } else {
            Logger.warn('LetterGridController', '事件处理器未设置，无法设置回调函数');
        }
    }

    /**
     * 为所有字母单元格设置事件监听器
     */
    private _setupLetterEventListeners(): void {
        if (!this._isInitialized) {
            Logger.warn('LetterGridController', '网格未初始化，跳过事件监听器设置');
            return;
        }

        let listenerCount = 0;
        let missingCells = [];

        // 遍历9行8列的字母网格
        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                const cellNode = this._getCellNode(row, col);
                if (cellNode) {
                    // 使用立即执行函数表达式(IIFE)来正确捕获row和col的值
                    ((currentRow: number, currentCol: number) => {
                        // 为单元格节点添加触摸事件监听器
                        cellNode.on(Node.EventType.TOUCH_START, (event: EventTouch) => {
                            this._onLetterCellTouched(currentRow, currentCol, event);
                        }, this);

                        cellNode.on(Node.EventType.TOUCH_MOVE, (event: EventTouch) => {
                            this._onLetterCellTouchMove(currentRow, currentCol, event);
                        }, this);

                        cellNode.on(Node.EventType.TOUCH_END, (event: EventTouch) => {
                            this._onLetterCellTouchEnd(currentRow, currentCol, event);
                        }, this);

                        cellNode.on(Node.EventType.TOUCH_CANCEL, (event: EventTouch) => {
                            this._onLetterCellTouchEnd(currentRow, currentCol, event);
                        }, this);
                    })(row, col);

                    listenerCount++;
                } else {
                    missingCells.push(`(${row},${col})`);
                }
            }
        }

        // 检查设置结果
        if (missingCells.length > 0) {
            Logger.warn('LetterGridController', `缺失单元格: ${missingCells.join(', ')}`);
        }

        if (listenerCount < 72) {
            Logger.warn('LetterGridController', `只设置了 ${listenerCount}/72 个事件监听器`);
        }
    }

    /**
     * 获取指定位置的单元格节点
     * @param row 行索引
     * @param col 列索引
     * @returns 单元格节点或null
     */
    private _getCellNode(row: number, col: number): Node | null {
        if (row < 0 || row >= GridDataManager.GRID_ROWS ||
            col < 0 || col >= GridDataManager.GRID_COLS) {
            return null;
        }

        // 检查 rowNodes 是否存在且有效
        if (!this.rowNodes || !Array.isArray(this.rowNodes) || this.rowNodes.length <= row) {
            return null;
        }

        const rowNode = this.rowNodes[row];
        if (!rowNode || !rowNode.children || rowNode.children.length <= col) {
            return null;
        }

        return rowNode.children[col];
    }

    /**
     * 公共方法：获取指定位置的单元格节点
     * @param row 行索引
     * @param col 列索引
     * @returns 单元格节点或null
     */
    public getCellNode(row: number, col: number): Node | null {
        return this._getCellNode(row, col);
    }

    /**
     * 字母单元格触摸开始事件处理
     * @param row 行索引
     * @param col 列索引
     * @param event 触摸事件
     */
    private _onLetterCellTouched(row: number, col: number, event: EventTouch): void {
        if (this._eventHandler) {
            this._eventHandler.handleLetterSelection(row, col, event);
        }
    }

    /**
     * 字母单元格触摸移动事件处理
     * @param row 行索引
     * @param col 列索引
     * @param event 触摸事件
     */
    private _onLetterCellTouchMove(row: number, col: number, event: EventTouch): void {
        if (this._eventHandler) {
            this._eventHandler.handleLetterSelection(row, col, event);
        }
    }

    /**
     * 字母单元格触摸结束事件处理
     * @param row 行索引
     * @param col 列索引
     * @param event 触摸事件
     */
    private _onLetterCellTouchEnd(row: number, col: number, event: EventTouch): void {
        if (this._eventHandler) {
            this._eventHandler.handleLetterSelection(row, col, event);
        }
    }

    /**
     * 移除所有事件监听器（组件销毁时调用）
     */
    onDestroy(): void {
        // 检查 rowNodes 是否存在且有效
        if (!this.rowNodes || !Array.isArray(this.rowNodes)) {
            return;
        }

        // 移除所有事件监听器
        for (let row = 0; row < GridDataManager.GRID_ROWS; row++) {
            for (let col = 0; col < GridDataManager.GRID_COLS; col++) {
                const cellNode = this._getCellNode(row, col);
                if (cellNode && cellNode.isValid) {
                    cellNode.off(Node.EventType.TOUCH_START);
                    cellNode.off(Node.EventType.TOUCH_MOVE);
                    cellNode.off(Node.EventType.TOUCH_END);
                    cellNode.off(Node.EventType.TOUCH_CANCEL);
                }
            }
        }


    }
}
