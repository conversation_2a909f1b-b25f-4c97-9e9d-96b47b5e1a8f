import { _decorator, Component, Node, tween, Color, Vec3, Tween, Label } from 'cc';
import { Logger } from '../../Utils/Logger';

const { ccclass } = _decorator;

/**
 * 动画类型枚举
 */
export enum AnimationType {
    COLOR = 'color',
    SCALE = 'scale',
    POSITION = 'position',
    OPACITY = 'opacity',
    ROTATION = 'rotation',
    LINE_DRAW = 'line_draw',
    LINE_PULSE = 'line_pulse',
    LETTER_GLOW = 'letter_glow',
    CONNECTION_FEEDBACK = 'connection_feedback'
}

/**
 * 缓动函数类型
 */
export type EasingType = 'linear' | 'sineIn' | 'sineOut' | 'sineInOut' | 
                        'quadIn' | 'quadOut' | 'quadInOut' | 'cubicIn' | 'cubicOut' | 'cubicInOut' |
                        'quartIn' | 'quartOut' | 'quartInOut' | 'quintIn' | 'quintOut' | 'quintInOut' |
                        'expoIn' | 'expoOut' | 'expoInOut' | 'circIn' | 'circOut' | 'circInOut' |
                        'elasticIn' | 'elasticOut' | 'elasticInOut' | 'backIn' | 'backOut' | 'backInOut' |
                        'bounceIn' | 'bounceOut' | 'bounceInOut';

/**
 * 动画配置接口
 */
export interface AnimationConfig {
    /** 动画持续时间（秒） */
    duration: number;
    /** 缓动函数 */
    easing?: EasingType;
    /** 延迟时间（秒） */
    delay?: number;
    /** 重复次数（-1为无限循环） */
    repeat?: number;
    /** 是否往返播放 */
    yoyo?: boolean;
    /** 完成回调 */
    onComplete?: () => void;
    /** 更新回调 */
    onUpdate?: () => void;
    /** 开始回调 */
    onStart?: () => void;
}

/**
 * 颜色动画参数
 */
export interface ColorAnimationParams {
    target: Node | Label | Sprite;
    targetColor: Color;
    config: AnimationConfig;
}

/**
 * 缩放动画参数
 */
export interface ScaleAnimationParams {
    target: Node;
    targetScale: Vec3 | number;
    config: AnimationConfig;
}

/**
 * 位移动画参数
 */
export interface PositionAnimationParams {
    target: Node;
    targetPosition: Vec3;
    config: AnimationConfig;
}

/**
 * 透明度动画参数
 */
export interface OpacityAnimationParams {
    target: Node | Label | Sprite;
    targetOpacity: number;
    config: AnimationConfig;
}

/**
 * 动画信息接口
 */
export interface AnimationInfo {
    id: string;
    type: AnimationType;
    target: Node | Label | Sprite;
    tween: Tween<Node | Label | Sprite>;
    startTime: number;
    config: AnimationConfig;
}

/**
 * 动画性能等级
 */
export enum AnimationQuality {
    LOW = 'low',        // 低质量：禁用大部分动画
    MEDIUM = 'medium',  // 中等质量：启用基础动画
    HIGH = 'high'       // 高质量：启用所有动画
}

/**
 * 动画管理器全局配置
 */
export interface AnimationManagerConfig {
    /** 是否全局启用动画 */
    enabled: boolean;
    /** 动画质量等级 */
    quality: AnimationQuality;
    /** 最大并发动画数量 */
    maxConcurrentAnimations: number;
    /** 默认动画持续时间 */
    defaultDuration: number;
    /** 默认缓动函数 */
    defaultEasing: EasingType;

}

/**
 * 统一动画管理器
 *
 * 负责管理游戏中所有动画效果的创建、播放、停止和销毁，提供标准化的动画接口。
 * 采用单例模式确保全局唯一性，支持多种动画类型和性能优化策略。
 *
 * 核心功能：
 * - 动画生命周期管理：创建、播放、暂停、停止、销毁
 * - 多种动画类型支持：缩放、颜色过渡、透明度、位移等
 * - 性能优化：动画队列管理、并发控制、资源清理
 * - 设备适配：根据设备性能调整动画质量和数量
 * - 调试支持：动画状态监控和性能统计
 *
 * 动画类型：
 * - SCALE: 缩放动画（如字母呼吸效果）
 * - COLOR: 颜色过渡动画（如单词完成状态变化）
 * - OPACITY: 透明度动画（如淡入淡出效果）
 * - POSITION: 位移动画（如元素移动效果）
 *
 * 使用示例：
 * ```typescript
 * const animationManager = AnimationManager.getInstance();
 * const animationId = animationManager.playScaleAnimation({
 *     target: letterNode,
 *     targetScale: new Vec3(1.2, 1.2, 1.0),
 *     config: { duration: 0.5, easing: 'easeInOut' }
 * });
 * ```
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-09
 */
@ccclass('AnimationManager')
export class AnimationManager extends Component {

    /**
     * 单例实例
     */
    private static _instance: AnimationManager | null = null;

    /**
     * 动画管理器全局配置
     */
    private static _globalConfig: AnimationManagerConfig = {
        enabled: true,
        quality: AnimationQuality.HIGH,
        maxConcurrentAnimations: 50,
        defaultDuration: 0.3,
        defaultEasing: 'sineOut'
    };

    /**
     * 活跃动画跟踪
     * key: 动画ID，value: 动画信息
     */
    private _activeAnimations: Map<string, AnimationInfo> = new Map();

    /**
     * 动画ID计数器
     */
    private _animationIdCounter: number = 0;

    /**
     * 组件生命周期 - 初始化
     */
    onLoad(): void {
        // 设置单例
        if (AnimationManager._instance === null) {
            AnimationManager._instance = this;
        } else {
            Logger.warn('AnimationManager', '动画管理器已存在，销毁重复实例');
            this.node.destroy();
        }
    }

    /**
     * 获取动画管理器单例实例
     *
     * 提供全局唯一的动画管理器实例，确保动画系统的一致性。
     * 包含实例有效性检查，自动清理无效的实例引用。
     *
     * @returns {AnimationManager | null} 动画管理器实例，如果未初始化则返回null
     *
     * @example
     * ```typescript
     * const animationManager = AnimationManager.getInstance();
     * if (animationManager) {
     *     animationManager.playScaleAnimation(params);
     * }
     * ```
     */
    public static getInstance(): AnimationManager | null {
        if (AnimationManager._instance &&
            (!AnimationManager._instance.node || !AnimationManager._instance.node.isValid)) {
            Logger.warn('AnimationManager', '单例实例节点无效，清理引用');
            AnimationManager._instance = null;
        }
        return AnimationManager._instance;
    }

    /**
     * 设置动画管理器全局配置
     *
     * 更新动画系统的全局配置参数，影响所有后续创建的动画。
     * 支持部分配置更新，未指定的配置项保持原有值。
     *
     * @param {Partial<AnimationManagerConfig>} config - 配置对象，支持部分配置更新
     * @param {boolean} [config.enabled] - 是否全局启用动画
     * @param {number} [config.maxConcurrentAnimations] - 最大并发动画数量
     * @param {AnimationQuality} [config.quality] - 动画质量等级

     *
     * @example
     * ```typescript
     * AnimationManager.setGlobalConfig({
     *     enabled: true,
     *     maxConcurrentAnimations: 20,
     *     quality: AnimationQuality.HIGH
     * });
     * ```
     */
    public static setGlobalConfig(config: Partial<AnimationManagerConfig>): void {
        AnimationManager._globalConfig = { ...AnimationManager._globalConfig, ...config };
    }

    /**
     * 获取全局配置
     */
    public static getGlobalConfig(): AnimationManagerConfig {
        return { ...AnimationManager._globalConfig };
    }

    /**
     * 生成唯一动画ID
     */
    private _generateAnimationId(): string {
        return `anim_${++this._animationIdCounter}_${Date.now()}`;
    }

    /**
     * 检查是否可以播放动画
     */
    private _canPlayAnimation(): boolean {
        // 检查组件是否有效
        if (!this.node || !this.node.isValid) {
            Logger.warn('AnimationManager', '动画管理器节点无效，无法播放动画');
            return false;
        }

        if (!AnimationManager._globalConfig.enabled) {
            return false;
        }

        if (this._activeAnimations.size >= AnimationManager._globalConfig.maxConcurrentAnimations) {
            Logger.warn('AnimationManager',
                `达到最大并发动画数量限制: ${AnimationManager._globalConfig.maxConcurrentAnimations}`);
            return false;
        }

        return true;
    }

    /**
     * 播放颜色过渡动画
     * @param params 动画参数
     * @returns 动画ID，用于后续控制
     */
    public playColorAnimation(params: ColorAnimationParams): string | null {
        if (!this._canPlayAnimation()) {
            return null;
        }

        const animationId = this._generateAnimationId();
        const { target, targetColor, config } = params;

        // 获取起始颜色
        const startColor = new Color(target.color || new Color(255, 255, 255, 255));
        const animationTarget = { r: startColor.r, g: startColor.g, b: startColor.b, a: startColor.a };

        // 创建Tween动画
        const colorTween = tween(animationTarget)
            .delay(config.delay || 0)
            .to(config.duration, {
                r: targetColor.r,
                g: targetColor.g,
                b: targetColor.b,
                a: targetColor.a
            }, {
                easing: config.easing || AnimationManager._globalConfig.defaultEasing,
                onStart: () => {
                    if (config.onStart) config.onStart();
                },
                onUpdate: () => {
                    // 实时更新目标颜色
                    target.color = new Color(
                        Math.round(animationTarget.r),
                        Math.round(animationTarget.g),
                        Math.round(animationTarget.b),
                        Math.round(animationTarget.a)
                    );
                    if (config.onUpdate) config.onUpdate();
                },
                onComplete: () => {
                    // 确保最终颜色精确
                    target.color = targetColor;
                    this._removeAnimation(animationId);
                    if (config.onComplete) config.onComplete();
                }
            });

        // 处理重复播放和yoyo效果（颜色动画通常不需要yoyo，但保持一致性）
        if (config.yoyo) {
            // yoyo效果：来回播放
            if (config.repeat !== undefined && config.repeat >= 0) {
                // 有明确的repeat值，使用该值（0表示播放一次来回）
                const repeatCount = Math.max(1, config.repeat);
                colorTween.repeat(repeatCount);
            } else {
                // 没有repeat值，默认播放一次
            }
        } else if (config.repeat !== undefined && config.repeat > 0) {
            // 普通重复：重复播放指定次数
            colorTween.repeat(config.repeat);
        }

        colorTween.start();

        // 记录动画信息
        const animationInfo: AnimationInfo = {
            id: animationId,
            type: AnimationType.COLOR,
            target: target,
            tween: colorTween,
            startTime: Date.now(),
            config: config
        };

        this._activeAnimations.set(animationId, animationInfo);

        // 移除过多的DEBUG日志以提升性能

        return animationId;
    }

    /**
     * 播放缩放动画
     *
     * 创建并播放目标对象的缩放动画，支持三维缩放控制。
     * 常用于字母呼吸效果、按钮点击反馈、元素强调等场景。
     *
     * @param {ScaleAnimationParams} params - 缩放动画参数对象
     * @param {Node | Label | Sprite} params.target - 动画目标对象，必须是有效的Cocos Creator节点
     * @param {Vec3} params.targetScale - 目标缩放值，Vec3格式 (x, y, z)
     * @param {AnimationConfig} params.config - 动画配置
     * @param {number} params.config.duration - 动画持续时间（秒）
     * @param {string} params.config.easing - 缓动函数类型
     * @param {Function} [params.config.onComplete] - 动画完成回调函数
     * @param {Function} [params.config.onUpdate] - 动画更新回调函数
     *
     * @returns {string | null} 动画唯一标识符，用于后续控制（停止、暂停等），失败时返回null
     *
     * @example
     * ```typescript
     * // 播放字母呼吸动画
     * const animationId = animationManager.playScaleAnimation({
     *     target: letterNode,
     *     targetScale: new Vec3(1.2, 1.2, 1.0),
     *     config: {
     *         duration: 0.5,
     *         easing: 'easeInOut',
     *         onComplete: () => console.log('动画完成')
     *     }
     * });
     * ```
     */
    public playScaleAnimation(params: ScaleAnimationParams): string | null {
        if (!this._canPlayAnimation()) {
            return null;
        }

        const animationId = this._generateAnimationId();
        const { target, targetScale, config } = params;

        // 处理缩放值
        const finalScale = typeof targetScale === 'number'
            ? new Vec3(targetScale, targetScale, targetScale)
            : targetScale;

        // 准备tween配置
        const tweenConfig: any = {
            easing: config.easing || AnimationManager._globalConfig.defaultEasing,
            onStart: () => {
                if (config.onStart) config.onStart();
            },
            onUpdate: config.onUpdate,
            onComplete: () => {
                this._removeAnimation(animationId);
                if (config.onComplete) config.onComplete();
            }
        };

        // 处理重复播放和yoyo效果
        if (config.yoyo) {
            if (config.repeat !== undefined && config.repeat === -1) {
                // 无限循环yoyo效果 - 使用特殊处理
                this._createInfiniteYoyoScaleAnimation(target, targetScale, config, animationId);
                return animationId;
            } else {
                // 有限次数的yoyo效果 - 使用手动yoyo实现，确保兼容性

                // 修改onComplete回调，实现手动yoyo
                const originalOnComplete = tweenConfig.onComplete;
                tweenConfig.onComplete = () => {
                    if (AnimationManager._globalConfig.debugMode) {
                        Logger.debug('AnimationManager', `缩放动画第一阶段完成，开始第二阶段（缩小）`);
                    }

                    // 第二阶段：缩小回原始大小（Vec3(1.0, 1.0, 1.0)）
                    const returnTween = tween(target)
                        .to(config.duration, { scale: new Vec3(1.0, 1.0, 1.0) }, {
                            easing: tweenConfig.easing,
                            onComplete: () => {
                                // 确保节点状态正确
                                target.setScale(new Vec3(1.0, 1.0, 1.0));
                                target.active = true;

                                // 确保Label组件可见
                                const label = target.getComponent(Label);
                                if (label) {
                                    label.enabled = true;
                                    // 不修改颜色，保持动画设置的颜色
                                }

                                this._removeAnimation(animationId);
                                if (AnimationManager._globalConfig.debugMode) {
                                    Logger.debug('AnimationManager', `手动yoyo缩放动画完全完成: ${animationId}`);
                                }
                                if (originalOnComplete) originalOnComplete();
                            }
                        });

                    returnTween.start();
                };
            }
        } else if (config.repeat !== undefined && config.repeat === -1) {
            // 无限循环普通重复
            tweenConfig.repeat = Infinity;
            if (AnimationManager._globalConfig.debugMode) {
                Logger.debug('AnimationManager', `缩放动画设置无限重复`);
            }
        } else if (config.repeat !== undefined && config.repeat > 0) {
            // 普通重复：重复播放指定次数
            tweenConfig.repeat = config.repeat;
            if (AnimationManager._globalConfig.debugMode) {
                Logger.debug('AnimationManager', `缩放动画设置普通重复: repeat=${config.repeat}`);
            }
        }



        // 创建缩放动画
        const scaleTween = tween(target)
            .delay(config.delay || 0)
            .to(config.duration, { scale: finalScale }, tweenConfig);

        scaleTween.start();

        // 记录动画信息
        const animationInfo: AnimationInfo = {
            id: animationId,
            type: AnimationType.SCALE,
            target: target,
            tween: scaleTween,
            startTime: Date.now(),
            config: config
        };

        this._activeAnimations.set(animationId, animationInfo);

        // 移除过多的DEBUG日志以提升性能

        return animationId;
    }

    /**
     * 播放位移动画
     * @param params 动画参数
     * @returns 动画ID，用于后续控制
     */
    public playPositionAnimation(params: PositionAnimationParams): string | null {
        if (!this._canPlayAnimation()) {
            return null;
        }

        const animationId = this._generateAnimationId();
        const { target, targetPosition, config } = params;

        // 创建位移动画
        const positionTween = tween(target)
            .delay(config.delay || 0)
            .to(config.duration, { position: targetPosition }, {
                easing: config.easing || AnimationManager._globalConfig.defaultEasing,
                onStart: () => {
                    if (config.onStart) config.onStart();
                    // 移除过多的DEBUG日志以提升性能
                },
                onUpdate: config.onUpdate,
                onComplete: () => {
                    this._removeAnimation(animationId);
                    if (config.onComplete) config.onComplete();
                    // 移除过多的DEBUG日志以提升性能
                }
            });

        // 处理重复播放和yoyo效果
        if (config.yoyo) {
            // yoyo效果：来回播放
            if (config.repeat !== undefined && config.repeat >= 0) {
                // 有明确的repeat值，使用该值（0表示播放一次来回）
                positionTween.repeat(Math.max(1, config.repeat)).yoyo(true);
            } else {
                // 没有repeat值，默认播放一次来回
                positionTween.repeat(1).yoyo(true);
            }
        } else if (config.repeat !== undefined && config.repeat > 0) {
            // 普通重复：重复播放指定次数
            positionTween.repeat(config.repeat);
        }

        positionTween.start();

        // 记录动画信息
        const animationInfo: AnimationInfo = {
            id: animationId,
            type: AnimationType.POSITION,
            target: target,
            tween: positionTween,
            startTime: Date.now(),
            config: config
        };

        this._activeAnimations.set(animationId, animationInfo);

        if (AnimationManager._globalConfig.debugMode) {
            Logger.debug('AnimationManager', `创建位移动画: ${animationId}, 目标位置: ${targetPosition}`);
        }

        return animationId;
    }

    /**
     * 播放透明度动画
     * @param params 动画参数
     * @returns 动画ID，用于后续控制
     */
    public playOpacityAnimation(params: OpacityAnimationParams): string | null {
        if (!this._canPlayAnimation()) {
            return null;
        }

        const animationId = this._generateAnimationId();
        const { target, targetOpacity, config } = params;

        // 创建透明度动画
        const opacityTween = tween(target)
            .delay(config.delay || 0)
            .to(config.duration, { opacity: targetOpacity }, {
                easing: config.easing || AnimationManager._globalConfig.defaultEasing,
                onStart: () => {
                    if (config.onStart) config.onStart();
                    if (AnimationManager._globalConfig.debugMode) {
                        Logger.debug('AnimationManager', `透明度动画开始: ${animationId}`);
                    }
                },
                onUpdate: config.onUpdate,
                onComplete: () => {
                    this._removeAnimation(animationId);
                    if (config.onComplete) config.onComplete();
                    if (AnimationManager._globalConfig.debugMode) {
                        Logger.debug('AnimationManager', `透明度动画完成: ${animationId}`);
                    }
                }
            });

        // 处理重复播放和yoyo效果
        if (config.yoyo) {
            // yoyo效果：来回播放
            if (config.repeat !== undefined && config.repeat >= 0) {
                // 有明确的repeat值，使用该值（0表示播放一次来回）
                opacityTween.repeat(Math.max(1, config.repeat)).yoyo(true);
            } else {
                // 没有repeat值，默认播放一次来回
                opacityTween.repeat(1).yoyo(true);
            }
        } else if (config.repeat !== undefined && config.repeat > 0) {
            // 普通重复：重复播放指定次数
            opacityTween.repeat(config.repeat);
        }

        opacityTween.start();

        // 记录动画信息
        const animationInfo: AnimationInfo = {
            id: animationId,
            type: AnimationType.OPACITY,
            target: target,
            tween: opacityTween,
            startTime: Date.now(),
            config: config
        };

        this._activeAnimations.set(animationId, animationInfo);

        if (AnimationManager._globalConfig.debugMode) {
            Logger.debug('AnimationManager', `创建透明度动画: ${animationId}, 目标透明度: ${targetOpacity}`);
        }

        return animationId;
    }

    /**
     * 停止指定动画
     * @param animationId 动画ID
     * @returns 是否成功停止
     */
    public stopAnimation(animationId: string): boolean {
        const animationInfo = this._activeAnimations.get(animationId);
        if (!animationInfo) {
            if (AnimationManager._globalConfig.debugMode) {
                Logger.warn('AnimationManager', `动画不存在: ${animationId}`);
            }
            return false;
        }

        // 安全检查：确保tween对象存在且有效
        if (animationInfo.tween && typeof animationInfo.tween.stop === 'function') {
            try {
                animationInfo.tween.stop();
            } catch (error) {
                Logger.warn('AnimationManager', `停止动画时发生错误: ${animationId}`, error as Error);
            }
        }

        // 🔧 关键修复：停止动画时重置缩放状态
        this._resetAnimationTargetScale(animationInfo);

        this._activeAnimations.delete(animationId);

        if (AnimationManager._globalConfig.debugMode) {
            Logger.debug('AnimationManager', `停止动画: ${animationId}`);
        }

        return true;
    }

    /**
     * 重置动画目标的缩放状态
     * 确保停止动画时目标对象恢复到正确的缩放值
     * @param animationInfo 动画信息
     */
    private _resetAnimationTargetScale(animationInfo: AnimationInfo): void {
        if (!animationInfo || !animationInfo.target) return;

        // 只对缩放动画进行缩放重置
        if (animationInfo.type === AnimationType.SCALE) {
            const target = animationInfo.target;

            // 检查目标是否是Node类型且有效
            if (target && typeof target.setScale === 'function' && target.isValid) {
                try {
                    // 🔧 关键修复：强制重置到1.0倍缩放
                    target.setScale(1.0, 1.0, 1.0);

                    // 🔧 额外保险：停止目标上所有可能的Tween
                    Tween.stopAllByTarget(target);

                    if (AnimationManager._globalConfig.debugMode) {
                        Logger.debug('AnimationManager', `动画停止时重置缩放: ${animationInfo.id}, 当前缩放: ${target.scale}`);
                    }
                } catch (error) {
                    Logger.warn('AnimationManager', `重置缩放时发生错误: ${animationInfo.id}`, error as Error);
                }
            }
        }
    }

    /**
     * 停止指定目标的所有动画
     * @param target 目标对象
     * @returns 停止的动画数量
     */
    public stopAnimationsByTarget(target: any): number {
        let stoppedCount = 0;
        const animationsToStop: string[] = [];

        // 查找目标对象的所有动画
        this._activeAnimations.forEach((animationInfo, animationId) => {
            if (animationInfo.target === target) {
                animationsToStop.push(animationId);
            }
        });

        // 停止找到的动画
        animationsToStop.forEach(animationId => {
            if (this.stopAnimation(animationId)) {
                stoppedCount++;
            }
        });

        if (AnimationManager._globalConfig.debugMode && stoppedCount > 0) {
            Logger.debug('AnimationManager', `停止目标对象的 ${stoppedCount} 个动画`);
        }

        return stoppedCount;
    }

    /**
     * 停止指定类型的所有动画
     * @param type 动画类型
     * @returns 停止的动画数量
     */
    public stopAnimationsByType(type: AnimationType): number {
        let stoppedCount = 0;
        const animationsToStop: string[] = [];

        // 查找指定类型的所有动画
        this._activeAnimations.forEach((animationInfo, animationId) => {
            if (animationInfo.type === type) {
                animationsToStop.push(animationId);
            }
        });

        // 停止找到的动画
        animationsToStop.forEach(animationId => {
            if (this.stopAnimation(animationId)) {
                stoppedCount++;
            }
        });

        if (AnimationManager._globalConfig.debugMode && stoppedCount > 0) {
            Logger.debug('AnimationManager', `停止 ${type} 类型的 ${stoppedCount} 个动画`);
        }

        return stoppedCount;
    }

    /**
     * 停止所有动画
     * @returns 停止的动画数量
     */
    public stopAllAnimations(): number {
        const stoppedCount = this._activeAnimations.size;

        this._activeAnimations.forEach((animationInfo) => {
            // 安全检查：确保tween对象存在且有效
            if (animationInfo.tween && typeof animationInfo.tween.stop === 'function') {
                try {
                    animationInfo.tween.stop();
                } catch (error) {
                    Logger.warn('AnimationManager', `停止动画时发生错误`, error as Error);
                }
            }
        });

        this._activeAnimations.clear();

        if (AnimationManager._globalConfig.debugMode && stoppedCount > 0) {
            Logger.debug('AnimationManager', `停止所有动画，共 ${stoppedCount} 个`);
        }

        return stoppedCount;
    }

    /**
     * 暂停指定动画
     * @param animationId 动画ID
     * @returns 是否成功暂停
     */
    public pauseAnimation(animationId: string): boolean {
        const animationInfo = this._activeAnimations.get(animationId);
        if (!animationInfo) {
            return false;
        }

        // 安全检查：确保tween对象存在且有效
        if (animationInfo.tween && typeof animationInfo.tween.pause === 'function') {
            try {
                animationInfo.tween.pause();
            } catch (error) {
                Logger.warn('AnimationManager', `暂停动画时发生错误: ${animationId}`, error as Error);
                return false;
            }
        }

        if (AnimationManager._globalConfig.debugMode) {
            Logger.debug('AnimationManager', `暂停动画: ${animationId}`);
        }

        return true;
    }

    /**
     * 恢复指定动画
     * @param animationId 动画ID
     * @returns 是否成功恢复
     */
    public resumeAnimation(animationId: string): boolean {
        const animationInfo = this._activeAnimations.get(animationId);
        if (!animationInfo) {
            return false;
        }

        // 安全检查：确保tween对象存在且有效
        if (animationInfo.tween && typeof animationInfo.tween.resume === 'function') {
            try {
                animationInfo.tween.resume();
            } catch (error) {
                Logger.warn('AnimationManager', `恢复动画时发生错误: ${animationId}`, error as Error);
                return false;
            }
        }

        if (AnimationManager._globalConfig.debugMode) {
            Logger.debug('AnimationManager', `恢复动画: ${animationId}`);
        }

        return true;
    }

    /**
     * 暂停所有动画
     * @returns 暂停的动画数量
     */
    public pauseAllAnimations(): number {
        let pausedCount = 0;

        this._activeAnimations.forEach((animationInfo) => {
            // 安全检查：确保tween对象存在且有效
            if (animationInfo.tween && typeof animationInfo.tween.pause === 'function') {
                try {
                    animationInfo.tween.pause();
                    pausedCount++;
                } catch (error) {
                    Logger.warn('AnimationManager', `暂停动画时发生错误`, error as Error);
                }
            }
        });

        if (AnimationManager._globalConfig.debugMode && pausedCount > 0) {
            Logger.debug('AnimationManager', `暂停所有动画，共 ${pausedCount} 个`);
        }

        return pausedCount;
    }

    /**
     * 恢复所有动画
     * @returns 恢复的动画数量
     */
    public resumeAllAnimations(): number {
        let resumedCount = 0;

        this._activeAnimations.forEach((animationInfo) => {
            // 安全检查：确保tween对象存在且有效
            if (animationInfo.tween && typeof animationInfo.tween.resume === 'function') {
                try {
                    animationInfo.tween.resume();
                    resumedCount++;
                } catch (error) {
                    Logger.warn('AnimationManager', `恢复动画时发生错误`, error as Error);
                }
            }
        });

        if (AnimationManager._globalConfig.debugMode && resumedCount > 0) {
            Logger.debug('AnimationManager', `恢复所有动画，共 ${resumedCount} 个`);
        }

        return resumedCount;
    }

    /**
     * 创建无限循环的yoyo缩放动画
     * @param target 目标节点
     * @param targetScale 目标缩放值
     * @param config 动画配置
     * @param animationId 动画ID
     */
    private _createInfiniteYoyoScaleAnimation(target: Node, targetScale: number, config: AnimationConfig, animationId: string): void {
        if (AnimationManager._globalConfig.debugMode) {
            Logger.debug('AnimationManager', `创建无限循环yoyo缩放动画: ${animationId}`);
        }

        // 创建递归的yoyo动画函数
        const createYoyoCycle = () => {
            // 检查动画是否已被停止
            if (!this._activeAnimations.has(animationId)) {
                return; // 动画已被停止，不再继续
            }

            // 第一阶段：放大
            const expandTween = tween(target)
                .to(config.duration, { scale: new Vec3(targetScale, targetScale, targetScale) }, {
                    easing: config.easing || 'sineInOut',
                    onComplete: () => {
                        // 检查动画是否已被停止
                        if (!this._activeAnimations.has(animationId)) {
                            return; // 动画已被停止，不再继续
                        }

                        // 第二阶段：缩小
                        const shrinkTween = tween(target)
                            .to(config.duration, { scale: new Vec3(1.0, 1.0, 1.0) }, {
                                easing: config.easing || 'sineInOut',
                                onComplete: () => {
                                    // 检查动画是否已被停止
                                    if (!this._activeAnimations.has(animationId)) {
                                        return; // 动画已被停止，不再继续
                                    }

                                    // 递归调用，创建下一个循环
                                    createYoyoCycle();
                                }
                            });

                        shrinkTween.start();
                    }
                });

            expandTween.start();
        };

        // 记录动画信息（无限循环动画的特殊处理）
        const animationInfo: AnimationInfo = {
            id: animationId,
            type: AnimationType.SCALE,
            target: target,
            tween: null as any, // 无限循环动画没有单一的tween对象
            startTime: Date.now(),
            config: config
        };
        this._activeAnimations.set(animationId, animationInfo);

        // 开始第一个循环
        createYoyoCycle();
    }

    /**
     * 移除动画记录（内部方法）
     * @param animationId 动画ID
     */
    private _removeAnimation(animationId: string): void {
        this._activeAnimations.delete(animationId);
    }

    /**
     * 获取活跃动画数量
     * @returns 当前活跃的动画数量
     */
    public getActiveAnimationCount(): number {
        return this._activeAnimations.size;
    }

    /**
     * 获取指定类型的活跃动画数量
     * @param type 动画类型
     * @returns 指定类型的活跃动画数量
     */
    public getActiveAnimationCountByType(type: AnimationType): number {
        let count = 0;
        this._activeAnimations.forEach((animationInfo) => {
            if (animationInfo.type === type) {
                count++;
            }
        });
        return count;
    }

    /**
     * 获取指定目标的活跃动画数量
     * @param target 目标对象
     * @returns 指定目标的活跃动画数量
     */
    public getActiveAnimationCountByTarget(target: any): number {
        let count = 0;
        this._activeAnimations.forEach((animationInfo) => {
            if (animationInfo.target === target) {
                count++;
            }
        });
        return count;
    }

    /**
     * 检查动画是否存在
     * @param animationId 动画ID
     * @returns 动画是否存在
     */
    public hasAnimation(animationId: string): boolean {
        return this._activeAnimations.has(animationId);
    }

    /**
     * 获取动画信息
     * @param animationId 动画ID
     * @returns 动画信息，如果不存在则返回null
     */
    public getAnimationInfo(animationId: string): AnimationInfo | null {
        return this._activeAnimations.get(animationId) || null;
    }

    /**
     * 获取所有活跃动画的信息
     * @returns 所有活跃动画的信息数组
     */
    public getAllAnimationInfo(): AnimationInfo[] {
        return Array.from(this._activeAnimations.values());
    }

    /**
     * 获取动画统计信息
     * @returns 动画统计信息
     */
    public getAnimationStatistics(): {
        totalAnimations: number;
        animationsByType: Record<string, number>;
        averageDuration: number;
        oldestAnimationAge: number;
    } {
        const totalAnimations = this._activeAnimations.size;
        const animationsByType: Record<string, number> = {};
        let totalDuration = 0;
        let oldestTime = Date.now();

        // 统计各类型动画数量和持续时间
        this._activeAnimations.forEach((animationInfo) => {
            // 按类型统计
            if (!animationsByType[animationInfo.type]) {
                animationsByType[animationInfo.type] = 0;
            }
            animationsByType[animationInfo.type]++;

            // 累计持续时间
            totalDuration += animationInfo.config.duration;

            // 找到最早的动画
            if (animationInfo.startTime < oldestTime) {
                oldestTime = animationInfo.startTime;
            }
        });

        return {
            totalAnimations,
            animationsByType,
            averageDuration: totalAnimations > 0 ? totalDuration / totalAnimations : 0,
            oldestAnimationAge: totalAnimations > 0 ? Date.now() - oldestTime : 0
        };
    }



    /**
     * 组件销毁时清理资源
     */
    onDestroy(): void {
        try {
            // 停止所有动画
            this.stopAllAnimations();

            // 清理单例引用
            if (AnimationManager._instance === this) {
                AnimationManager._instance = null;
            }
        } catch (error) {
            Logger.warn('AnimationManager', '清理动画管理器时发生错误', error as Error);
        }

        Logger.info('AnimationManager', '动画管理器已销毁');
    }

    /**
     * 安全创建Tween动画的辅助方法
     * @param target 目标对象
     * @param onTweenCreated Tween创建完成回调
     * @returns 是否成功创建
     */
    private _safeCreateTween<T>(target: T, onTweenCreated: (tweenObj: Tween<T>) => void): boolean {
        try {
            if (!target) {
                Logger.error('AnimationManager', '动画目标对象为空');
                return false;
            }

            const tweenObj = tween(target);
            if (!tweenObj) {
                Logger.error('AnimationManager', 'Tween对象创建失败');
                return false;
            }

            onTweenCreated(tweenObj);
            return true;
        } catch (error) {
            Logger.error('AnimationManager', '创建Tween动画时发生错误', error as Error);
            return false;
        }
    }
}
