import { _decorator, Component, Node } from 'cc';
import { Logger } from '../../Utils/Logger';
import { TranslationAPIService } from '../Services/TranslationAPIService';
import { TranslationCacheService } from '../Services/TranslationCacheService';
import { TranslationAnimationController } from '../Controllers/TranslationAnimationController';

const { ccclass } = _decorator;

/**
 * 翻译管理器 (架构重构版本)
 *
 * 负责协调和管理游戏中的翻译功能，采用单例模式确保全局唯一性。
 * 遵循单一职责原则，作为轻量级协调器管理各个翻译服务组件。
 *
 * 核心职责：
 * - 服务协调：统一管理API服务、缓存服务和动画控制器
 * - 依赖注入：初始化和管理各个服务组件的依赖关系
 * - 流程控制：协调翻译请求的完整流程
 * - 错误处理：统一处理各服务层的错误和异常
 * - 对外接口：提供简洁的翻译功能调用接口
 *
 * <AUTHOR>
 * @version 2.0 (架构重构版本)
 * @since 2025-08-03
 */
@ccclass('TranslationManager')
export class TranslationManager extends Component {

    /**
     * 单例实例
     */
    private static _instance: TranslationManager | null = null;

    /**
     * 翻译API服务
     */
    private _apiService: TranslationAPIService | null = null;

    /**
     * 翻译缓存服务
     */
    private _cacheService: TranslationCacheService | null = null;

    /**
     * 翻译动画控制器
     */
    private _animationController: TranslationAnimationController | null = null;

    /**
     * 服务初始化状态
     */
    private _isInitialized: boolean = false;

    /**
     * 并发请求控制
     */
    private _activeRequests: Set<string> = new Set();

    /**
     * 获取单例实例
     */
    public static getInstance(): TranslationManager | null {
        return TranslationManager._instance;
    }

    /**
     * 组件加载时初始化
     */
    onLoad() {
        if (TranslationManager._instance === null) {
            TranslationManager._instance = this;
            this._initializeServices();
            Logger.info('TranslationManager', '翻译管理器初始化完成');
        } else {
            // 销毁重复实例
            this.scheduleOnce(() => {
                this.node.destroy();
            }, 0);
            return;
        }
    }

    /**
     * 组件销毁时清理
     */
    onDestroy() {
        if (TranslationManager._instance === this) {
            this._cleanup();
            TranslationManager._instance = null;
        }
    }

    /**
     * 初始化所有服务组件
     */
    private _initializeServices(): void {
        try {
            // 初始化API服务
            this._apiService = new TranslationAPIService();
            Logger.debug('TranslationManager', 'API服务已初始化');

            // 初始化缓存服务
            this._cacheService = new TranslationCacheService();
            Logger.debug('TranslationManager', '缓存服务已初始化');

            // 初始化动画控制器
            this._animationController = new TranslationAnimationController();
            Logger.debug('TranslationManager', '动画控制器已初始化');

            this._isInitialized = true;
            Logger.info('TranslationManager', '所有翻译服务已初始化');
        } catch (error) {
            Logger.error('TranslationManager', '翻译服务初始化失败', error as Error);
            this._isInitialized = false;
        }
    }

    /**
     * 请求单词翻译并显示动画
     * @param word 要翻译的单词
     * @param wordNode 单词节点（用于定位翻译显示位置）
     * @param onComplete 完成回调（可选）
     */
    public async requestTranslation(
        word: string,
        wordNode: Node,
        onComplete?: (success: boolean, translation?: string) => void
    ): Promise<void> {
        // 检查服务是否已初始化
        if (!this._isInitialized) {
            Logger.warn('TranslationManager', '翻译服务未初始化，跳过翻译请求');
            onComplete?.(false);
            return;
        }

        const upperWord = word.toUpperCase();

        // 检查是否已有相同请求在处理
        if (this._activeRequests.has(upperWord)) {
            Logger.debug('TranslationManager', `单词 "${upperWord}" 翻译请求已在处理中`);
            onComplete?.(false);
            return;
        }

        // 检查并发请求数量限制
        if (this._activeRequests.size >= 3) {
            Logger.warn('TranslationManager', '并发翻译请求数量已达上限，跳过请求');
            onComplete?.(false);
            return;
        }

        try {
            // 标记请求开始
            this._activeRequests.add(upperWord);

            // 首先检查缓存
            const cachedTranslation = this._cacheService?.get(upperWord);
            if (cachedTranslation) {
                Logger.debug('TranslationManager', `使用缓存翻译: ${upperWord} -> ${cachedTranslation}`);
                this._animationController?.showTranslationAnimation(upperWord, cachedTranslation, wordNode);
                onComplete?.(true, cachedTranslation);
                return;
            }

            // 发起API请求
            const translation = await this._apiService?.requestTranslation(upperWord);
            if (translation) {
                // 缓存翻译结果
                this._cacheService?.set(upperWord, translation);

                // 显示翻译动画
                this._animationController?.showTranslationAnimation(upperWord, translation, wordNode);

                Logger.info('TranslationManager', `翻译成功: ${upperWord} -> ${translation}`);
                onComplete?.(true, translation);
            } else {
                Logger.warn('TranslationManager', `翻译失败: ${upperWord}`);
                onComplete?.(false);
            }

        } catch (error) {
            Logger.error('TranslationManager', `翻译请求异常: ${upperWord}`, error as Error);
            onComplete?.(false);
        } finally {
            // 清理请求标记
            this._activeRequests.delete(upperWord);
        }
    }



    /**
     * 清理所有活跃的翻译显示
     */
    public clearAllTranslations(): void {
        this._animationController?.clearAllTranslations();
        Logger.info('TranslationManager', '所有翻译显示已清理');
    }

    /**
     * 获取翻译缓存统计信息
     */
    public getCacheStats(): any {
        return this._cacheService?.getStats() || null;
    }

    /**
     * 获取API服务状态
     */
    public getAPIServiceStatus(): any {
        return this._apiService?.getServiceStatus() || null;
    }

    /**
     * 检查翻译系统是否可用
     */
    public isTranslationSystemAvailable(): boolean {
        return this._isInitialized &&
               this._apiService !== null &&
               this._cacheService !== null &&
               this._animationController !== null;
    }

    /**
     * 清理资源
     */
    private _cleanup(): void {
        try {
            // 清理所有翻译显示
            this.clearAllTranslations();

            // 销毁服务组件
            this._animationController?.destroy();
            this._cacheService?.destroy();

            // 清理引用
            this._apiService = null;
            this._cacheService = null;
            this._animationController = null;
            this._activeRequests.clear();
            this._isInitialized = false;

            Logger.info('TranslationManager', '翻译管理器资源清理完成');
        } catch (error) {
            Logger.error('TranslationManager', '清理翻译管理器资源失败', error as Error);
        }
    }


}
