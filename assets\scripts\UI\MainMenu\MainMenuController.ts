import { _decorator, Component, Node, Button } from 'cc';
import { SceneManager } from '../../Core/SceneManager';
import { AudioIntegrationService } from '../../Core/AudioIntegrationService';
import { CoreSystemManager } from '../../Core/CoreSystemManager';
import { Logger } from '../../Utils/Logger';
const { ccclass, property } = _decorator;

/**
 * 主菜单控制器 - 负责主菜单界面的交互逻辑
 * 遵循单一职责原则，只处理主菜单相关的UI交互
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-03
 */
@ccclass('MainMenuController')
export class MainMenuController extends Component {

    /**
     * 开始游戏按钮节点引用
     * 需要在编辑器中手动拖拽赋值
     */
    @property(Node)
    startGameButton: Node = null!;

    /**
     * 按钮组件缓存
     */
    private _startButtonComponent: Button | null = null;

    /**
     * 音效集成服务引用
     */
    private _audioService: AudioIntegrationService | null = null;

    /**
     * 组件生命周期 - 初始化
     */
    onLoad() {
        this._initializeComponents();
    }

    /**
     * 组件生命周期 - 启动
     */
    start() {
        // 延迟初始化，确保GameInitializer先完成初始化
        this.scheduleOnce(() => {
            this._initializeAudioService();
            this._bindEvents();
            this._preloadGameScene();
        }, 0.1);
    }

    /**
     * 组件销毁时清理
     */
    onDestroy() {
        this._unbindEvents();
    }

    /**
     * 初始化组件引用
     */
    private _initializeComponents(): void {
        // 验证必要的节点引用
        if (!this.startGameButton) {
            Logger.error('MainMenuController', '开始游戏按钮节点未设置！');
            return;
        }

        // 获取按钮组件
        this._startButtonComponent = this.startGameButton.getComponent(Button);
        if (!this._startButtonComponent) {
            Logger.error('MainMenuController', '开始游戏按钮缺少Button组件！');
            return;
        }

        // 音效服务将在start()方法中延迟初始化
    }

    /**
     * 初始化音效服务（延迟初始化）
     */
    private _initializeAudioService(): void {
        // 首先尝试从CoreSystemManager获取
        const coreSystemManager = CoreSystemManager.getInstance();
        if (coreSystemManager) {
            this._audioService = coreSystemManager.getAudioIntegrationService();
            if (this._audioService) {
                Logger.info('MainMenuController', '音效集成服务初始化成功（通过CoreSystemManager）');
                return;
            }
        }

        // 回退到直接获取实例
        this._audioService = AudioIntegrationService.getInstance();
        if (!this._audioService) {
            Logger.warn('MainMenuController', '音效集成服务未初始化，音效功能将不可用');
        } else {
            Logger.info('MainMenuController', '音效集成服务初始化成功（直接获取）');
        }
    }

    /**
     * 绑定事件监听
     */
    private _bindEvents(): void {
        if (this._startButtonComponent && this._startButtonComponent.node) {
            // 绑定按钮点击事件
            this._startButtonComponent.node.on(Button.EventType.CLICK, this._onStartGameClicked, this);
        } else {
            Logger.warn('MainMenuController', '无法绑定事件，按钮组件或节点无效');
        }
    }

    /**
     * 解绑事件监听
     */
    private _unbindEvents(): void {
        if (this._startButtonComponent && this._startButtonComponent.node && this._startButtonComponent.node.isValid) {
            // 解绑按钮点击事件
            this._startButtonComponent.node.off(Button.EventType.CLICK, this._onStartGameClicked, this);
            // 事件解绑完成
        }
    }

    /**
     * 开始游戏按钮点击事件处理
     */
    private async _onStartGameClicked(): Promise<void> {
        Logger.info('MainMenuController', '开始游戏按钮被点击');

        // 播放按钮点击音效
        if (this._audioService) {
            this._audioService.playButtonClickSound();
        }

        try {
            // 禁用按钮防止重复点击
            this._setButtonInteractable(false);

            // 获取场景管理器实例
            const sceneManager = SceneManager.getInstance();
            if (!sceneManager) {
                Logger.error('MainMenuController', '场景管理器未找到！');
                this._setButtonInteractable(true);
                return;
            }

            // 切换到游戏场景（默认关卡1）
            const success = await sceneManager.loadGameScene(1);

            if (!success) {
                Logger.error('MainMenuController', '场景切换失败！');
                this._setButtonInteractable(true);
                return;
            }



        } catch (error) {
            Logger.error('MainMenuController', '开始游戏时发生错误', error as Error);
            this._setButtonInteractable(true);
        }
    }

    /**
     * 设置按钮可交互状态
     * @param interactable 是否可交互
     */
    private _setButtonInteractable(interactable: boolean): void {
        if (this._startButtonComponent) {
            this._startButtonComponent.interactable = interactable;
        }
    }

    /**
     * 预加载游戏场景以提升用户体验
     */
    private async _preloadGameScene(): Promise<void> {
        try {
            const sceneManager = SceneManager.getInstance();
            if (sceneManager) {
                await sceneManager.preloadScene(SceneManager.SCENE_NAMES.GAME_SCENE);
                // 游戏场景预加载完成
            }
        } catch (error) {
            Logger.warn('MainMenuController', '游戏场景预加载失败', error as Error);
        }
    }

    /**
     * 公共方法：手动触发开始游戏（供其他脚本调用）
     */
    public startGame(): void {
        this._onStartGameClicked();
    }
}
