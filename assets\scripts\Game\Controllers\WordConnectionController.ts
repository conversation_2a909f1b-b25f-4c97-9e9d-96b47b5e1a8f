import { _decorator, Component, Node, EventTouch, Vec3 } from 'cc';
import { LetterGridController } from './LetterGridController';
import { WordAreaController } from './WordAreaController';
import { ConnectionVisualizer } from '../../Game/Components/ConnectionVisualizer';
import { WordValidator } from '../../Game/Validators/WordValidator';
import { ColorThemeManager } from '../../Game/Managers/ColorThemeManager';
import { LetterEventHandler } from '../../Game/Components/LetterEventHandler';
import { ConnectionStateManager } from '../../Game/Managers/ConnectionStateManager';
import { LetterGenerationService } from '../../Game/Services/LetterGenerationService';
import { IWordData } from '../../Data/WordDatabase';
import { GameUtils } from '../../Utils/GameUtils';
import { AudioIntegrationService } from '../../Core/AudioIntegrationService';
import { WordPronunciationService } from '../Services/WordPronunciationService';
import { Logger } from '../../Utils/Logger';
const { ccclass, property } = _decorator;

/**
 * 字母连线控制器
 *
 * 游戏核心交互协调器，负责统筹管理字母连接游戏的所有交互逻辑和组件协调。
 * 作为游戏的中央控制器，协调各个专业组件之间的通信和数据流转。
 *
 * 核心职责：
 * - 游戏流程控制：管理从开始连接到完成验证的整个流程
 * - 组件协调：协调字母网格、单词区域、视觉效果等各个组件
 * - 事件分发：处理用户输入事件并分发给相应的处理组件
 * - 状态管理：维护游戏的全局状态和连接状态
 * - 数据同步：确保各组件间的数据一致性
 *
 * 组件架构：
 * - LetterGridController: 字母网格管理和显示
 * - WordAreaController: 单词区域管理和状态显示
 * - ConnectionVisualizer: 连接过程的视觉效果
 * - WordValidator: 单词验证和匹配逻辑
 * - ColorThemeManager: 颜色主题和视觉风格管理
 * - LetterEventHandler: 字母触摸事件处理
 * - ConnectionStateManager: 连接状态管理
 * - AudioIntegrationService: 音效集成和播放
 *
 * 交互流程：
 * 1. 用户触摸字母开始连接
 * 2. 实时显示连接路径和视觉反馈
 * 3. 验证连接的单词是否有效
 * 4. 更新游戏状态和视觉效果
 * 5. 检查游戏完成条件
 *
 * 设计原则：
 * - 单一职责：专注于协调功能，不处理具体业务逻辑
 * - 委托模式：将具体逻辑委托给专业组件实现
 * - 松耦合：组件间通过接口通信，降低耦合度
 * - 可扩展：支持新功能和组件的轻松集成
 *
 * <AUTHOR>
 * @version 2.1
 * @since 2025-07-05
 */
@ccclass('WordConnectionController')
export class WordConnectionController extends Component {

    // ==================== 组件依赖注入 ====================

    /**
     * 字母网格控制器引用 - 负责字母网格的显示和数据管理
     */
    @property(LetterGridController)
    letterGridController: LetterGridController = null!;

    /**
     * 单词区域控制器引用 - 负责目标单词的显示和状态管理
     */
    @property(WordAreaController)
    wordAreaController: WordAreaController = null!;

    /**
     * 连线可视化器引用 - 负责连线过程的视觉效果和高亮显示
     */
    @property(ConnectionVisualizer)
    connectionVisualizer: ConnectionVisualizer = null!;

    /**
     * 事件处理器引用 - 负责字母节点的触摸事件处理
     */
    @property(LetterEventHandler)
    letterEventHandler: LetterEventHandler = null!;

    /**
     * 连接状态管理器引用 - 负责连接状态和路径的管理
     */
    @property(ConnectionStateManager)
    stateManager: ConnectionStateManager = null!;

    /**
     * 字母生成服务引用 - 负责字母网格的生成算法
     */
    @property(LetterGenerationService)
    letterGenerationService: LetterGenerationService = null!;

    // ==================== 内部服务实例 ====================

    /**
     * 单词验证器 - 负责单词有效性验证和路径匹配
     */
    private _wordValidator: WordValidator = null!;

    /**
     * 颜色主题管理器 - 负责游戏颜色主题的统一管理
     */
    private _colorThemeManager: ColorThemeManager = null!;

    /**
     * 音效集成服务 - 负责游戏音效的播放和管理
     */
    private _audioService: AudioIntegrationService | null = null;

    /**
     * 单词发音服务 - 负责单词发音功能的管理
     */
    private _pronunciationService: WordPronunciationService | null = null;

    // ==================== 组件生命周期 ====================

    /**
     * 组件初始化 - Cocos Creator生命周期方法
     * 按顺序初始化各个子系统，确保依赖关系正确建立
     */
    onLoad(): void {
        this._initializeInternalServices();
        this._setupComponentIntegration();
        this._setupEventSystem();
    }

    /**
     * 组件启动 - Cocos Creator生命周期方法
     * 在所有组件初始化完成后执行，确保服务连接正常
     */
    start(): void {
        // 🔧 场景切换后重新验证和连接发音服务
        this._reinitializePronunciationService();
    }

    // ==================== 初始化方法组 ====================

    /**
     * 初始化内部服务实例
     * 创建和配置控制器内部使用的服务对象
     */
    private _initializeInternalServices(): void {
        // 初始化单词验证器
        this._wordValidator = new WordValidator();

        // 获取颜色主题管理器单例实例
        this._colorThemeManager = ColorThemeManager.getInstance();
        if (!this._colorThemeManager) {
            Logger.warn('WordConnectionController', '颜色主题管理器未初始化，将使用默认颜色');
        }

        // 获取音效集成服务单例实例
        this._audioService = AudioIntegrationService.getInstance();
        if (!this._audioService) {
            Logger.warn('WordConnectionController', '音效集成服务未初始化，音效功能将不可用');
        }

        // 获取单词发音服务单例实例
        this._pronunciationService = WordPronunciationService.getInstance();
        if (!this._pronunciationService) {
            Logger.warn('WordConnectionController', '单词发音服务未初始化，发音功能将不可用');
        }

        // 配置连线可视化器的颜色管理
        this._configureVisualizerColorManagement();
    }

    /**
     * 配置可视化器的颜色管理
     * 确保连线可视化器使用统一的颜色主题管理
     */
    private _configureVisualizerColorManagement(): void {
        if (this.connectionVisualizer) {
            this.connectionVisualizer.setColorThemeManager(this._colorThemeManager);

            // 验证关键配置
            if (!this.connectionVisualizer.letterGridNode) {
                Logger.error('WordConnectionController', 'ConnectionVisualizer的letterGridNode未设置！请在编辑器中配置');
            }
        } else {
            Logger.warn('WordConnectionController', '连线可视化器未设置，无法传递颜色主题管理器');
        }
    }

    /**
     * 设置组件间的集成关系
     * 建立各个组件之间的协作关系
     */
    private _setupComponentIntegration(): void {
        // 设置网格控制器的事件处理器
        if (this.letterGridController && this.letterEventHandler) {
            this.letterGridController.setEventHandler(this.letterEventHandler);
        }

        // 设置字母选择回调
        this._setupLetterSelectionCallback();
    }

    /**
     * 设置事件系统
     * 配置全局和局部事件监听器
     */
    private _setupEventSystem(): void {
        this._setupGlobalEventListeners();
    }



    /**
     * 设置字母选择回调机制
     * 建立字母触摸事件与控制器处理方法的连接
     */
    private _setupLetterSelectionCallback(): void {
        if (!this.letterGridController || !this.letterEventHandler) {
            Logger.error('WordConnectionController', '字母网格控制器或事件处理器未设置');
            return;
        }

        // 通过事件处理器设置字母选择回调函数
        this.letterEventHandler.setLetterSelectionCallback((row: number, col: number, event: EventTouch) => {
            this._onLetterSelected(row, col, event);
        });
    }

    /**
     * 设置全局事件监听器
     * 配置支持跨字母拖拽的全局触摸事件
     */
    private _setupGlobalEventListeners(): void {
        if (!this.letterGridController) {
            Logger.error('WordConnectionController', '字母网格控制器未设置，无法设置全局事件监听');
            return;
        }

        const gridContainer = this.letterGridController.node;
        if (!gridContainer) {
            Logger.error('WordConnectionController', '字母网格容器节点未找到');
            return;
        }

        // 设置全局触摸事件监听器（用于跨字母拖拽）
        gridContainer.on(Node.EventType.TOUCH_START, this._onGlobalTouchStart, this);
        gridContainer.on(Node.EventType.TOUCH_MOVE, this._onGlobalTouchMove, this);
        gridContainer.on(Node.EventType.TOUCH_END, this._onGlobalTouchEnd, this);
        gridContainer.on(Node.EventType.TOUCH_CANCEL, this._onGlobalTouchEnd, this);
    }

    // ==================== 公共接口方法组 ====================

    /**
     * 设置目标单词
     * 配置当前关卡的目标单词，并初始化相关验证和颜色系统
     * @param words 目标单词数组
     */
    public setTargetWords(words: IWordData[]): void {
        if (this.stateManager) {
            this.stateManager.setTargetWords(words);
        }

        this._wordValidator.setTargetWords(words);
        this._colorThemeManager.assignColorsToWords(words);
    }



    // ==================== 全局事件处理方法组 ====================

    /**
     * 全局触摸开始事件处理
     * 作为直接字母事件的备用机制，主要用于调试和兼容性
     * @param event 触摸事件
     */
    private _onGlobalTouchStart(event: EventTouch): void {
        // 全局TOUCH_START作为直接字母事件的备用，主要用于调试
        // 实际的连线开始由直接字母事件处理，更精确
    }

    /**
     * 全局触摸移动事件处理
     * 处理跨字母拖拽，支持连续的字母连接
     * @param event 触摸事件
     */
    private _onGlobalTouchMove(event: EventTouch): void {
        if (!this._isValidConnectionState()) {
            return;
        }

        // 使用坐标转换工具找到当前触摸位置对应的字母
        const letterPos = GameUtils.convertTouchToLetterPosition(event, this.letterGridController);
        if (!letterPos) {
            return;
        }

        // 避免重复位置的处理
        if (this._isDuplicatePosition(letterPos)) {
            return;
        }

        // 尝试添加到连接路径
        if (this._canAddToPath(letterPos)) {
            this._addLetterToPath(letterPos);
        }
    }

    /**
     * 全局触摸结束事件处理
     * 完成当前的字母连接操作
     * @param event 触摸事件
     */
    private _onGlobalTouchEnd(event: EventTouch): void {
        if (!this._isValidConnectionState()) {
            return;
        }

        // 根据路径长度决定完成或取消连接
        const currentPath = this.stateManager!.getCurrentPath();
        if (currentPath.length > 1) {
            this._finishConnection();
        } else {
            this._cancelConnection();
        }
    }

    // ==================== 直接字母事件处理方法组 ====================

    /**
     * 字母选择事件处理
     * 处理直接点击字母节点的事件，提供精确的位置定位
     * @param row 行索引
     * @param col 列索引
     * @param event 触摸事件
     */
    private _onLetterSelected(row: number, col: number, event: EventTouch): void {
        if (!this.stateManager || this.stateManager.isGameCompleted()) {
            return;
        }

        const letterPos: LetterPosition = { row, col };

        // 根据事件类型分发处理
        // 直接字母事件主要处理TOUCH_START（精确定位）
        // TOUCH_MOVE和TOUCH_END由全局事件处理（支持跨字母拖拽）
        switch (event.type) {
            case Node.EventType.TOUCH_START:
                this._handleLetterTouchStart(letterPos, event);
                break;
            case Node.EventType.TOUCH_MOVE:
                // TOUCH_MOVE主要由全局事件处理，这里作为备用
                this._handleLetterTouchMove(letterPos, event);
                break;
            case Node.EventType.TOUCH_END:
            case Node.EventType.TOUCH_CANCEL:
                // TOUCH_END主要由全局事件处理，这里作为备用
                this._handleLetterTouchEnd(letterPos, event);
                break;
        }
    }



    /**
     * 处理字母触摸开始事件
     * 处理连线的开始或向现有连线添加字母
     * @param letterPos 字母位置
     * @param event 触摸事件
     */
    private _handleLetterTouchStart(letterPos: LetterPosition, event: EventTouch): void {
        if (!this.stateManager) return;

        // 如果已经在连线中，尝试添加到路径
        if (this.stateManager.isConnecting()) {
            if (!this._isDuplicatePosition(letterPos) && this._canAddToPath(letterPos)) {
                this._addLetterToPath(letterPos);
            }
            return;
        }

        // 开始新连线（支持共享字母起始）
        if (this._canStartNewConnection(letterPos)) {
            this._startConnection(letterPos);
            this._playConnectionStartAudio();
        }
    }

    /**
     * 处理字母触摸移动事件
     * 作为全局触摸移动的备用处理机制
     * @param letterPos 字母位置
     * @param event 触摸事件
     */
    private _handleLetterTouchMove(letterPos: LetterPosition, event: EventTouch): void {
        if (!this._isValidConnectionState()) {
            return;
        }

        // 避免重复位置处理
        if (this._isDuplicatePosition(letterPos)) {
            return;
        }

        // 尝试添加到连接路径
        if (this._canAddToPath(letterPos)) {
            this._addLetterToPath(letterPos);
        }
    }

    /**
     * 处理字母触摸结束事件
     * 作为全局触摸结束的备用处理机制
     * @param letterPos 字母位置
     * @param event 触摸事件
     */
    private _handleLetterTouchEnd(letterPos: LetterPosition, event: EventTouch): void {
        if (!this._isValidConnectionState()) return;

        // 根据路径长度决定完成或取消连接
        const currentPath = this.stateManager!.getCurrentPath();
        if (currentPath.length > 1) {
            this._finishConnection();
        } else {
            this._cancelConnection();
        }
    }

    // ==================== 连接状态管理方法组 ====================

    /**
     * 开始新的字母连接
     * 初始化连接状态并启动视觉反馈
     * @param startPos 起始位置
     */
    private _startConnection(startPos: LetterPosition): void {
        if (!this.stateManager) return;

        this.stateManager.startConnection(startPos);

        // 启动可视化反馈
        if (this.connectionVisualizer) {
            this.connectionVisualizer.startConnection(startPos);
        }
    }

    /**
     * 完成当前连接
     * 验证连接的有效性并处理结果
     */
    private _finishConnection(): void {
        if (!this.stateManager || !this._wordValidator || !this.letterGridController) {
            Logger.error('WordConnectionController', '关键组件未初始化，无法完成连接');
            return;
        }

        const currentPath = this.stateManager.getCurrentPath();
        if (!currentPath || currentPath.length === 0) {
            Logger.warn('WordConnectionController', '当前路径为空，无法完成连接');
            return;
        }

        // 使用验证器查找有效单词
        const validWord = this._wordValidator.findValidWordFromPath(currentPath, this.letterGridController);

        if (validWord && this._wordValidator.validateWord(validWord, currentPath)) {
            this._onWordCompleted(validWord);
            this._resetConnection();
        } else {
            this._onConnectionFailed();
            // 延迟重置连接状态，让失败高亮有时间显示
            this.scheduleOnce(() => {
                this._resetConnection();
            }, 0.6); // 比红色高亮持续时间稍长
        }
    }

    /**
     * 取消当前连接
     * 直接重置连接状态，不进行验证
     */
    private _cancelConnection(): void {
        this._resetConnection();
    }

    /**
     * 重置连接状态
     * 清理所有连接相关的状态和视觉效果
     */
    private _resetConnection(): void {
        if (this.stateManager) {
            this.stateManager.resetConnection();
        }

        if (this.connectionVisualizer) {
            this.connectionVisualizer.clearConnection();
        }
    }

    /**
     * 单词完成处理 - 集成连接动画反馈
     * @param word 完成的单词
     */
    private _onWordCompleted(word: string): void {
        if (!this.stateManager) return;

        const currentPath = this.stateManager.getCurrentPath();

        // 委托给状态管理器保存完成的单词
        this.stateManager.completeWord(word);

        // 成功反馈现在由ConnectionVisualizer的Sprite系统处理
        this._onWordCompletionAnimationFinished(word, currentPath);
        this._playSuccessEffect();

        // 播放单词发音
        this._playWordPronunciation(word);
    }

    /**
     * 单词完成动画结束后的处理
     * @param word 完成的单词
     * @param currentPath 连接路径
     */
    private _onWordCompletionAnimationFinished(word: string, currentPath: LetterPosition[]): void {
        // 委托给可视化器处理高亮显示
        if (this.connectionVisualizer) {
            const wordColor = this._colorThemeManager.getWordColor(word);
            this.connectionVisualizer.highlightCompletedWord(currentPath, wordColor);
        }

        // 更新单词区域的视觉状态
        if (this.wordAreaController) {
            this.wordAreaController.markWordAsCompleted(word);
        }
    }

    /**
     * 连线失败处理 - 集成连接动画反馈
     */
    private _onConnectionFailed(): void {
        if (!this.stateManager) return;

        const currentPath = this.stateManager.getCurrentPath();
        const possibleWords = this._wordValidator.extractPossibleWordsFromPath(currentPath, this.letterGridController);
        Logger.warn('WordConnectionController', `连线失败，尝试的单词: ${possibleWords.join(', ') || '无效路径'}`);

        // 失败反馈现在由ConnectionVisualizer的Sprite系统处理
        this._onConnectionFailureAnimationFinished(currentPath);
        this._playFailureEffect();
    }

    /**
     * 连接失败动画结束后的处理
     * @param currentPath 连接路径
     */
    private _onConnectionFailureAnimationFinished(currentPath: LetterPosition[]): void {
        // 显示深红色高亮效果（0.5秒快速反应）
        if (this.connectionVisualizer && currentPath.length > 0) {
            this.connectionVisualizer.showFailureHighlight([...currentPath], 500);
        }
    }

    /**
     * 获取连接的单词 - 委托给状态管理器（已弃用，使用WordValidator.findValidWordFromPath代替）
     * @returns 连接的单词字符串
     * @deprecated 使用WordValidator.findValidWordFromPath代替
     */
    private _getConnectedWord(): string {
        if (!this.stateManager) return '';

        // 委托给状态管理器，由它负责构建单词
        return this.stateManager.getConnectedWord(this.letterGridController);
    }



    /**
     * 检查游戏完成状态 - 委托给状态管理器
     */
    private _checkGameCompletion(): void {
        if (!this.stateManager) return;

        // 状态管理器内部已经处理了游戏完成检查
        if (this.stateManager.isGameCompleted()) {
            this._playVictoryEffect();
        }
    }

    /**
     * 播放成功效果
     */
    private _playSuccessEffect(): void {
        if (this._audioService) {
            this._audioService.playWordSuccessSound();
        }
    }

    /**
     * 播放失败效果
     */
    private _playFailureEffect(): void {
        if (this._audioService) {
            this._audioService.playWordErrorSound();
        }
    }

    /**
     * 播放胜利效果
     */
    private _playVictoryEffect(): void {
        if (this._audioService) {
            this._audioService.playLevelCompleteSound();
        }
    }



    /**
     * 检查是否是有效的起始位置
     * @param pos 位置
     * @returns 是否有效
     */
    private _isValidStartPosition(pos: LetterPosition): boolean {
        // 检查该位置是否有字母
        const letter = this.letterGridController.getLetterAt(pos.row, pos.col);
        if (!letter || letter === '') {
            return false;
        }

        // 注意：在单词搜索游戏中，字母可以被多个单词共享
        // 所以即使字母已经属于完成的单词，仍然可以作为新连线的起始位置
        // 这里不再阻止已完成单词的字母作为起始位置

        return true;
    }

    /**
     * 检查是否可以添加到路径
     * @param pos 位置
     * @returns 是否可以添加
     */
    private _canAddToPath(pos: LetterPosition): boolean {
        if (!this.stateManager) return false;

        // 检查位置是否有效
        const letter = this.letterGridController.getLetterAt(pos.row, pos.col);
        if (!letter) return false;

        // 使用 ConnectionStateManager 的统一检查逻辑
        return this.stateManager.canAddToPath(pos);
    }

    /**
     * 检查两个位置是否相邻（包括对角线）
     * @param pos1 位置1
     * @param pos2 位置2
     * @returns 是否相邻
     */
    private _isAdjacent(pos1: LetterPosition, pos2: LetterPosition): boolean {
        return GameUtils.arePositionsAdjacent(pos1, pos2);
    }





    /**
     * 基于触摸位置检测相邻字母
     * @param currentPos 当前字母位置
     * @param event 触摸事件
     * @returns 相邻字母位置或null
     */
    private _detectAdjacentLetterFromTouch(currentPos: LetterPosition, event: EventTouch): LetterPosition | null {
        if (!this._connectionPath || this._connectionPath.length === 0) return null;

        // 获取连线路径中的最后一个字母位置
        const lastPos = this._connectionPath[this._connectionPath.length - 1];

        // 获取8个相邻方向的位置
        const adjacentPositions = this._getAdjacentPositions(lastPos);

        // 检查每个相邻位置是否可以添加到路径
        for (const adjPos of adjacentPositions) {
            if (this._canAddToPath(adjPos)) {
                return adjPos;
            }
        }

        return null;
    }

    /**
     * 获取指定位置的8个相邻位置
     * @param pos 中心位置
     * @returns 相邻位置数组
     */
    private _getAdjacentPositions(pos: LetterPosition): LetterPosition[] {
        const adjacent: LetterPosition[] = [];

        // 8个方向：上、下、左、右、左上、右上、左下、右下
        const directions = [
            { row: -1, col: 0 },  // 上
            { row: 1, col: 0 },   // 下
            { row: 0, col: -1 },  // 左
            { row: 0, col: 1 },   // 右
            { row: -1, col: -1 }, // 左上
            { row: -1, col: 1 },  // 右上
            { row: 1, col: -1 },  // 左下
            { row: 1, col: 1 }    // 右下
        ];

        for (const dir of directions) {
            const newRow = pos.row + dir.row;
            const newCol = pos.col + dir.col;

            // 检查边界
            if (newRow >= 0 && newRow < 9 && newCol >= 0 && newCol < 8) {
                adjacent.push({ row: newRow, col: newCol });
            }
        }

        return adjacent;
    }



    // ==================== 内部工具方法组 ====================

    /**
     * 检查连接状态是否有效
     * 验证状态管理器是否存在且正在连接中
     * @returns 连接状态是否有效
     */
    private _isValidConnectionState(): boolean {
        return !!(this.stateManager && this.stateManager.isConnecting());
    }

    /**
     * 检查位置是否为重复位置
     * 避免在同一位置重复处理事件
     * @param letterPos 要检查的字母位置
     * @returns 是否为重复位置
     */
    private _isDuplicatePosition(letterPos: LetterPosition): boolean {
        if (!this.stateManager) return false;

        const currentPath = this.stateManager.getCurrentPath();
        if (currentPath.length === 0) return false;

        const lastPos = currentPath[currentPath.length - 1];
        return lastPos.row === letterPos.row && lastPos.col === letterPos.col;
    }

    /**
     * 检查是否可以开始新连接
     * 验证起始位置的有效性和状态管理器的准备状态
     * @param letterPos 起始位置
     * @returns 是否可以开始新连接
     */
    private _canStartNewConnection(letterPos: LetterPosition): boolean {
        return this._isValidStartPosition(letterPos) &&
               this.stateManager!.canStartNewWordAt(letterPos);
    }

    /**
     * 添加字母到连接路径
     * 统一处理添加字母的逻辑，包括状态更新和视觉反馈
     * @param letterPos 字母位置
     */
    private _addLetterToPath(letterPos: LetterPosition): void {
        if (!this.stateManager) return;

        this.stateManager.addToPath(letterPos);
        this._updateVisualFeedback();

        // 播放字母拖拽音效
        this._playLetterDragAudio();
    }

    /**
     * 播放连接开始音效
     * 统一处理连接开始时的音效播放
     */
    private _playConnectionStartAudio(): void {
        if (this._audioService) {
            this._audioService.playLetterConnectSound();
        }
    }

    /**
     * 播放字母拖拽音效
     * 统一处理字母拖拽时的音效播放
     */
    private _playLetterDragAudio(): void {
        if (this._audioService) {
            this._audioService.playLetterDragSound();
        }
    }

    /**
     * 播放单词发音
     * 统一处理单词完成后的发音播放
     * @param word 要发音的单词
     */
    private _playWordPronunciation(word: string): void {
        try {
            // 🔧 检查发音服务是否存在
            if (!this._pronunciationService) {
                Logger.warn('WordConnectionController', '发音服务不存在，尝试重新获取');
                this._pronunciationService = WordPronunciationService.getInstance();

                if (!this._pronunciationService) {
                    Logger.warn('WordConnectionController', '无法获取发音服务实例，跳过发音播放');
                    return;
                }
            }

            // 检查发音服务是否启用
            const isEnabled = this._pronunciationService.isEnabled();

            if (!isEnabled) {
                const serviceStatus = this._pronunciationService.getServiceStatus();
                Logger.warn('WordConnectionController', '发音服务不可用');

                // 如果管理器不可用，尝试触发重新连接
                if (!serviceStatus.managerAvailable) {
                    Logger.info('WordConnectionController', '尝试触发发音服务重新连接');
                }
            }

            // 🔧 异步播放发音，不阻塞游戏流程
            this._pronunciationService.playWordPronunciation(word).then((success) => {
                if (success) {
                    Logger.success('WordConnectionController', `单词 "${word}" 发音播放成功`);
                } else {
                    Logger.warn('WordConnectionController', `单词 "${word}" 发音播放失败`);
                }
            }).catch((error) => {
                Logger.error('WordConnectionController', `单词 "${word}" 发音播放异常`, error as Error);
            });

        } catch (error) {
            Logger.error('WordConnectionController', `播放单词 "${word}" 发音时发生异常`, error as Error);
        }
    }

    // 🔧 已移除强制播放判断 - 简化发音逻辑

    /**
     * 重新连接发音系统
     * 确保重新开始游戏后发音系统状态正常
     */
    private _clearPronunciationCache(): void {
        try {
            Logger.info('WordConnectionController', '开始重新连接发音系统');

            // 🔧 重新获取发音服务实例，确保连接正常
            this._pronunciationService = WordPronunciationService.getInstance();
            if (this._pronunciationService) {
                // 获取服务状态用于调试
                const status = this._pronunciationService.getServiceStatus();
                Logger.info('WordConnectionController',
                    `发音服务重新连接状态: 启用=${status.enabled}, 管理器可用=${status.managerAvailable}`
                );
            } else {
                Logger.warn('WordConnectionController', '无法重新获取发音服务实例');
            }

        } catch (error) {
            Logger.error('WordConnectionController', '重新连接发音系统时发生异常', error as Error);
        }
    }

    /**
     * 更新视觉反馈
     * 统一处理连接过程中的视觉效果更新
     */
    private _updateVisualFeedback(): void {
        if (!this.stateManager || !this.connectionVisualizer) {
            Logger.error('WordConnectionController', `组件未初始化: stateManager=${!!this.stateManager}, connectionVisualizer=${!!this.connectionVisualizer}`);
            return;
        }

        const currentPath = this.stateManager.getCurrentPath();

        // 更新连接路径和高亮效果
        this.connectionVisualizer.updateConnectionPath(currentPath);
        this.connectionVisualizer.highlightCurrentPath(currentPath);
    }

    /**
     * 获取字母的世界坐标位置
     * @param letterPos 字母位置
     * @returns 世界坐标或null
     */
    private _getLetterWorldPosition(letterPos: LetterPosition): Vec3 | null {
        if (!this.letterGridController) {
            Logger.warn('WordConnectionController', 'LetterGridController未初始化');
            return null;
        }

        try {
            // 获取字母网格节点
            const gridNode = this.letterGridController.node;
            if (!gridNode) {
                Logger.warn('WordConnectionController', '字母网格节点未找到');
                return null;
            }

            // 根据网格结构查找字母节点：LetterGrid -> Row -> Cell
            const rowNode = gridNode.children[letterPos.row];
            if (!rowNode) {
                Logger.warn('WordConnectionController', `行节点未找到: ${letterPos.row}`);
                return null;
            }

            const cellNode = rowNode.children[letterPos.col];
            if (!cellNode) {
                Logger.warn('WordConnectionController', `单元格节点未找到: (${letterPos.row}, ${letterPos.col})`);
                return null;
            }

            // 获取单元格的世界坐标
            const worldPos = new Vec3();
            cellNode.getWorldPosition(worldPos);

            return worldPos;
        } catch (error) {
            Logger.error('WordConnectionController', '获取字母世界坐标失败', error as Error);
            return null;
        }
    }

    // ==================== 游戏状态查询接口组 ====================

    /**
     * 获取已完成的单词数量
     * @returns 已完成的单词数量
     */
    public getCompletedWordsCount(): number {
        if (!this.stateManager) return 0;
        return this.stateManager.getCompletedWordsCount();
    }

    /**
     * 获取总单词数量
     * @returns 总单词数量
     */
    public getTotalWordsCount(): number {
        if (!this.stateManager) return 0;
        return this.stateManager.getTargetWords().length;
    }

    /**
     * 检查单词是否已完成
     * @param word 单词
     * @returns 是否已完成
     */
    public isWordCompleted(word: string): boolean {
        if (!this.stateManager) return false;
        return this.stateManager.isWordCompleted(word.toUpperCase());
    }

    /**
     * 获取当前游戏统计信息
     * 提供完整的游戏进度和状态数据
     * @returns 游戏统计信息
     */
    public getGameStats(): GameStats {
        if (!this.stateManager) {
            return this._getEmptyGameStats();
        }

        const validationStats = this._wordValidator.getStats();
        const targetWords = this.stateManager.getTargetWords();
        const completedWords = this.stateManager.getCompletedWords();

        return {
            targetWords: targetWords.map(w => w.word.toUpperCase()),
            completedWords: Array.from(completedWords.keys()),
            totalWords: validationStats.totalWords,
            foundWords: validationStats.foundWords,
            progress: validationStats.progress,
            isCompleted: this.stateManager.isGameCompleted()
        };
    }

    /**
     * 获取空的游戏统计信息
     * 用于状态管理器未初始化时的默认返回值
     * @returns 空的游戏统计信息
     */
    private _getEmptyGameStats(): GameStats {
        return {
            targetWords: [],
            completedWords: [],
            totalWords: 0,
            foundWords: 0,
            progress: 0,
            isCompleted: false
        };
    }

    // ==================== 游戏控制接口组 ====================

    /**
     * 重置游戏状态
     * 清理所有游戏相关的状态和视觉效果
     */
    public resetGame(): void {
        if (this.stateManager) {
            this.stateManager.resetGame();
        }

        this._resetConnection();

        if (this.connectionVisualizer) {
            this.connectionVisualizer.clearAllHighlights();
        }

        // 重置验证器
        if (this._wordValidator) {
            this._wordValidator.reset();
        }

        // 重置单词区域的视觉状态
        if (this.wordAreaController) {
            this.wordAreaController.resetAllWordStatus();
        }

        // 🔧 重要：清理发音系统缓存，确保重新开始游戏后发音正常
        this._clearPronunciationCache();

        Logger.info('WordConnectionController', '游戏状态重置完成');
    }

    // ==================== 组件访问接口组 ====================

    /**
     * 获取单词验证器实例
     * 供其他组件访问验证器功能
     * @returns 单词验证器实例
     */
    public getWordValidator(): WordValidator {
        return this._wordValidator;
    }

    /**
     * 获取颜色主题管理器实例
     * 供其他组件访问颜色管理功能
     * @returns 颜色主题管理器实例
     */
    public getColorThemeManager(): ColorThemeManager {
        return this._colorThemeManager;
    }

    // ==================== 组件生命周期清理 ====================

    /**
     * 组件销毁时清理资源
     * 确保所有事件监听器和状态都被正确清理
     */
    onDestroy(): void {
        // 清理所有定时器
        this.unscheduleAllCallbacks();

        // 清理全局事件监听器
        this._cleanupGlobalEventListeners();

        // 清理字母事件处理器
        this._cleanupLetterEventHandlers();

        // 清理连线状态
        if (this.stateManager) {
            this.stateManager.resetGame();
        }
    }

    /**
     * 清理全局事件监听器
     * 移除所有注册的全局触摸事件监听器
     */
    private _cleanupGlobalEventListeners(): void {
        if (this.letterGridController && this.letterGridController.node && this.letterGridController.node.isValid) {
            const gridContainer = this.letterGridController.node;
            gridContainer.off(Node.EventType.TOUCH_START, this._onGlobalTouchStart, this);
            gridContainer.off(Node.EventType.TOUCH_MOVE, this._onGlobalTouchMove, this);
            gridContainer.off(Node.EventType.TOUCH_END, this._onGlobalTouchEnd, this);
            gridContainer.off(Node.EventType.TOUCH_CANCEL, this._onGlobalTouchEnd, this);
        }
    }

    /**
     * 清理字母事件处理器
     * 移除字母选择回调和相关事件监听器，防止内存泄漏
     */
    private _cleanupLetterEventHandlers(): void {
        // 清理字母事件处理器的回调
        if (this.letterEventHandler) {
            // 移除字母选择回调（设置为空函数而不是null）
            this.letterEventHandler.setLetterSelectionCallback(() => {});
            Logger.debug('WordConnectionController', '字母事件处理器回调已清理');
        }

        // 注意：不清理网格控制器的事件处理器引用，因为这会影响组件的正常功能
        // 字母事件处理器会在组件销毁时自动清理
    }

    /**
     * 重新初始化发音服务
     * 用于场景切换后确保发音服务连接正常
     */
    private _reinitializePronunciationService(): void {
        try {
            Logger.info('WordConnectionController', '开始重新初始化发音服务');

            // 🔧 重新获取发音服务实例
            this._pronunciationService = WordPronunciationService.getInstance();

            if (!this._pronunciationService) {
                Logger.warn('WordConnectionController', '发音服务实例不存在');
                return;
            }

            // 🔧 检查服务状态并触发重新连接
            const serviceStatus = this._pronunciationService.getServiceStatus();
            Logger.info('WordConnectionController',
                `发音服务状态: 启用=${serviceStatus.enabled}, 管理器可用=${serviceStatus.managerAvailable}`
            );

            // 🔧 如果管理器不可用，尝试触发重新连接
            if (!serviceStatus.managerAvailable) {
                Logger.info('WordConnectionController', '发音管理器不可用，尝试触发重新连接');

                // 尝试重新连接发音服务
                Logger.info('WordConnectionController', '发音管理器重新连接中...');
            } else {
                Logger.success('WordConnectionController', '发音服务状态正常');
            }

        } catch (error) {
            Logger.error('WordConnectionController', '重新初始化发音服务时发生异常', error as Error);
        }
    }


}

/**
 * 字母位置接口
 */
export interface LetterPosition {
    row: number;
    col: number;
}

/**
 * 游戏统计信息接口
 */
export interface GameStats {
    targetWords: string[];
    completedWords: string[];
    totalWords: number;
    foundWords: number;
    progress: number;
    isCompleted: boolean;
}
