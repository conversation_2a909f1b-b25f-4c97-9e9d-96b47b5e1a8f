import { Component, _decorator, Enum } from 'cc';
import { Logger } from '../Utils/Logger';
import { PronunciationApiClient, PronunciationResult } from '../Utils/PronunciationApiClient';
import { 
    DEFAULT_PRONUNCIATION_CONFIG, 
    PronunciationConfig,
    PRONUNCIATION_PERFORMANCE_CONFIG 
} from '../Game/Constants/PronunciationConstants';

const { ccclass, property } = _decorator;

/**
 * 单词发音管理器
 * 
 * 作为Core层的核心组件，负责管理整个发音功能的全局状态和配置。
 * 遵循单一职责原则，专注于发音功能的统一管理和协调。
 * 
 * 核心职责：
 * - 发音功能的全局配置管理
 * - 发音服务的初始化和生命周期管理
 * - 提供统一的发音接口给其他系统
 * - 处理发音功能的启用/禁用状态
 * 
 * 设计特点：
 * - 单例模式，确保全局唯一性
 * - 持久化节点，跨场景保持状态
 * - 配置驱动，支持运行时调整
 * - 事件驱动，与其他系统解耦
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22
 */
@ccclass('WordPronunciationManager')
export class WordPronunciationManager extends Component {

    // ==================== 编辑器配置属性 ====================

    /**
     * 是否启用发音功能
     */
    @property({ displayName: "启用发音功能", tooltip: "是否启用单词发音播放功能" })
    enablePronunciation: boolean = true;

    /**
     * 发音音量控制
     */
    @property({ displayName: "发音音量", tooltip: "单词发音的音量大小", range: [0, 1, 0.1] })
    pronunciationVolume: number = 0.8;

    /**
     * 发音类型选择
     */
    @property({
        type: Enum({
            British: 0,
            American: 1
        }),
        displayName: "发音类型",
        tooltip: "选择英式发音或美式发音"
    })
    pronunciationType: number = 1; // 默认美式发音

    /**
     * 单例实例
     */
    private static _instance: WordPronunciationManager | null = null;
    
    /**
     * 发音配置
     */
    private _config: PronunciationConfig = { ...DEFAULT_PRONUNCIATION_CONFIG };
    
    /**
     * 是否已初始化
     */
    private _initialized: boolean = false;
    
    /**
     * 发音统计信息
     */
    private _statistics = {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0
    };

    /**
     * 获取单例实例
     * @returns WordPronunciationManager实例或null
     */
    public static getInstance(): WordPronunciationManager | null {
        return WordPronunciationManager._instance;
    }

    /**
     * 组件加载时的初始化
     */
    onLoad(): void {
        // 单例模式实现
        if (WordPronunciationManager._instance === null) {
            WordPronunciationManager._instance = this;
            this._initializeManager();
            Logger.info('WordPronunciationManager', '单词发音管理器初始化完成');
        } else {
            // 销毁重复实例
            Logger.warn('WordPronunciationManager', '检测到重复实例，销毁当前节点');
            this.node.destroy();
            return;
        }
    }

    /**
     * 组件启动时的设置
     */
    start(): void {
        if (WordPronunciationManager._instance === this) {
            this._setupPronunciationSystem();
        }
    }

    /**
     * 组件销毁时的清理
     */
    onDestroy(): void {
        // 检查是否在持久化节点中，如果是则不清理单例实例
        if (this.node.parent && this.node.parent.name === '管理器') {
            Logger.info('WordPronunciationManager', '发音管理器在持久化节点中，跳过单例清理');
            return;
        }

        if (WordPronunciationManager._instance === this) {
            this._cleanup();
            WordPronunciationManager._instance = null;
            Logger.info('WordPronunciationManager', '单词发音管理器已清理');
        }
    }

    /**
     * 播放单词发音
     * @param word 要发音的单词
     * @returns 发音结果Promise
     */
    public async playWordPronunciation(word: string): Promise<boolean> {
        // 检查功能是否启用
        if (!this._config.enabled) {
            Logger.debug('WordPronunciationManager', '发音功能已禁用，跳过播放');
            return false;
        }

        // 检查初始化状态，支持场景切换后的重新初始化
        if (!this._initialized) {
            Logger.warn('WordPronunciationManager', '发音管理器未初始化，尝试重新初始化');
            this._initializeManager();
            if (!this._initialized) {
                Logger.error('WordPronunciationManager', '重新初始化失败');
                return false;
            }
        }

        try {
            this._statistics.totalRequests++;
            const startTime = Date.now();

            Logger.info('WordPronunciationManager', `开始播放单词 "${word}" 的发音`);

            // 调用API客户端播放发音（传递音量参数）
            const result: PronunciationResult = await PronunciationApiClient.playWordPronunciation(
                word,
                this._config.type,
                this._config.volume
            );

            // 更新统计信息
            const responseTime = Date.now() - startTime;
            this._updateStatistics(result.success, responseTime);

            if (result.success) {
                Logger.success('WordPronunciationManager', `单词 "${word}" 发音播放成功`);
                return true;
            } else {
                Logger.warn('WordPronunciationManager', `单词 "${word}" 发音播放失败: ${result.errorMessage}`);
                return false;
            }

        } catch (error) {
            this._statistics.failedRequests++;
            Logger.error('WordPronunciationManager', `播放单词 "${word}" 发音时发生异常`, error as Error);
            return false;
        }
    }

    /**
     * 批量预加载单词发音
     * @param words 单词数组
     * @returns 预加载结果Promise
     */
    public async preloadWordPronunciations(words: string[]): Promise<boolean> {
        if (!this._config.enabled || !this._config.enableCache) {
            return false;
        }

        if (!Array.isArray(words) || words.length === 0) {
            Logger.warn('WordPronunciationManager', '预加载单词列表为空');
            return false;
        }

        try {
            Logger.info('WordPronunciationManager', `开始预加载${words.length}个单词的发音`);

            const results = await PronunciationApiClient.preloadWordPronunciations(words, this._config.type);
            const successCount = Array.from(results.values()).filter(success => success).length;
            
            Logger.info('WordPronunciationManager', `预加载完成: ${successCount}/${words.length} 成功`);
            return successCount > 0;

        } catch (error) {
            Logger.error('WordPronunciationManager', '批量预加载发音失败', error as Error);
            return false;
        }
    }

    /**
     * 更新发音配置
     * @param newConfig 新的配置
     */
    public updateConfig(newConfig: Partial<PronunciationConfig>): void {
        const oldConfig = { ...this._config };
        this._config = { ...this._config, ...newConfig };

        Logger.info('WordPronunciationManager', '发音配置已更新');

        // 如果启用状态发生变化，记录日志
        if (oldConfig.enabled !== this._config.enabled) {
            Logger.info('WordPronunciationManager', `发音功能${this._config.enabled ? '已启用' : '已禁用'}`);
        }
    }

    /**
     * 获取当前配置
     * @returns 当前发音配置
     */
    public getConfig(): PronunciationConfig {
        return { ...this._config };
    }

    /**
     * 获取统计信息
     * @returns 发音统计数据
     */
    public getStatistics(): typeof WordPronunciationManager.prototype._statistics {
        return { ...this._statistics };
    }

    /**
     * 检查发音功能是否可用
     * @returns 是否可用
     */
    public isAvailable(): boolean {
        return this._initialized && this._config.enabled;
    }

    /**
     * 重置统计信息
     */
    public resetStatistics(): void {
        this._statistics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0
        };
        Logger.info('WordPronunciationManager', '发音统计信息已重置');
    }

    /**
     * 强制重新初始化发音管理器
     * 用于场景切换后确保功能正常
     */
    public forceReinitialize(): void {
        Logger.info('WordPronunciationManager', '开始强制重新初始化');

        try {
            // 🔧 强制重置API客户端状态
            PronunciationApiClient.reset();
            Logger.info('WordPronunciationManager', 'API客户端状态已重置');

            // 重置初始化状态
            this._initialized = false;

            // 🔧 延迟重新初始化，确保重置完成
            this.scheduleOnce(() => {
                this._initializeManager();

                if (this._initialized) {
                    Logger.success('WordPronunciationManager', '强制重新初始化成功');

                    // 🔧 验证API客户端状态
                    const apiStatus = PronunciationApiClient.getStatus();
                    Logger.info('WordPronunciationManager', `API客户端状态: 已初始化=${apiStatus.initialized}, 活跃请求=${apiStatus.activeRequests}`);
                } else {
                    Logger.error('WordPronunciationManager', '强制重新初始化失败');
                }
            }, 0.1); // 延迟100ms确保重置完成

        } catch (error) {
            Logger.error('WordPronunciationManager', '强制重新初始化时发生异常', error as Error);
        }
    }

    /**
     * 初始化管理器
     */
    private _initializeManager(): void {
        try {
            // 初始化API客户端
            PronunciationApiClient.initialize();
            
            this._initialized = true;
            Logger.info('WordPronunciationManager', '发音管理器内部初始化完成');

        } catch (error) {
            this._initialized = false;
            Logger.error('WordPronunciationManager', '发音管理器初始化失败', error as Error);
        }
    }

    /**
     * 设置发音系统（应用编辑器配置）
     */
    private _setupPronunciationSystem(): void {
        try {
            // 应用编辑器配置到发音配置
            this._config.enabled = this.enablePronunciation;
            this._config.volume = this.pronunciationVolume;
            this._config.type = this.pronunciationType;

            Logger.info('WordPronunciationManager', `发音系统设置完成 - 启用: ${this._config.enabled}`);

        } catch (error) {
            Logger.error('WordPronunciationManager', '发音系统设置失败', error as Error);
        }
    }

    /**
     * 更新统计信息
     * @param success 是否成功
     * @param responseTime 响应时间
     */
    private _updateStatistics(success: boolean, responseTime: number): void {
        if (success) {
            this._statistics.successfulRequests++;
        } else {
            this._statistics.failedRequests++;
        }

        // 计算平均响应时间
        const totalSuccessful = this._statistics.successfulRequests;
        if (totalSuccessful > 0) {
            this._statistics.averageResponseTime = 
                (this._statistics.averageResponseTime * (totalSuccessful - 1) + responseTime) / totalSuccessful;
        }
    }

    /**
     * 清理资源
     */
    private _cleanup(): void {
        try {
            // 重置API客户端
            PronunciationApiClient.reset();
            
            // 清理统计信息
            this.resetStatistics();
            
            this._initialized = false;
            
            Logger.info('WordPronunciationManager', '发音管理器资源清理完成');

        } catch (error) {
            Logger.error('WordPronunciationManager', '发音管理器清理失败', error as Error);
        }
    }

    /**
     * 获取详细状态信息（用于调试）
     */
    public getDetailedStatus(): {
        initialized: boolean;
        config: PronunciationConfig;
        statistics: typeof WordPronunciationManager.prototype._statistics;
        apiClientStatus: ReturnType<typeof PronunciationApiClient.getStatus>;
    } {
        return {
            initialized: this._initialized,
            config: this.getConfig(),
            statistics: this.getStatistics(),
            apiClientStatus: PronunciationApiClient.getStatus()
        };
    }
}
