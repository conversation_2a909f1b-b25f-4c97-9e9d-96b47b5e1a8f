import { _decorator, Component, Node, Label, Color, tween } from 'cc';
import { AnimationManager } from '../Animation/AnimationManager';
import { ColorThemeManager } from '../Managers/ColorThemeManager';
import { WordTranslationDisplay } from './WordTranslationDisplay';
import { Logger } from '../../Utils/Logger';

const { ccclass, property } = _decorator;

/**
 * 单词完成动画器 - 专门负责单词完成时的动画效果
 * 遵循单一职责原则，只负责单词完成动画的播放和管理
 * 集成统一动画管理系统，确保动画的一致性和性能
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-09
 */
@ccclass('WordCompletionAnimator')
export class WordCompletionAnimator extends Component {

    /**
     * 动画配置常量
     */
    private static readonly ANIMATION_CONFIG = {
        /** 缩放动画持续时间（秒） */
        SCALE_DURATION: 0.4,
        /** 颜色过渡动画持续时间（秒） */
        COLOR_DURATION: 0.3,
        /** 最大缩放倍数 */
        MAX_SCALE: 1.5,
        /** 原始缩放倍数 */
        ORIGINAL_SCALE: 1.0,
        /** 缓动函数类型 */
        EASING: 'sineOut',
        /** 动画延迟（秒） */
        ANIMATION_DELAY: 0.1,
        /** 是否启用动画（可配置） */
        ENABLED: true
    } as const;

    /**
     * 颜色主题管理器实例
     */
    private _colorThemeManager: ColorThemeManager = null!;

    /**
     * 动画管理器实例
     */
    private _animationManager: AnimationManager = null!;

    /**
     * 当前活跃的动画记录
     * Key: 单词, Value: 动画ID数组
     */
    private _activeAnimations: Map<string, string[]> = new Map();

    /**
     * 翻译显示组件引用
     */
    @property(WordTranslationDisplay)
    translationDisplay: WordTranslationDisplay = null!;



    /**
     * 是否启用翻译显示功能
     */
    @property({ displayName: "启用翻译显示", tooltip: "是否在单词完成后显示翻译" })
    enableTranslation: boolean = true;

    /**
     * 组件初始化
     */
    onLoad(): void {
        this._initializeManagers();
    }

    /**
     * 初始化管理器实例
     */
    private _initializeManagers(): void {
        // 获取颜色主题管理器单例实例
        this._colorThemeManager = ColorThemeManager.getInstance();
        if (!this._colorThemeManager) {
            Logger.error('WordCompletionAnimator', '无法获取ColorThemeManager实例');
            return;
        }

        // 获取动画管理器单例实例
        this._animationManager = AnimationManager.getInstance();
        if (!this._animationManager) {
            Logger.error('WordCompletionAnimator', '无法获取AnimationManager实例');
            return;
        }

        Logger.info('WordCompletionAnimator', '单词完成动画器初始化完成');
    }

    /**
     * 播放单词完成动画
     * 包含缩放动画和颜色过渡动画的组合效果
     * @param wordNode 单词节点
     * @param word 单词文本
     * @param onComplete 动画完成回调
     * @returns 是否成功开始播放动画
     */
    public playWordCompletionAnimation(
        wordNode: Node, 
        word: string, 
        onComplete?: () => void
    ): boolean {
        if (!this._validateInputs(wordNode, word)) {
            return false;
        }

        if (!WordCompletionAnimator.ANIMATION_CONFIG.ENABLED) {
            Logger.debug('WordCompletionAnimator', '动画已禁用，跳过播放');
            this._applyCompletedState(wordNode, word);
            if (onComplete) onComplete();
            return true;
        }

        // 停止该单词的现有动画
        this._stopWordAnimations(word);

        // 获取Label组件
        const label = wordNode.getComponent(Label);
        if (!label) {
            Logger.error('WordCompletionAnimator', `单词节点 "${word}" 缺少Label组件`);
            return false;
        }

        // 播放组合动画
        return this._playComboAnimation(wordNode, label, word, onComplete);
    }

    /**
     * 播放组合动画（缩放 + 颜色过渡）
     * @param wordNode 单词节点
     * @param label Label组件
     * @param word 单词文本
     * @param onComplete 完成回调
     * @returns 是否成功播放
     */
    private _playComboAnimation(
        wordNode: Node, 
        label: Label, 
        word: string, 
        onComplete?: () => void
    ): boolean {
        const animationIds: string[] = [];
        let completedAnimations = 0;
        const totalAnimations = 2; // 缩放 + 颜色

        // 动画完成计数器
        const onAnimationComplete = () => {
            completedAnimations++;

            if (completedAnimations >= totalAnimations) {
                // 所有动画完成
                this._activeAnimations.delete(word);
                Logger.debug('WordCompletionAnimator', `单词 "${word}" 完成动画全部结束`);

                if (onComplete) onComplete();
            }
        };

        // 1. 播放缩放动画
        const scaleAnimationId = this._animationManager.playScaleAnimation({
            target: wordNode,
            targetScale: WordCompletionAnimator.ANIMATION_CONFIG.MAX_SCALE,
            config: {
                duration: WordCompletionAnimator.ANIMATION_CONFIG.SCALE_DURATION,
                easing: WordCompletionAnimator.ANIMATION_CONFIG.EASING,
                yoyo: true, // 来回播放：1.0 → 1.5 → 1.0
                repeat: 0,  // 不额外重复，yoyo本身就是一次完整的来回
                onComplete: onAnimationComplete
            }
        });

        if (scaleAnimationId) {
            animationIds.push(scaleAnimationId);
        } else {
            Logger.warn('WordCompletionAnimator', `单词 "${word}" 缩放动画播放失败`);
            onAnimationComplete(); // 仍然计数，避免卡住
        }

        // 2. 播放颜色过渡动画（稍微延迟）
        setTimeout(() => {
            try {
                const colorAnimationId = this._animationManager.playColorAnimation({
                    target: label,
                    targetColor: this._colorThemeManager.getCompletedTextColor(),
                    config: {
                        duration: WordCompletionAnimator.ANIMATION_CONFIG.COLOR_DURATION,
                        easing: WordCompletionAnimator.ANIMATION_CONFIG.EASING,
                        onComplete: onAnimationComplete
                    }
                });

                if (colorAnimationId) {
                    animationIds.push(colorAnimationId);
                } else {
                    Logger.warn('WordCompletionAnimator', `单词 "${word}" 颜色动画播放失败`);
                    // 回退到即时颜色更新
                    label.color = this._colorThemeManager.getCompletedTextColor();
                    onAnimationComplete();
                }
            } catch (error) {
                Logger.error('WordCompletionAnimator', `单词 "${word}" 动画异常`, error as Error);
                // 异常时回退到即时更新
                label.color = this._colorThemeManager.getCompletedTextColor();
                onAnimationComplete();
            }
        }, WordCompletionAnimator.ANIMATION_CONFIG.ANIMATION_DELAY * 1000);

        // 记录活跃动画
        if (animationIds.length > 0) {
            this._activeAnimations.set(word, animationIds);
            return true;
        }

        return false;
    }

    /**
     * 停止指定单词的所有动画
     * @param word 单词文本
     */
    public stopWordAnimations(word: string): void {
        this._stopWordAnimations(word);
    }

    /**
     * 停止指定单词的所有动画（内部方法）
     * @param word 单词文本
     */
    private _stopWordAnimations(word: string): void {
        const animationIds = this._activeAnimations.get(word);
        if (animationIds && animationIds.length > 0) {
            animationIds.forEach(id => {
                this._animationManager.stopAnimation(id);
            });
            this._activeAnimations.delete(word);
            Logger.debug('WordCompletionAnimator', `停止单词 "${word}" 的所有动画`);
        }
    }

    /**
     * 停止所有动画
     */
    public stopAllAnimations(): void {
        for (const [word, animationIds] of this._activeAnimations) {
            animationIds.forEach(id => {
                this._animationManager.stopAnimation(id);
            });
        }
        this._activeAnimations.clear();
        Logger.debug('WordCompletionAnimator', '停止所有单词完成动画');
    }

    /**
     * 直接应用完成状态（无动画）
     * @param wordNode 单词节点
     * @param word 单词文本
     */
    private _applyCompletedState(wordNode: Node, word: string): void {
        const label = wordNode.getComponent(Label);
        if (label) {
            label.color = this._colorThemeManager.getCompletedTextColor();
            Logger.debug('WordCompletionAnimator', `直接应用单词 "${word}" 完成状态`);
        }
    }

    /**
     * 验证输入参数
     * @param wordNode 单词节点
     * @param word 单词文本
     * @returns 是否有效
     */
    private _validateInputs(wordNode: Node, word: string): boolean {
        if (!wordNode) {
            Logger.error('WordCompletionAnimator', '单词节点不能为空');
            return false;
        }

        if (!word || word.trim().length === 0) {
            Logger.error('WordCompletionAnimator', '单词文本不能为空');
            return false;
        }

        if (!this._animationManager || !this._colorThemeManager) {
            Logger.error('WordCompletionAnimator', '管理器实例未初始化');
            return false;
        }

        return true;
    }

    /**
     * 播放完整的单词完成动画序列（包含翻译）
     * @param word 完成的单词
     * @param wordNode 单词节点
     * @param wordIndex 单词索引
     */
    public async playWordCompletionWithTranslation(
        word: string,
        wordNode: Node,
        wordIndex: number
    ): Promise<void> {
        try {
            // 参数验证和调试日志
            Logger.info('WordCompletionAnimator', `开始播放完整动画序列: ${word} (类型: ${typeof word})`);

            if (!word || typeof word !== 'string') {
                Logger.error('WordCompletionAnimator', `无效的单词参数: ${typeof word}, 值: ${word}`);
                return;
            }

            if (!wordNode || !wordNode.isValid) {
                Logger.error('WordCompletionAnimator', '单词节点无效');
                return;
            }

            // 1. 播放现有的缩放动画
            const animationSuccess = this.playWordCompletionAnimation(wordNode, word);
            if (!animationSuccess) {
                Logger.warn('WordCompletionAnimator', `缩放动画启动失败: ${word}`);
                return;
            }

            // 等待动画完成（使用Promise包装）
            await this._waitForAnimationComplete();

            // 2. 缩放动画完成后，显示翻译
            if (this.enableTranslation && this.translationDisplay) {
                Logger.info('WordCompletionAnimator', `开始显示翻译: ${word}`);
                await this.translationDisplay.showTranslation(word, wordNode);
                Logger.info('WordCompletionAnimator', `翻译显示完成: ${word}`);
            } else if (this.enableTranslation && !this.translationDisplay) {
                Logger.warn('WordCompletionAnimator', '翻译显示组件未配置');
            }

            Logger.success('WordCompletionAnimator', `完整动画序列完成: ${word}`);
        } catch (error) {
            Logger.error('WordCompletionAnimator', `单词完成动画序列失败: ${word}`, error as Error);
        }
    }

    /**
     * 等待动画完成
     * @returns Promise
     */
    private _waitForAnimationComplete(): Promise<void> {
        return new Promise(resolve => {
            // 等待缩放动画的持续时间
            const animationDuration = WordCompletionAnimator.ANIMATION_CONFIG.SCALE_DURATION * 1000;
            setTimeout(resolve, animationDuration);
        });
    }

    /**
     * 获取当前活跃动画数量
     * @returns 活跃动画数量
     */
    public getActiveAnimationCount(): number {
        let totalAnimations = 0;
        for (const animationIds of this._activeAnimations.values()) {
            totalAnimations += animationIds.length;
        }
        return totalAnimations;
    }

    /**
     * 组件销毁时清理
     */
    onDestroy(): void {
        this.stopAllAnimations();
        Logger.debug('WordCompletionAnimator', '单词完成动画器已销毁');
    }
}
