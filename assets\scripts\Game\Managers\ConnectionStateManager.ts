import { _decorator, Component } from 'cc';
import { LetterPosition } from '../../Game/Constants/GameConstants';
import { IWordData } from '../../Data/WordDatabase';

const { ccclass } = _decorator;

/**
 * 连线状态管理器 - 专门管理连线状态和路径数据
 * 遵循单一职责原则，专注于状态管理功能
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-05
 */
@ccclass('ConnectionStateManager')
export class ConnectionStateManager extends Component {

    /**
     * 当前连线状态
     */
    private _isConnecting: boolean = false;

    /**
     * 当前连线路径
     */
    private _currentPath: LetterPosition[] = [];

    /**
     * 已完成的单词连线
     */
    private _completedWords: Map<string, LetterPosition[]> = new Map();

    /**
     * 目标单词列表
     */
    private _targetWords: IWordData[] = [];

    /**
     * 游戏完成状态
     */
    private _isGameCompleted: boolean = false;

    /**
     * 已完成单词数量
     */
    private _completedWordCount: number = 0;

    /**
     * 组件初始化
     */
    onLoad(): void {
        // 连线状态管理器初始化
    }

    /**
     * 设置目标单词
     * @param words 目标单词数组
     */
    public setTargetWords(words: IWordData[]): void {
        this._targetWords = [...words];
    }

    /**
     * 获取目标单词列表
     * @returns 目标单词数组
     */
    public getTargetWords(): IWordData[] {
        return [...this._targetWords];
    }

    /**
     * 开始连线
     * @param startPos 起始位置
     */
    public startConnection(startPos: LetterPosition): void {
        this._isConnecting = true;
        this._currentPath = [startPos];
        
    }

    /**
     * 添加位置到当前路径
     * @param position 字母位置
     */
    public addToPath(position: LetterPosition): void {
        // 检查是否已在路径中，避免重复添加
        if (this._isPositionInCurrentPath(position)) {
            return;
        }

        this._currentPath.push(position);
    }

    /**
     * 检查位置是否可以添加到路径
     * @param position 字母位置
     * @returns 是否可以添加
     */
    public canAddToPath(position: LetterPosition): boolean {
        // 检查是否已在当前路径中
        if (this._isPositionInCurrentPath(position)) {
            return false;
        }

        // 如果路径为空，可以添加任何位置
        if (this._currentPath.length === 0) {
            return true;
        }

        // 检查是否与路径中最后一个位置相邻
        const lastPos = this._currentPath[this._currentPath.length - 1];
        return this._arePositionsAdjacent(lastPos, position);
    }

    /**
     * 检查位置是否在当前路径中
     * @param position 字母位置
     * @returns 是否在路径中
     */
    private _isPositionInCurrentPath(position: LetterPosition): boolean {
        return this._currentPath.some(pos => pos.row === position.row && pos.col === position.col);
    }

    /**
     * 检查两个位置是否相邻（8方向）
     * @param pos1 位置1
     * @param pos2 位置2
     * @returns 是否相邻
     */
    private _arePositionsAdjacent(pos1: LetterPosition, pos2: LetterPosition): boolean {
        const rowDiff = Math.abs(pos1.row - pos2.row);
        const colDiff = Math.abs(pos1.col - pos2.col);
        
        // 8方向相邻：行差和列差都不超过1，且不能是同一位置
        return rowDiff <= 1 && colDiff <= 1 && !(rowDiff === 0 && colDiff === 0);
    }

    /**
     * 完成单词连线
     * @param word 完成的单词
     */
    public completeWord(word: string): void {
        const upperWord = word.toUpperCase();
        this._completedWords.set(upperWord, [...this._currentPath]);
        this._completedWordCount++;
        
        // 检查游戏是否完成
        this._checkGameCompletion();
        
    }

    /**
     * 检查游戏是否完成
     */
    private _checkGameCompletion(): void {
        if (this._completedWordCount >= this._targetWords.length) {
            this._isGameCompleted = true;
        }
    }

    /**
     * 重置连线状态
     */
    public resetConnection(): void {
        this._isConnecting = false;
        this._currentPath = [];
    }

    /**
     * 获取当前连接的单词（已弃用，使用WordValidator.findValidWordFromPath代替）
     * @param letterGridController 字母网格控制器
     * @returns 连接的单词字符串
     * @deprecated 使用WordValidator.findValidWordFromPath代替
     */
    public getConnectedWord(letterGridController: any): string {
        let word = '';
        for (const pos of this._currentPath) {
            const letter = letterGridController.getLetterAt(pos.row, pos.col);
            if (letter) {
                word += letter;
            }
        }
        return word.toUpperCase();
    }

    /**
     * 检查当前路径是否与已完成的单词路径重叠
     * @returns 是否存在路径重叠
     */
    public hasPathOverlapWithCompletedWords(): boolean {
        const currentPathSet = new Set(this._currentPath.map(pos => `${pos.row}-${pos.col}`));

        for (const [word, path] of this._completedWords) {
            const completedPathSet = new Set(path.map(pos => `${pos.row}-${pos.col}`));

            // 检查是否有重叠（除了可能的共享起始字母）
            let overlapCount = 0;
            for (const posKey of currentPathSet) {
                if (completedPathSet.has(posKey)) {
                    overlapCount++;
                }
            }

            // 如果重叠超过1个位置，或者完全重叠，则认为有冲突
            if (overlapCount > 1 || (overlapCount === currentPathSet.size && overlapCount === completedPathSet.size)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查字母是否在已完成的单词中
     * @param position 字母位置
     * @returns 是否在已完成单词中
     */
    public isLetterInCompletedWord(position: LetterPosition): boolean {
        for (const [word, path] of this._completedWords) {
            if (path.some(pos => pos.row === position.row && pos.col === position.col)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查位置是否可以作为新单词的起始点（支持共享字母）
     * @param position 字母位置
     * @returns 是否可以作为起始点
     */
    public canStartNewWordAt(position: LetterPosition): boolean {
        // 即使字母已经在完成的单词中，也允许作为新单词的起始点
        // 这支持了共享字母的场景
        return true;
    }

    /**
     * 获取指定位置参与的所有已完成单词
     * @param position 字母位置
     * @returns 包含该位置的单词列表
     */
    public getCompletedWordsAtPosition(position: LetterPosition): string[] {
        const words: string[] = [];
        for (const [word, path] of this._completedWords) {
            if (path.some(pos => pos.row === position.row && pos.col === position.col)) {
                words.push(word);
            }
        }
        return words;
    }

    /**
     * 获取当前连线状态
     * @returns 是否正在连线
     */
    public isConnecting(): boolean {
        return this._isConnecting;
    }

    /**
     * 获取当前连线路径
     * @returns 当前路径
     */
    public getCurrentPath(): LetterPosition[] {
        return [...this._currentPath];
    }

    /**
     * 获取已完成的单词
     * @returns 已完成单词的映射
     */
    public getCompletedWords(): Map<string, LetterPosition[]> {
        return new Map(this._completedWords);
    }

    /**
     * 获取游戏完成状态
     * @returns 是否游戏完成
     */
    public isGameCompleted(): boolean {
        return this._isGameCompleted;
    }

    /**
     * 获取已完成单词数量
     * @returns 已完成单词数量
     */
    public getCompletedWordCount(): number {
        return this._completedWordCount;
    }

    /**
     * 获取游戏进度百分比
     * @returns 进度百分比 (0-100)
     */
    public getProgressPercentage(): number {
        if (this._targetWords.length === 0) return 0;
        return Math.round((this._completedWordCount / this._targetWords.length) * 100);
    }

    /**
     * 重置游戏状态
     */
    public resetGame(): void {
        this._isConnecting = false;
        this._currentPath = [];
        this._completedWords.clear();
        this._isGameCompleted = false;
        this._completedWordCount = 0;
        
    }

    /**
     * 获取状态管理器的详细状态信息
     * @returns 状态信息对象
     */
    public getDetailedStatus(): {
        isConnecting: boolean;
        currentPathLength: number;
        completedWordCount: number;
        totalWordCount: number;
        isGameCompleted: boolean;
        progressPercentage: number;
    } {
        return {
            isConnecting: this._isConnecting,
            currentPathLength: this._currentPath.length,
            completedWordCount: this._completedWordCount,
            totalWordCount: this._targetWords.length,
            isGameCompleted: this._isGameCompleted,
            progressPercentage: this.getProgressPercentage()
        };
    }
}
