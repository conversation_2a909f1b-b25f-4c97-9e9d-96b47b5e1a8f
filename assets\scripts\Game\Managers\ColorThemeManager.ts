import { Color } from 'cc';
import { IWordData } from '../../Data/WordDatabase';
import { Logger } from '../../Utils/Logger';

/**
 * 颜色主题管理器
 *
 * 负责管理游戏中所有颜色主题的统一分配和管理，采用马卡龙色彩风格，
 * 为不同的单词分配独特且美观的颜色。使用单例模式确保全局颜色一致性。
 *
 * 核心功能：
 * - 马卡龙色彩主题管理：提供8种精心设计的马卡龙色彩
 * - 单词颜色分配：为每个单词分配唯一的颜色标识
 * - UI颜色统一管理：管理所有UI交互相关的颜色
 * - 颜色可访问性支持：确保颜色对比度符合可访问性标准
 * - 颜色状态管理：跟踪颜色使用状态，避免重复分配
 *
 * 马卡龙色彩方案：
 * - 柔和粉色：温暖而友好的粉色调
 * - 薄荷绿色：清新自然的绿色调
 * - 淡紫色：优雅神秘的紫色调
 * - 桃色：活泼可爱的橙粉色调
 * - 天空蓝：宁静舒适的蓝色调
 * - 柠檬黄：明亮活力的黄色调
 * - 珊瑚橙：温暖热情的橙色调
 * - 薰衣草紫：浪漫梦幻的紫色调
 *
 * 技术特性：
 * - 单例模式确保全局颜色一致性
 * - 智能颜色分配算法，避免相邻单词颜色相似
 * - 颜色对比度计算，确保文字可读性
 * - 颜色状态缓存，提升性能
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-04
 */
export class ColorThemeManager {

    /**
     * 单例实例
     */
    private static _instance: ColorThemeManager | null = null;

    /**
     * UI交互颜色定义 - 统一管理所有UI相关颜色
     * 遵循单一职责原则，集中化颜色管理
     */
    private static readonly UI_COLORS = {
        /** 临时高亮颜色 - 连接过程中的黄色高亮 */
        TEMPORARY_HIGHLIGHT: new Color(255, 255, 0, 200), // 明亮的黄色，半透明

        /** 失败高亮颜色 - 连接失败时的红色高亮 */
        FAILURE_HIGHLIGHT: new Color(255, 0, 0, 255), // 纯红色，完全不透明

        /** 成功指示颜色 - 成功操作的绿色指示 */
        SUCCESS_INDICATOR: new Color(0, 255, 0, 255), // 纯绿色，完全不透明

        /** 默认背景颜色 - 字母的默认背景色 */
        DEFAULT_BACKGROUND: new Color(255, 255, 255, 255), // 纯白色，完全不透明

        /** 默认文本颜色 - 未完成单词的文本颜色 */
        DEFAULT_TEXT: new Color(0, 0, 0, 255), // 纯黑色，完全不透明

        /** 已完成文本颜色 - 已完成单词的文本颜色 */
        COMPLETED_TEXT: new Color(128, 128, 128, 255), // 中等灰色，良好对比度

        /** 连线颜色 - 连接过程中的连线颜色 */
        CONNECTION_LINE: new Color(255, 215, 0, 217), // 金色，85%透明度

        /** 连线光效颜色 - 连线的光效颜色 */
        CONNECTION_GLOW: new Color(255, 255, 255, 128), // 白色光效，50%透明度

        /** 字母高亮颜色 - 被连接字母的高亮颜色 */
        LETTER_HIGHLIGHT: new Color(255, 255, 0, 153), // 黄色高亮，60%透明度

        /** 成功反馈颜色 - 连接成功时的反馈颜色 */
        SUCCESS_FEEDBACK: new Color(0, 255, 127, 255), // 春绿色，完全不透明

        /** 失败反馈颜色 - 连接失败时的反馈颜色 */
        FAILURE_FEEDBACK: new Color(255, 69, 0, 255) // 橙红色，完全不透明
    } as const;

    /**
     * 马卡龙色彩主题调色板 - 鲜艳明亮版本（优化色彩区分度）
     * 8种鲜艳且区分明显的马卡龙颜色，避免相似色彩混淆
     */
    private static readonly MACARON_COLORS: MacaronColor[] = [
        {
            name: '樱花粉',
            color: new Color(255, 105, 180, 255), // Hot Pink - 鲜艳粉色
            description: '鲜艳浪漫的樱花粉'
        },
        {
            name: '翡翠绿',
            color: new Color(50, 205, 50, 255), // Lime Green - 活力绿色
            description: '清新活力的翡翠绿'
        },
        {
            name: '紫水晶',
            color: new Color(153, 102, 255, 255), // Medium Slate Blue - 明亮紫色，确保可读性
            description: '明亮优雅的紫水晶色'
        },
        {
            name: '蜜桃橙',
            color: new Color(255, 140, 0, 255), // Dark Orange - 温暖橙色
            description: '温暖甜美的蜜桃橙'
        },
        {
            name: '海洋蓝',
            color: new Color(30, 144, 255, 255), // Dodger Blue - 深邃蓝色
            description: '深邃清澈的海洋蓝'
        },
        {
            name: '阳光黄',
            color: new Color(255, 215, 0, 255), // Gold - 明亮黄色
            description: '明亮活泼的阳光黄'
        },
        {
            name: '珊瑚红',
            color: new Color(255, 99, 71, 255), // Tomato - 活力红色
            description: '温暖活力的珊瑚红'
        },
        {
            name: '青绿色',
            color: new Color(0, 206, 209, 255), // Dark Turquoise - 青绿色，替代薰衣草紫
            description: '清新独特的青绿色'
        }
    ];

    /**
     * 单词到颜色的映射
     */
    private _wordColorMap: Map<string, MacaronColor> = new Map();

    /**
     * 已使用的颜色索引
     */
    private _usedColorIndices: Set<number> = new Set();

    /**
     * 私有构造函数，防止外部直接实例化
     */
    private constructor() {
        this._validateColorPalette();
    }

    /**
     * 获取单例实例
     * @returns ColorThemeManager实例
     */
    public static getInstance(): ColorThemeManager {
        if (ColorThemeManager._instance === null) {
            ColorThemeManager._instance = new ColorThemeManager();
            Logger.info('ColorThemeManager', '颜色主题管理器单例实例创建完成');
        }
        return ColorThemeManager._instance;
    }

    /**
     * 验证颜色调色板
     */
    private _validateColorPalette(): void {
        // 静默验证，仅在错误时输出
        if (ColorThemeManager.MACARON_COLORS.length === 0) {
            Logger.error('ColorThemeManager', '马卡龙色彩调色板为空');
        }
    }

    /**
     * 为单词分配颜色主题
     * @param words 单词数组
     */
    public assignColorsToWords(words: IWordData[]): void {
        this._wordColorMap.clear();
        this._usedColorIndices.clear();

        if (words.length > ColorThemeManager.MACARON_COLORS.length) {
            Logger.warn('ColorThemeManager', `单词数量(${words.length})超过可用颜色数量(${ColorThemeManager.MACARON_COLORS.length})`);
        }

        // 为每个单词分配唯一的颜色
        words.forEach((word, index) => {
            const colorIndex = index % ColorThemeManager.MACARON_COLORS.length;
            const colorInfo = ColorThemeManager.MACARON_COLORS[colorIndex];
            const upperWord = word.word.toUpperCase();

            this._wordColorMap.set(upperWord, colorInfo);
            this._usedColorIndices.add(colorIndex);
        });
    }

    /**
     * 获取单词的颜色
     * @param word 单词
     * @returns 颜色对象
     */
    public getWordColor(word: string): Color {
        const upperWord = word.toUpperCase();
        const colorInfo = this._wordColorMap.get(upperWord);
        
        if (colorInfo) {
            return colorInfo.color;
        }

        // 如果没有找到对应颜色，返回默认颜色
        Logger.warn('ColorThemeManager', `未找到单词 ${upperWord} 的颜色，使用默认颜色`);
        return this._getDefaultColor();
    }

    /**
     * 获取单词的颜色信息
     * @param word 单词
     * @returns 颜色信息对象
     */
    public getWordColorInfo(word: string): MacaronColor | null {
        const upperWord = word.toUpperCase();
        return this._wordColorMap.get(upperWord) || null;
    }

    /**
     * 获取所有单词的颜色映射
     * @returns 颜色映射对象
     */
    public getAllWordColors(): Map<string, MacaronColor> {
        return new Map(this._wordColorMap);
    }

    /**
     * 获取默认颜色
     * @returns 默认颜色
     */
    private _getDefaultColor(): Color {
        return ColorThemeManager.MACARON_COLORS[0].color; // 使用第一个颜色作为默认
    }

    // ==================== UI颜色管理接口 ====================

    /**
     * 获取临时高亮颜色
     * 用于字母连接过程中的黄色高亮显示
     * @returns 临时高亮颜色 (明亮黄色，半透明)
     */
    public getTemporaryHighlightColor(): Color {
        return new Color(ColorThemeManager.UI_COLORS.TEMPORARY_HIGHLIGHT);
    }

    /**
     * 获取失败高亮颜色
     * 用于连接失败时的红色高亮显示
     * @returns 失败高亮颜色 (纯红色，完全不透明)
     */
    public getFailureHighlightColor(): Color {
        return new Color(ColorThemeManager.UI_COLORS.FAILURE_HIGHLIGHT);
    }

    /**
     * 获取成功指示颜色
     * 用于成功操作的绿色指示显示
     * @returns 成功指示颜色 (纯绿色，完全不透明)
     */
    public getSuccessIndicatorColor(): Color {
        return new Color(ColorThemeManager.UI_COLORS.SUCCESS_INDICATOR);
    }

    /**
     * 获取默认背景颜色
     * 用于字母的默认背景色显示
     * @returns 默认背景颜色 (纯白色，完全不透明)
     */
    public getDefaultBackgroundColor(): Color {
        return new Color(ColorThemeManager.UI_COLORS.DEFAULT_BACKGROUND);
    }

    /**
     * 获取默认文本颜色
     * 用于未完成单词的文本显示
     * @returns 默认文本颜色 (纯黑色，完全不透明)
     */
    public getDefaultTextColor(): Color {
        return new Color(ColorThemeManager.UI_COLORS.DEFAULT_TEXT);
    }

    /**
     * 获取已完成文本颜色
     * 用于已完成单词的文本显示
     * @returns 已完成文本颜色 (中等灰色，良好对比度)
     */
    public getCompletedTextColor(): Color {
        return new Color(ColorThemeManager.UI_COLORS.COMPLETED_TEXT);
    }

    /**
     * 获取连线颜色
     * 用于连接过程中的连线显示
     * @returns 连线颜色 (金色，85%透明度)
     */
    public getConnectionLineColor(): Color {
        return new Color(ColorThemeManager.UI_COLORS.CONNECTION_LINE);
    }

    /**
     * 获取连线光效颜色
     * 用于连线的光效显示
     * @returns 连线光效颜色 (白色光效，50%透明度)
     */
    public getConnectionGlowColor(): Color {
        return new Color(ColorThemeManager.UI_COLORS.CONNECTION_GLOW);
    }

    /**
     * 获取字母高亮颜色
     * 用于被连接字母的高亮显示
     * @returns 字母高亮颜色 (黄色高亮，60%透明度)
     */
    public getLetterHighlightColor(): Color {
        return new Color(ColorThemeManager.UI_COLORS.LETTER_HIGHLIGHT);
    }

    /**
     * 获取成功反馈颜色
     * 用于连接成功时的反馈显示
     * @returns 成功反馈颜色 (春绿色，完全不透明)
     */
    public getSuccessFeedbackColor(): Color {
        return new Color(ColorThemeManager.UI_COLORS.SUCCESS_FEEDBACK);
    }

    /**
     * 获取失败反馈颜色
     * 用于连接失败时的反馈显示
     * @returns 失败反馈颜色 (橙红色，完全不透明)
     */
    public getFailureFeedbackColor(): Color {
        return new Color(ColorThemeManager.UI_COLORS.FAILURE_FEEDBACK);
    }

    /**
     * 提示功能颜色定义
     */
    private static readonly HINT_COLORS = {
        HINT_PATH: new Color(255, 215, 0, 200), // 金色，半透明
        HINT_BUTTON: new Color(70, 130, 180, 255), // 钢蓝色
        HINT_HIGHLIGHT: new Color(135, 206, 235, 180) // 天空蓝，半透明
    } as const;

    /**
     * 获取提示颜色
     * 用于提示路径的显示
     * @returns 提示颜色 (金色，半透明)
     */
    public getHintColor(): Color {
        return ColorThemeManager.HINT_COLORS.HINT_PATH;
    }

    /**
     * 获取提示按钮颜色
     * 用于提示按钮的背景色
     * @returns 提示按钮颜色 (钢蓝色)
     */
    public getHintButtonColor(): Color {
        return ColorThemeManager.HINT_COLORS.HINT_BUTTON;
    }

    /**
     * 获取提示高亮颜色
     * 用于提示功能的字母高亮显示
     * @returns 提示高亮颜色 (明亮黄色，半透明) - 与连线临时高亮相同
     */
    public getHintHighlightColor(): Color {
        return new Color(ColorThemeManager.UI_COLORS.TEMPORARY_HIGHLIGHT);
    }

    /**
     * 验证UI颜色配置
     * 检查所有UI颜色是否正确定义
     * @returns 验证结果
     */
    public validateUIColors(): boolean {
        const uiColors = ColorThemeManager.UI_COLORS;
        let isValid = true;

        // 检查每个UI颜色是否正确定义
        const colorChecks = [
            { name: '临时高亮颜色', color: uiColors.TEMPORARY_HIGHLIGHT },
            { name: '失败高亮颜色', color: uiColors.FAILURE_HIGHLIGHT },
            { name: '成功指示颜色', color: uiColors.SUCCESS_INDICATOR },
            { name: '默认背景颜色', color: uiColors.DEFAULT_BACKGROUND },
            { name: '默认文本颜色', color: uiColors.DEFAULT_TEXT },
            { name: '已完成文本颜色', color: uiColors.COMPLETED_TEXT },
            { name: '连线颜色', color: uiColors.CONNECTION_LINE },
            { name: '连线光效颜色', color: uiColors.CONNECTION_GLOW },
            { name: '字母高亮颜色', color: uiColors.LETTER_HIGHLIGHT },
            { name: '成功反馈颜色', color: uiColors.SUCCESS_FEEDBACK },
            { name: '失败反馈颜色', color: uiColors.FAILURE_FEEDBACK }
        ];

        colorChecks.forEach(check => {
            if (!check.color || !(check.color instanceof Color)) {
                Logger.error('ColorThemeManager', `${check.name} 配置错误`);
                isValid = false;
            }
        });

        return isValid;
    }

    /**
     * 获取颜色管理统计信息
     * @returns 统计信息对象
     */
    public getColorStatistics(): {
        macaronColorCount: number;
        uiColorCount: number;
        totalManagedColors: number;
        assignedWordCount: number;
    } {
        const stats = {
            macaronColorCount: ColorThemeManager.MACARON_COLORS.length,
            uiColorCount: Object.keys(ColorThemeManager.UI_COLORS).length,
            totalManagedColors: ColorThemeManager.MACARON_COLORS.length + Object.keys(ColorThemeManager.UI_COLORS).length,
            assignedWordCount: this._wordColorMap.size
        };

        return stats;
    }

    /**
     * 获取可用的颜色数量
     * @returns 颜色数量
     */
    public getAvailableColorCount(): number {
        return ColorThemeManager.MACARON_COLORS.length;
    }

    /**
     * 检查是否有足够的颜色
     * @param wordCount 单词数量
     * @returns 是否有足够颜色
     */
    public hasEnoughColors(wordCount: number): boolean {
        return wordCount <= ColorThemeManager.MACARON_COLORS.length;
    }

    /**
     * 获取颜色对比度信息
     * @param backgroundColor 背景颜色
     * @returns 对比度信息
     */
    public getContrastInfo(backgroundColor: Color): ContrastInfo {
        // 计算与黑色文字的对比度
        const blackColor = ColorThemeManager.UI_COLORS.DEFAULT_TEXT;
        const contrastRatio = this._calculateContrastRatio(backgroundColor, blackColor);
        
        return {
            contrastRatio: contrastRatio,
            isAccessible: contrastRatio >= 4.5, // WCAG AA 标准
            readabilityLevel: this._getReadabilityLevel(contrastRatio)
        };
    }

    /**
     * 计算颜色对比度
     * @param color1 颜色1
     * @param color2 颜色2
     * @returns 对比度比值
     */
    private _calculateContrastRatio(color1: Color, color2: Color): number {
        const luminance1 = this._calculateLuminance(color1);
        const luminance2 = this._calculateLuminance(color2);
        
        const lighter = Math.max(luminance1, luminance2);
        const darker = Math.min(luminance1, luminance2);
        
        return (lighter + 0.05) / (darker + 0.05);
    }

    /**
     * 计算颜色亮度
     * @param color 颜色
     * @returns 亮度值
     */
    private _calculateLuminance(color: Color): number {
        const r = this._linearizeColorComponent(color.r / 255);
        const g = this._linearizeColorComponent(color.g / 255);
        const b = this._linearizeColorComponent(color.b / 255);
        
        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    }

    /**
     * 线性化颜色分量
     * @param component 颜色分量
     * @returns 线性化值
     */
    private _linearizeColorComponent(component: number): number {
        return component <= 0.03928 
            ? component / 12.92 
            : Math.pow((component + 0.055) / 1.055, 2.4);
    }

    /**
     * 获取可读性等级
     * @param contrastRatio 对比度
     * @returns 可读性等级
     */
    private _getReadabilityLevel(contrastRatio: number): ReadabilityLevel {
        if (contrastRatio >= 7) return ReadabilityLevel.AAA;
        if (contrastRatio >= 4.5) return ReadabilityLevel.AA;
        if (contrastRatio >= 3) return ReadabilityLevel.A;
        return ReadabilityLevel.POOR;
    }

    /**
     * 获取推荐的文字颜色
     * @param backgroundColor 背景颜色
     * @returns 推荐的文字颜色
     */
    public getRecommendedTextColor(backgroundColor: Color): Color {
        const luminance = this._calculateLuminance(backgroundColor);
        
        // 如果背景较亮，使用黑色文字；如果背景较暗，使用白色文字
        return luminance > 0.5
            ? ColorThemeManager.UI_COLORS.DEFAULT_TEXT    // 黑色
            : ColorThemeManager.UI_COLORS.DEFAULT_BACKGROUND; // 白色
    }

    /**
     * 重置颜色分配
     */
    public reset(): void {
        this._wordColorMap.clear();
        this._usedColorIndices.clear();
    }
}

/**
 * 马卡龙颜色信息接口
 */
export interface MacaronColor {
    name: string;
    color: Color;
    description: string;
}

/**
 * 对比度信息接口
 */
export interface ContrastInfo {
    contrastRatio: number;
    isAccessible: boolean;
    readabilityLevel: ReadabilityLevel;
}

/**
 * 可读性等级枚举
 */
export enum ReadabilityLevel {
    POOR = 'poor',
    A = 'a',
    AA = 'aa',
    AAA = 'aaa'
}

/**
 * 颜色可访问性结果接口
 */
export interface ColorAccessibilityResult {
    colorName: string;
    color: Color;
    contrastInfo: ContrastInfo;
}

/**
 * 可访问性报告接口
 */
export interface AccessibilityReport {
    totalColors: number;
    accessibleColors: number;
    accessibilityRate: number;
    results: ColorAccessibilityResult[];
}
