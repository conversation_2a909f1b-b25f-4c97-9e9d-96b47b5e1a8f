import { Logger } from '../Logger';
import { SpatialDistributionManager } from './SpatialDistributionManager';

/**
 * 空间质量评估器
 * 
 * 专门负责评估单词在网格中的空间分布质量。
 * 遵循单一职责原则，只处理空间质量评估相关的逻辑。
 * 
 * 核心功能：
 * - 空间分布质量评估
 * - 象限平衡度计算
 * - 聚集惩罚评估
 * - 方向多样性分析
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-20
 */
export class SpatialQualityEvaluator {
    
    // 网格常量
    private static readonly GRID_ROWS = 9;
    private static readonly GRID_COLS = 8;

    /**
     * 评估空间分布质量
     * @param placedWords 已放置的单词信息
     * @returns 空间分布质量评分 (0-100)
     */
    public static evaluateSpatialDistributionQuality(
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): {
        overallScore: number;
        quadrantBalance: number;
        spatialSpread: number;
        clusteringPenalty: number;
        edgeDistribution: number;
        details: string[];
    } {
        if (placedWords.length === 0) {
            return {
                overallScore: 0,
                quadrantBalance: 0,
                spatialSpread: 0,
                clusteringPenalty: 0,
                edgeDistribution: 0,
                details: ['无已放置单词']
            };
        }

        const details: string[] = [];
        
        // 评估1：象限平衡度 (0-30分)
        const quadrantBalance = this._evaluateQuadrantBalance(placedWords);
        details.push(`象限平衡: ${quadrantBalance.toFixed(1)}/30`);

        // 评估2：空间分散度 (0-25分)
        const spatialSpread = this._evaluateSpatialSpread(placedWords);
        details.push(`空间分散: ${spatialSpread.toFixed(1)}/25`);

        // 评估3：聚集惩罚 (0-20分扣分)
        const clusteringPenalty = this._evaluateClusteringPenalty(placedWords);
        details.push(`聚集惩罚: -${clusteringPenalty.toFixed(1)}/20`);

        // 评估4：边缘分布 (0-15分)
        const edgeDistribution = this._evaluateEdgeDistribution(placedWords);
        details.push(`边缘分布: ${edgeDistribution.toFixed(1)}/15`);

        // 评估5：方向多样性 (0-10分)
        const directionDiversity = this._evaluateDirectionDiversity(placedWords);
        details.push(`方向多样性: ${directionDiversity.toFixed(1)}/10`);

        const overallScore = Math.max(0, Math.min(100, 
            quadrantBalance + spatialSpread - clusteringPenalty + edgeDistribution + directionDiversity
        ));

        return {
            overallScore,
            quadrantBalance,
            spatialSpread,
            clusteringPenalty,
            edgeDistribution,
            details
        };
    }

    /**
     * 为空间分布优化方向优先级
     * @param directions 所有方向
     * @param placedWords 已放置的单词
     * @returns 优先级排序的方向数组
     */
    public static prioritizeDirectionsForSpatialDistribution(
        directions: any[], 
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): any[] {
        if (placedWords.length === 0) {
            // 如果没有已放置的单词，随机化方向
            const shuffledDirections = [...directions];
            this._shuffleArray(shuffledDirections);
            return shuffledDirections;
        }

        // 统计已使用的方向
        const directionCounts = new Map<string, number>();
        for (const direction of directions) {
            directionCounts.set(direction.name, 0);
        }

        for (const wordInfo of placedWords) {
            const count = directionCounts.get(wordInfo.direction) || 0;
            directionCounts.set(wordInfo.direction, count + 1);
        }

        // 按使用次数排序，优先选择使用较少的方向
        const prioritizedDirections = [...directions].sort((a, b) => {
            const countA = directionCounts.get(a.name) || 0;
            const countB = directionCounts.get(b.name) || 0;
            
            // 使用次数少的优先，如果相同则随机
            if (countA !== countB) {
                return countA - countB;
            }
            return Math.random() - 0.5;
        });

        Logger.info('SpatialQualityEvaluator', 
            `🧭 方向优先级：${prioritizedDirections.map(d => `${d.name}(${directionCounts.get(d.name)})`).join(', ')}`);

        return prioritizedDirections;
    }

    /**
     * 评估象限平衡度
     * @param placedWords 已放置单词
     * @returns 平衡度评分 (0-30)
     */
    private static _evaluateQuadrantBalance(
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): number {
        const quadrantCounts = new Map<number, number>();
        for (let i = 1; i <= 4; i++) {
            quadrantCounts.set(i, 0);
        }

        for (const wordInfo of placedWords) {
            const quadrant = SpatialDistributionManager.getQuadrantForPosition(wordInfo.row, wordInfo.col);
            quadrantCounts.set(quadrant, (quadrantCounts.get(quadrant) || 0) + 1);
        }

        const counts = Array.from(quadrantCounts.values());
        const maxCount = Math.max(...counts);
        const minCount = Math.min(...counts);
        const avgCount = placedWords.length / 4;

        // 理想情况：每个象限都有相近数量的单词
        const variance = counts.reduce((sum, count) => sum + Math.pow(count - avgCount, 2), 0) / 4;
        const balanceScore = Math.max(0, 30 - variance * 5);

        return balanceScore;
    }

    /**
     * 评估空间分散度
     * @param placedWords 已放置单词
     * @returns 分散度评分 (0-25)
     */
    private static _evaluateSpatialSpread(
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): number {
        if (placedWords.length < 2) return 25;

        let totalDistance = 0;
        let pairCount = 0;

        for (let i = 0; i < placedWords.length; i++) {
            for (let j = i + 1; j < placedWords.length; j++) {
                const word1 = placedWords[i];
                const word2 = placedWords[j];
                
                const distance = Math.sqrt(
                    Math.pow(word1.row - word2.row, 2) + 
                    Math.pow(word1.col - word2.col, 2)
                );
                
                totalDistance += distance;
                pairCount++;
            }
        }

        const avgDistance = totalDistance / pairCount;
        const maxPossibleDistance = Math.sqrt(Math.pow(this.GRID_ROWS - 1, 2) + Math.pow(this.GRID_COLS - 1, 2));
        const spreadScore = (avgDistance / maxPossibleDistance) * 25;

        return Math.min(25, spreadScore);
    }

    /**
     * 评估聚集惩罚
     * @param placedWords 已放置单词
     * @returns 聚集惩罚分数 (0-20)
     */
    private static _evaluateClusteringPenalty(
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): number {
        let penalty = 0;
        const minDistance = 2; // 最小期望距离

        for (let i = 0; i < placedWords.length; i++) {
            for (let j = i + 1; j < placedWords.length; j++) {
                const word1 = placedWords[i];
                const word2 = placedWords[j];
                
                const distance = Math.abs(word1.row - word2.row) + Math.abs(word1.col - word2.col);
                
                if (distance < minDistance) {
                    penalty += (minDistance - distance) * 2; // 距离越近惩罚越重
                }
            }
        }

        return Math.min(20, penalty);
    }

    /**
     * 评估边缘分布
     * @param placedWords 已放置单词
     * @returns 边缘分布评分 (0-15)
     */
    private static _evaluateEdgeDistribution(
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): number {
        let edgeCount = 0;
        
        for (const wordInfo of placedWords) {
            const row = wordInfo.row;
            const col = wordInfo.col;
            
            // 检查是否在边缘
            if (row === 0 || row === this.GRID_ROWS - 1 || 
                col === 0 || col === this.GRID_COLS - 1) {
                edgeCount++;
            }
        }

        // 理想情况：约1/3的单词在边缘
        const idealEdgeRatio = 1/3;
        const actualEdgeRatio = edgeCount / placedWords.length;
        const edgeScore = 15 - Math.abs(actualEdgeRatio - idealEdgeRatio) * 30;

        return Math.max(0, edgeScore);
    }

    /**
     * 评估方向多样性
     * @param placedWords 已放置单词
     * @returns 方向多样性评分 (0-10)
     */
    private static _evaluateDirectionDiversity(
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): number {
        const directionSet = new Set(placedWords.map(w => w.direction));
        const diversityScore = (directionSet.size / 8) * 10; // 8个方向为满分
        
        return diversityScore;
    }

    /**
     * 数组随机化工具方法
     * @param array 要随机化的数组
     */
    private static _shuffleArray(array: any[]): void {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }
}
