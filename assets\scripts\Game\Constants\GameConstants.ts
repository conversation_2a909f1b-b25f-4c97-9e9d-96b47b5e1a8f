import { Vec3 } from 'cc';

/**
 * 游戏常量配置 - 统一管理游戏中所有常量和接口定义
 * 
 * 整合了原 AnimationConstants.ts 和 IConnectionAnimationEvents.ts 的内容
 * 遵循单一职责原则，专门负责游戏配置的集中管理
 * 
 * 核心功能：
 * - 动画配置常量管理
 * - 连接动画事件接口定义
 * - 游戏规则常量定义
 * - 性能适配配置管理
 * 
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-20
 */

// ==================== 基础接口定义 ====================

/**
 * 字母位置接口
 */
export interface LetterPosition {
    row: number;
    col: number;
}

/**
 * 连接动画事件类型枚举
 */
export enum ConnectionAnimationEventType {
    /** 开始连接 */
    CONNECTION_START = 'connection_start',
    /** 连接进行中 */
    CONNECTION_PROGRESS = 'connection_progress',
    /** 连接成功 */
    CONNECTION_SUCCESS = 'connection_success',
    /** 连接失败 */
    CONNECTION_FAILURE = 'connection_failure',
    /** 连接取消 */
    CONNECTION_CANCEL = 'connection_cancel',
    /** 字母高亮 */
    LETTER_HIGHLIGHT = 'letter_highlight',
    /** 字母取消高亮 */
    LETTER_UNHIGHLIGHT = 'letter_unhighlight'
}

/**
 * 连接动画处理器接口
 */
export interface IConnectionAnimationHandler {
    /** 处理连接开始事件 */
    onConnectionStart(position: LetterPosition): void;
    /** 处理连接进行中事件 */
    onConnectionProgress(position: LetterPosition): void;
    /** 处理连接成功事件 */
    onConnectionSuccess(word: string, positions: LetterPosition[]): void;
    /** 处理连接失败事件 */
    onConnectionFailure(positions: LetterPosition[]): void;
    /** 处理连接取消事件 */
    onConnectionCancel(): void;
}

/**
 * 动画状态枚举
 */
export enum AnimationState {
    /** 空闲状态 */
    IDLE = 'idle',
    /** 播放中 */
    PLAYING = 'playing',
    /** 暂停 */
    PAUSED = 'paused',
    /** 完成 */
    COMPLETED = 'completed',
    /** 取消 */
    CANCELLED = 'cancelled'
}

/**
 * 动画缓动类型常量
 */
export const ANIMATION_EASING = {
    /** 线性 */
    LINEAR: 'linear',
    /** 缓入 */
    EASE_IN: 'easeIn',
    /** 缓出 */
    EASE_OUT: 'easeOut',
    /** 缓入缓出 */
    EASE_IN_OUT: 'easeInOut',
    /** 弹性缓出 */
    ELASTIC_OUT: 'elasticOut',
    /** 回弹缓出 */
    BACK_OUT: 'backOut',
    /** 弹跳缓出 */
    BOUNCE_OUT: 'bounceOut'
} as const;

/**
 * 动画类型常量
 */
export enum AnimationType {
    /** 连线动画 */
    CONNECTION = 'connection',
    /** 字母高亮 */
    LETTER_GLOW = 'letter_glow',
    /** 成功反馈 */
    SUCCESS_FEEDBACK = 'success_feedback',
    /** 失败反馈 */
    FAILURE_FEEDBACK = 'failure_feedback',
    /** 单词完成 */
    WORD_COMPLETION = 'word_completion'
}

// ==================== 事件数据接口 ====================

/**
 * 连接动画事件数据
 */
export interface ConnectionAnimationEventData {
    /** 事件类型 */
    type: ConnectionAnimationEventType;
    /** 字母位置 */
    position?: LetterPosition;
    /** 世界坐标 */
    worldPosition?: Vec3;
    /** 单词内容（成功时） */
    word?: string;
    /** 完成回调 */
    onComplete?: () => void;
}

// ==================== 字母分布配置常量 ====================

/**
 * 字母分布优化配置接口
 */
export interface DistributionOptimizationConfig {
    /** 后处理最大迭代次数 */
    maxPostProcessIterations: number;
    /** 连续字母修复最大尝试次数 */
    maxConsecutiveFixAttempts: number;
    /** 2x2区域修复最大尝试次数 */
    max2x2FixAttempts: number;
    /** 字母频率平衡最大尝试次数 */
    maxFrequencyBalanceAttempts: number;
    /** 是否启用详细日志 */
    enableDetailedLogging: boolean;
    /** 是否启用性能优化 */
    enablePerformanceOptimization: boolean;
}

/**
 * 字母分布质量标准接口
 */
export interface DistributionQualityStandards {
    /** 允许的最大连续字母数量 */
    maxConsecutiveLetters: number;
    /** 允许的最大2x2相同字母区域数量 */
    max2x2SameLetterAreas: number;
    /** 字母频率平衡阈值 */
    frequencyBalanceThreshold: number;
    /** 最小字母多样性 */
    minLetterDiversity: number;
}

/**
 * 字母分布配置常量
 */
export const LETTER_DISTRIBUTION_CONFIG = {
    /** 默认优化配置 */
    DEFAULT: {
        maxPostProcessIterations: 20,
        maxConsecutiveFixAttempts: 100,
        max2x2FixAttempts: 50,
        maxFrequencyBalanceAttempts: 30,
        enableDetailedLogging: false,
        enablePerformanceOptimization: true,
    },
    /** 质量标准 */
    QUALITY_STANDARDS: {
        maxConsecutiveLetters: 0, // 不允许连续字母
        max2x2SameLetterAreas: 0, // 不允许2x2相同字母
        frequencyBalanceThreshold: 1.5, // 频率平衡阈值
        minLetterDiversity: 15, // 最少使用15种不同字母
    }
} as const;

// ==================== 游戏规则常量 ====================

/**
 * 游戏网格配置常量
 */
export const GAME_GRID_CONFIG = {
    /** 网格行数 */
    ROWS: 9,
    /** 网格列数 */
    COLS: 8,
    /** 总字母数 */
    TOTAL_LETTERS: 72,
    /** 最大单词长度 */
    MAX_WORD_LENGTH: 8,
    /** 最小单词长度 */
    MIN_WORD_LENGTH: 3,
    /** 每关单词数量 */
    WORDS_PER_LEVEL: 8
} as const;

/**
 * 游戏性能配置常量
 */
export const GAME_PERFORMANCE_CONFIG = {
    /** 字母生成最大重试次数 */
    MAX_GENERATION_ATTEMPTS: 15,
    /** 字母生成超时时间（毫秒） */
    GENERATION_TIMEOUT: 3000,
    /** 动画帧率限制 */
    ANIMATION_FPS_LIMIT: 60,
    /** 日志级别 */
    DEFAULT_LOG_LEVEL: 'WARN'
} as const;

// ==================== 动画配置常量 ====================

/**
 * 连接动画配置常量
 */
export const CONNECTION_ANIMATION_CONFIG = {
    /** 连线延伸速度 (px/秒) */
    LINE_EXTEND_SPEED: 1000,
    /** 连线宽度 */
    LINE_WIDTH: 5,
    /** 连线透明度 */
    LINE_ALPHA: 0.85,
    
    /** 字母光环呼吸周期 (秒) */
    LETTER_GLOW_CYCLE: 1.5,
    /** 字母光环最小透明度 */
    GLOW_MIN_ALPHA: 0.4,
    /** 字母光环最大透明度 */
    GLOW_MAX_ALPHA: 0.8,
    /** 字母光环大小倍数 - 优化以避免重叠和闪烁 */
    GLOW_SCALE_MIN: 1.0,
    GLOW_SCALE_MAX: 1.05,
    /** 字母背景高亮透明度变化 */
    GLOW_BG_ALPHA_MIN: 0.0,
    GLOW_BG_ALPHA_MAX: 0.25,
    
    /** 渲染优化配置 */
    PIXEL_ALIGNMENT_ENABLED: true,
    SCALE_PRECISION: 1000,
    UPDATE_THROTTLE_MS: 16,
    
    /** 替代动画模式 */
    USE_OPACITY_ANIMATION: false,
    OPACITY_MIN: 0.7,
    OPACITY_MAX: 1.0
} as const;

/**
 * 成功反馈动画配置常量
 */
export const SUCCESS_FEEDBACK_CONFIG = {
    /** 成功反馈总持续时间 (秒) */
    DURATION: 0.65,
    /** 连线亮度增强倍数 */
    BRIGHTNESS_MULTIPLIER: 1.5,
    /** 光圈最大半径 (px) */
    GLOW_RADIUS: 20,
    /** 能量汇聚持续时间 (秒) */
    CONVERGE_DURATION: 0.2,
    /** 爆发效果持续时间 (秒) */
    BURST_DURATION: 0.15,
    /** 消失效果持续时间 (秒) */
    FADE_DURATION: 0.2
} as const;

/**
 * 失败反馈动画配置常量
 */
export const FAILURE_FEEDBACK_CONFIG = {
    /** 失败反馈总持续时间 (秒) */
    DURATION: 0.6,
    /** 震动幅度 (px) */
    SHAKE_AMPLITUDE: 2,
    /** 震动频率 (Hz) */
    SHAKE_FREQUENCY: 15,
    /** 震动持续时间 (秒) */
    SHAKE_DURATION: 0.2,
    /** 消散持续时间 (秒) */
    FADE_DURATION: 0.3,
    /** 字母高亮消失时间 (秒) */
    LETTER_FADE_DURATION: 0.1
} as const;

/**
 * 单词完成动画配置常量
 */
export const WORD_COMPLETION_CONFIG = {
    /** 缩放动画持续时间 (秒) */
    SCALE_DURATION: 0.3,
    /** 颜色过渡持续时间 (秒) */
    COLOR_DURATION: 0.5,
    /** 最大缩放倍数 */
    MAX_SCALE: 1.5,
    /** 缓动类型 */
    EASING: 'backOut'
} as const;

/**
 * 性能适配配置常量（整合字母分布性能配置）
 */
export const PERFORMANCE_ADAPTATION_CONFIG = {
    /** 高端设备倍数 */
    HIGH_END_MULTIPLIER: 1.0,
    /** 中端设备倍数 */
    MEDIUM_END_MULTIPLIER: 0.8,
    /** 低端设备倍数 */
    LOW_END_MULTIPLIER: 0.6,

    /** 光环周期调整倍数 */
    GLOW_CYCLE_MULTIPLIERS: {
        HIGH: 1.0,
        MEDIUM: 1.2,
        LOW: 1.8
    },

    /** 光圈半径调整倍数 */
    GLOW_RADIUS_MULTIPLIERS: {
        HIGH: 1.0,
        MEDIUM: 0.8,
        LOW: 0.6
    },

    /** 字母分布设备性能配置 */
    LETTER_DISTRIBUTION: {
        HIGH_END: {
            maxPostProcessIterations: 30,
            maxConsecutiveFixAttempts: 150,
            max2x2FixAttempts: 80,
            maxFrequencyBalanceAttempts: 50,
            enableDetailedLogging: true,
            enablePerformanceOptimization: false,
        },
        MEDIUM_END: {
            maxPostProcessIterations: 20,
            maxConsecutiveFixAttempts: 100,
            max2x2FixAttempts: 50,
            maxFrequencyBalanceAttempts: 30,
            enableDetailedLogging: false,
            enablePerformanceOptimization: true,
        },
        LOW_END: {
            maxPostProcessIterations: 15,
            maxConsecutiveFixAttempts: 60,
            max2x2FixAttempts: 30,
            maxFrequencyBalanceAttempts: 20,
            enableDetailedLogging: false,
            enablePerformanceOptimization: true,
        }
    }
} as const;

// ==================== 配置聚合函数 ====================

/**
 * 获取完整的游戏配置
 * @returns 完整的游戏配置对象
 */
export function getCompleteGameConfig() {
    return {
        grid: GAME_GRID_CONFIG,
        performance: GAME_PERFORMANCE_CONFIG,
        letterDistribution: LETTER_DISTRIBUTION_CONFIG,
        connectionAnimation: CONNECTION_ANIMATION_CONFIG,
        successFeedback: SUCCESS_FEEDBACK_CONFIG,
        failureFeedback: FAILURE_FEEDBACK_CONFIG,
        wordCompletion: WORD_COMPLETION_CONFIG,
        performanceAdaptation: PERFORMANCE_ADAPTATION_CONFIG,
        easing: ANIMATION_EASING,
        animationState: AnimationState,
        animationType: AnimationType,
        eventType: ConnectionAnimationEventType
    } as const;
}

// ==================== 类型定义 ====================

/**
 * 游戏配置类型定义
 */
export type GameConfigType = ReturnType<typeof getCompleteGameConfig>;
export type ConnectionAnimationConfigType = typeof CONNECTION_ANIMATION_CONFIG;
export type SuccessFeedbackConfigType = typeof SUCCESS_FEEDBACK_CONFIG;
export type FailureFeedbackConfigType = typeof FAILURE_FEEDBACK_CONFIG;
export type WordCompletionConfigType = typeof WORD_COMPLETION_CONFIG;
export type PerformanceAdaptationConfigType = typeof PERFORMANCE_ADAPTATION_CONFIG;
export type GameGridConfigType = typeof GAME_GRID_CONFIG;
export type GamePerformanceConfigType = typeof GAME_PERFORMANCE_CONFIG;
