import { _decorator } from 'cc';
import { Logger } from '../Utils/Logger';
const { ccclass } = _decorator;

/**
 * 单词数据接口定义
 *
 * 定义游戏中单个单词的数据结构，包含单词的基本信息和属性。
 * 用于统一管理单词数据，确保数据结构的一致性和类型安全。
 *
 * @interface IWordData
 *
 * @example
 * ```typescript
 * const wordData: IWordData = {
 *     word: "HELLO",
 *     length: 5,
 *     difficulty: 2
 * };
 * ```
 */
export interface IWordData {
    /**
     * 单词文本内容
     * 通常为大写英文字母组成的5字母单词
     */
    word: string;

    /**
     * 单词长度
     * 字符数量，通常为5个字母
     */
    length: number;

    /**
     * 单词难度等级
     * 数值越大表示难度越高，用于关卡难度控制
     * 范围：1-5，其中1为最简单，5为最困难
     */
    difficulty: number;
}

/**
 * 关卡数据接口定义
 * @interface ILevelData
 */
export interface ILevelData {
    /** 关卡编号 */
    levelId: number;
    /** 关卡名称 */
    levelName: string;
    /** 该关卡的单词列表 */
    words: IWordData[];
    /** 单词数量 */
    wordCount: number;
}



/**
 * 单词数据库 - 负责管理所有关卡的单词数据
 * 遵循单一职责原则，只负责单词数据的存储和提供
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-03
 */
@ccclass('WordDatabase')
export class WordDatabase {
    
    /**
     * 第1关单词数据 - 8个5字母英文单词
     */
    private static readonly LEVEL_1_WORDS: IWordData[] = [
        { word: "APPLE", length: 5, difficulty: 1 },
        { word: "HOUSE", length: 5, difficulty: 1 },
        { word: "WATER", length: 5, difficulty: 1 },
        { word: "MUSIC", length: 5, difficulty: 1 },
        { word: "HAPPY", length: 5, difficulty: 1 },
        { word: "LIGHT", length: 5, difficulty: 1 },
        { word: "WORLD", length: 5, difficulty: 1 },
        { word: "PEACE", length: 5, difficulty: 1 }
    ];

    /**
     * 所有关卡数据映射
     */
    private static readonly LEVEL_DATA_MAP: Map<number, ILevelData> = new Map([
        [1, {
            levelId: 1,
            levelName: "第1关",
            words: WordDatabase.LEVEL_1_WORDS,
            wordCount: 8
        }]
    ]);

    /**
     * 获取指定关卡的单词数据
     * @param levelId 关卡ID
     * @returns 关卡数据，如果关卡不存在则返回null
     */
    public static getLevelData(levelId: number): ILevelData | null {
        const levelData = WordDatabase.LEVEL_DATA_MAP.get(levelId);
        if (!levelData) {
            Logger.error('WordDatabase', `关卡 ${levelId} 不存在！`);
            return null;
        }

        Logger.info('WordDatabase', `成功获取关卡 ${levelId} 数据，包含 ${levelData.wordCount} 个单词`);
        return levelData;
    }

    /**
     * 获取指定关卡的单词列表
     * @param levelId 关卡ID
     * @returns 单词数组，如果关卡不存在则返回空数组
     */
    public static getLevelWords(levelId: number): IWordData[] {
        const levelData = WordDatabase.getLevelData(levelId);
        return levelData ? levelData.words : [];
    }

    /**
     * 验证关卡数据的完整性
     * @param levelId 关卡ID
     * @returns 验证结果
     */
    public static validateLevelData(levelId: number): boolean {
        const levelData = WordDatabase.getLevelData(levelId);
        if (!levelData) {
            return false;
        }

        // 验证单词数量
        if (levelData.words.length !== levelData.wordCount) {
            Logger.error('WordDatabase', `关卡 ${levelId} 单词数量不匹配！期望: ${levelData.wordCount}, 实际: ${levelData.words.length}`);
            return false;
        }

        // 验证每个单词的格式
        for (const wordData of levelData.words) {
            if (!wordData.word || wordData.word.length !== wordData.length) {
                Logger.error('WordDatabase', `关卡 ${levelId} 单词数据格式错误: ${JSON.stringify(wordData)}`);
                return false;
            }
        }

        return true;
    }

    /**
     * 获取所有可用的关卡ID列表
     * @returns 关卡ID数组
     */
    public static getAvailableLevels(): number[] {
        return Array.from(WordDatabase.LEVEL_DATA_MAP.keys());
    }

    /**
     * 检查指定关卡是否存在
     * @param levelId 关卡ID
     * @returns 是否存在
     */
    public static hasLevel(levelId: number): boolean {
        return WordDatabase.LEVEL_DATA_MAP.has(levelId);
    }
}
