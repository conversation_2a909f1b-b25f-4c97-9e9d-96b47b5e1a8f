import { _decorator, Component, Enum } from 'cc';
import { Logger } from '../Utils/Logger';
import { 
    LogLevel, 
    LogPresetMode, 
    CoreComponent, 
    ServiceComponent, 
    UtilityComponent,
    getComponentName,
    getCoreComponents,
    getHighFrequencyComponents
} from '../Utils/LoggerEnums';

const { ccclass, property } = _decorator;

/**
 * 可视化日志配置管理组件
 * 
 * 提供编辑器界面来控制日志级别，避免修改脚本代码来调整日志输出。
 * 支持运行时动态调整日志级别，适用于不同的开发和测试场景。
 * 
 * 功能特性：
 * - 全局日志级别控制
 * - 核心组件独立日志级别设置
 * - 高频组件静默开关
 * - 快捷预设模式
 * - 运行时动态调整
 * 
 * 使用方法：
 * 1. 将此组件添加到管理器节点
 * 2. 在编辑器中配置所需的日志级别
 * 3. 运行时会自动应用配置
 * 4. 可以通过预设模式快速切换配置
 */
@ccclass('LogConfigManager')
export class LogConfigManager extends Component {

    // ==================== 全局日志配置 ====================

    /**
     * 全局日志级别
     */
    @property({ 
        type: Enum(LogLevel),
        displayName: "全局日志级别", 
        tooltip: "控制所有组件的默认日志级别\nERROR: 只显示错误\nWARN: 显示警告和错误\nINFO: 显示信息、警告和错误\nDEBUG: 显示所有日志" 
    })
    globalLogLevel: LogLevel = LogLevel.WARN;

    /**
     * 快捷预设模式
     */
    @property({ 
        type: Enum(LogPresetMode),
        displayName: "预设模式", 
        tooltip: "快捷配置模式\nPRODUCTION: 生产模式，最少日志\nTESTING: 测试模式，适量日志\nDEVELOPMENT: 开发模式，详细日志\nDEBUG_ONLY: 纯调试模式，所有日志" 
    })
    presetMode: LogPresetMode = LogPresetMode.PRODUCTION;

    /**
     * 是否启用预设模式
     */
    @property({ 
        displayName: "启用预设模式", 
        tooltip: "启用后将使用预设模式配置，忽略下方的详细配置" 
    })
    usePresetMode: boolean = true;

    // ==================== 核心组件日志配置 ====================

    /**
     * GameInitializer日志级别
     */
    @property({ 
        type: Enum(LogLevel),
        displayName: "GameInitializer", 
        tooltip: "游戏初始化器的日志级别",
        visible: function() { return !this.usePresetMode; }
    })
    gameInitializerLevel: LogLevel = LogLevel.INFO;

    /**
     * SceneManager日志级别
     */
    @property({ 
        type: Enum(LogLevel),
        displayName: "SceneManager", 
        tooltip: "场景管理器的日志级别",
        visible: function() { return !this.usePresetMode; }
    })
    sceneManagerLevel: LogLevel = LogLevel.INFO;

    /**
     * AudioManager日志级别
     */
    @property({ 
        type: Enum(LogLevel),
        displayName: "AudioManager", 
        tooltip: "音频管理器的日志级别",
        visible: function() { return !this.usePresetMode; }
    })
    audioManagerLevel: LogLevel = LogLevel.INFO;

    /**
     * WordPronunciationManager日志级别
     */
    @property({ 
        type: Enum(LogLevel),
        displayName: "WordPronunciationManager", 
        tooltip: "发音管理器的日志级别",
        visible: function() { return !this.usePresetMode; }
    })
    wordPronunciationManagerLevel: LogLevel = LogLevel.INFO;

    // ==================== 高频组件静默配置 ====================

    /**
     * 静默TranslationCache
     */
    @property({ 
        displayName: "静默 TranslationCache", 
        tooltip: "是否静默翻译缓存组件的所有日志",
        visible: function() { return !this.usePresetMode; }
    })
    silenceTranslationCache: boolean = true;

    /**
     * 静默WordPlacementEngine
     */
    @property({ 
        displayName: "静默 WordPlacementEngine", 
        tooltip: "是否静默单词放置引擎的所有日志",
        visible: function() { return !this.usePresetMode; }
    })
    silenceWordPlacementEngine: boolean = true;

    /**
     * 静默GridSpatialAnalyzer
     */
    @property({ 
        displayName: "静默 GridSpatialAnalyzer", 
        tooltip: "是否静默网格空间分析器的所有日志",
        visible: function() { return !this.usePresetMode; }
    })
    silenceGridSpatialAnalyzer: boolean = true;

    /**
     * 静默TranslationApiClient
     */
    @property({ 
        displayName: "静默 TranslationApiClient", 
        tooltip: "是否静默翻译API客户端的所有日志",
        visible: function() { return !this.usePresetMode; }
    })
    silenceTranslationApiClient: boolean = true;

    /**
     * 静默PlatformAudioAdapter
     */
    @property({ 
        displayName: "静默 PlatformAudioAdapter", 
        tooltip: "是否静默平台音频适配器的所有日志",
        visible: function() { return !this.usePresetMode; }
    })
    silencePlatformAudioAdapter: boolean = true;

    // ==================== 运行时控制 ====================

    /**
     * 是否在运行时应用配置
     */
    @property({ 
        displayName: "运行时应用配置", 
        tooltip: "是否在游戏运行时动态应用日志配置" 
    })
    applyAtRuntime: boolean = true;

    /**
     * 是否显示配置应用日志
     */
    @property({ 
        displayName: "显示配置日志", 
        tooltip: "是否显示日志配置应用过程的日志信息" 
    })
    showConfigLogs: boolean = true;

    // ==================== 生命周期方法 ====================

    /**
     * 组件初始化
     */
    onLoad(): void {
        this._applyLogConfiguration();
    }

    /**
     * 组件启动
     */
    start(): void {
        if (this.applyAtRuntime) {
            // 延迟应用配置，确保所有组件都已初始化
            this.scheduleOnce(() => {
                this._applyLogConfiguration();
            }, 0.1);
        }
    }

    // ==================== 配置应用方法 ====================

    /**
     * 应用日志配置
     */
    private _applyLogConfiguration(): void {
        try {
            if (this.usePresetMode) {
                this._applyPresetMode();
            } else {
                this._applyDetailedConfiguration();
            }

            if (this.showConfigLogs) {
                Logger.info('LogConfigManager', '日志配置已应用');
            }
        } catch (error) {
            Logger.error('LogConfigManager', '应用日志配置失败', error as Error);
        }
    }

    /**
     * 应用预设模式配置
     */
    private _applyPresetMode(): void {
        switch (this.presetMode) {
            case LogPresetMode.PRODUCTION:
                this._applyProductionMode();
                break;
            case LogPresetMode.TESTING:
                this._applyTestingMode();
                break;
            case LogPresetMode.DEVELOPMENT:
                this._applyDevelopmentMode();
                break;
            case LogPresetMode.DEBUG_ONLY:
                this._applyDebugOnlyMode();
                break;
        }

        if (this.showConfigLogs) {
            const modeName = LogPresetMode[this.presetMode];
            Logger.info('LogConfigManager', `已应用预设模式: ${modeName}`);
        }
    }

    /**
     * 应用生产模式配置
     */
    private _applyProductionMode(): void {
        Logger.setLevel(LogLevel.WARN);
        this._silenceHighFrequencyComponents(true);
        this._setCoreComponentsLevel(LogLevel.WARN);
    }

    /**
     * 应用测试模式配置
     */
    private _applyTestingMode(): void {
        Logger.setLevel(LogLevel.INFO);
        this._silenceHighFrequencyComponents(true);
        this._setCoreComponentsLevel(LogLevel.INFO);
    }

    /**
     * 应用开发模式配置
     */
    private _applyDevelopmentMode(): void {
        Logger.setLevel(LogLevel.INFO);
        this._silenceHighFrequencyComponents(false);
        this._setCoreComponentsLevel(LogLevel.DEBUG);
    }

    /**
     * 应用纯调试模式配置
     */
    private _applyDebugOnlyMode(): void {
        Logger.setLevel(LogLevel.DEBUG);
        this._silenceHighFrequencyComponents(false);
        this._setCoreComponentsLevel(LogLevel.DEBUG);
    }

    /**
     * 应用详细配置
     */
    private _applyDetailedConfiguration(): void {
        // 设置全局日志级别
        Logger.setLevel(this.globalLogLevel);

        // 设置核心组件的日志级别
        Logger.setComponentLevel(getComponentName(CoreComponent.GAME_INITIALIZER), this.gameInitializerLevel);
        Logger.setComponentLevel(getComponentName(CoreComponent.SCENE_MANAGER), this.sceneManagerLevel);
        Logger.setComponentLevel(getComponentName(CoreComponent.AUDIO_MANAGER), this.audioManagerLevel);
        Logger.setComponentLevel(getComponentName(CoreComponent.WORD_PRONUNCIATION_MANAGER), this.wordPronunciationManagerLevel);

        // 应用高频组件的静默设置
        this._applyComponentSilence(getComponentName(UtilityComponent.TRANSLATION_CACHE), this.silenceTranslationCache);
        this._applyComponentSilence(getComponentName(UtilityComponent.WORD_PLACEMENT_ENGINE), this.silenceWordPlacementEngine);
        this._applyComponentSilence(getComponentName(UtilityComponent.GRID_SPATIAL_ANALYZER), this.silenceGridSpatialAnalyzer);
        this._applyComponentSilence(getComponentName(ServiceComponent.TRANSLATION_API_CLIENT), this.silenceTranslationApiClient);
        this._applyComponentSilence(getComponentName(UtilityComponent.PLATFORM_AUDIO_ADAPTER), this.silencePlatformAudioAdapter);

        if (this.showConfigLogs) {
            Logger.info('LogConfigManager', '已应用详细配置');
        }
    }

    /**
     * 设置高频组件的静默状态
     * @param silence 是否静默
     */
    private _silenceHighFrequencyComponents(silence: boolean): void {
        const highFreqComponents = getHighFrequencyComponents();
        highFreqComponents.forEach(componentEnum => {
            const componentName = getComponentName(componentEnum);
            this._applyComponentSilence(componentName, silence);
        });
    }

    /**
     * 设置核心组件的日志级别
     * @param level 日志级别
     */
    private _setCoreComponentsLevel(level: LogLevel): void {
        const coreComponents = getCoreComponents();
        coreComponents.forEach(componentEnum => {
            const componentName = getComponentName(componentEnum);
            Logger.setComponentLevel(componentName, level);
        });
    }

    /**
     * 应用组件静默设置
     * @param componentName 组件名称
     * @param silence 是否静默
     */
    private _applyComponentSilence(componentName: string, silence: boolean): void {
        if (silence) {
            Logger.silenceComponent(componentName);
        } else {
            Logger.unsilenceComponent(componentName);
        }
    }

    // ==================== 公共接口方法 ====================

    /**
     * 手动应用配置（供外部调用）
     */
    public applyConfiguration(): void {
        this._applyLogConfiguration();
    }

    /**
     * 切换到生产模式
     */
    public switchToProductionMode(): void {
        this.presetMode = LogPresetMode.PRODUCTION;
        this.usePresetMode = true;
        this._applyLogConfiguration();
    }

    /**
     * 切换到开发模式
     */
    public switchToDevelopmentMode(): void {
        this.presetMode = LogPresetMode.DEVELOPMENT;
        this.usePresetMode = true;
        this._applyLogConfiguration();
    }

    /**
     * 切换到测试模式
     */
    public switchToTestingMode(): void {
        this.presetMode = LogPresetMode.TESTING;
        this.usePresetMode = true;
        this._applyLogConfiguration();
    }

    /**
     * 临时启用组件日志（用于调试）
     * @param componentName 组件名称
     * @param level 日志级别
     */
    public temporaryEnableComponentLog(componentName: string, level: LogLevel = LogLevel.DEBUG): void {
        Logger.unsilenceComponent(componentName);
        Logger.setComponentLevel(componentName, level);

        if (this.showConfigLogs) {
            Logger.info('LogConfigManager', `临时启用组件日志: ${componentName} (${LogLevel[level]})`);
        }
    }

    /**
     * 恢复组件日志到配置状态
     * @param componentName 组件名称
     */
    public restoreComponentLog(componentName: string): void {
        this._applyLogConfiguration();

        if (this.showConfigLogs) {
            Logger.info('LogConfigManager', `已恢复组件日志配置: ${componentName}`);
        }
    }

    /**
     * 获取当前配置摘要
     * @returns 配置摘要字符串
     */
    public getConfigurationSummary(): string {
        if (this.usePresetMode) {
            return `预设模式: ${LogPresetMode[this.presetMode]}`;
        } else {
            return `详细配置 - 全局级别: ${LogLevel[this.globalLogLevel]}`;
        }
    }
}
