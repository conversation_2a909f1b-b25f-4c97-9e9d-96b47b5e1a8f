import { Logger } from './Logger';
import { TRANSLATION_CACHE_CONFIG } from '../Game/Constants/TranslationConstants';
import { sys } from 'cc';

/**
 * 缓存的翻译数据接口
 */
export interface CachedTranslation {
    /** 翻译结果 */
    translation: string;
    /** 缓存时间戳 */
    timestamp: number;
    /** 是否已过期 */
    isExpired(): boolean;
}

/**
 * 翻译缓存管理器
 * 
 * 负责翻译结果的本地缓存管理，提供高效的存取和过期清理功能。
 * 复用项目中成熟的缓存管理模式。
 * 
 * 核心功能：
 * - 翻译结果的内存缓存
 * - 本地存储持久化
 * - 过期数据自动清理
 * - LRU缓存策略
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-04
 */
export class TranslationCache {
    
    /**
     * 内存缓存
     */
    private static _memoryCache: Map<string, CachedTranslation> = new Map();
    
    /**
     * 缓存访问顺序（LRU）
     */
    private static _accessOrder: string[] = [];
    
    /**
     * 是否已初始化
     */
    private static _initialized: boolean = false;

    /**
     * 初始化缓存系统
     */
    public static initialize(): void {
        if (TranslationCache._initialized) {
            return;
        }

        try {
            // 从本地存储加载缓存
            TranslationCache._loadFromStorage();
            
            // 清理过期缓存
            TranslationCache._cleanExpiredCache();
            
            TranslationCache._initialized = true;
            Logger.info('TranslationCache', '翻译缓存系统初始化完成');
        } catch (error) {
            Logger.error('TranslationCache', '翻译缓存初始化失败', error as Error);
        }
    }

    /**
     * 获取翻译缓存
     * @param word 单词
     * @returns 缓存的翻译数据，如果不存在或已过期则返回null
     */
    public static get(word: string): CachedTranslation | null {
        if (!TranslationCache._initialized) {
            TranslationCache.initialize();
        }

        const key = word.toLowerCase();
        const cached = TranslationCache._memoryCache.get(key);
        
        if (!cached) {
            return null;
        }

        // 检查是否过期
        if (cached.isExpired()) {
            TranslationCache._memoryCache.delete(key);
            TranslationCache._removeFromAccessOrder(key);
            Logger.debug('TranslationCache', `缓存已过期: ${word}`);
            return null;
        }

        // 更新访问顺序
        TranslationCache._updateAccessOrder(key);
        
        Logger.debug('TranslationCache', `缓存命中: ${word}`);
        return cached;
    }

    /**
     * 设置翻译缓存
     * @param word 单词
     * @param translation 翻译结果
     */
    public static set(word: string, translation: string): void {
        if (!TranslationCache._initialized) {
            TranslationCache.initialize();
        }

        if (!word || !translation) {
            return;
        }

        const key = word.toLowerCase();
        const cachedData: CachedTranslation = {
            translation,
            timestamp: Date.now(),
            isExpired(): boolean {
                const expireTime = TRANSLATION_CACHE_CONFIG.EXPIRATION_HOURS * 60 * 60 * 1000;
                return Date.now() - this.timestamp > expireTime;
            }
        };

        // 检查缓存大小限制
        if (TranslationCache._memoryCache.size >= TRANSLATION_CACHE_CONFIG.MAX_SIZE) {
            TranslationCache._evictLRU();
        }

        TranslationCache._memoryCache.set(key, cachedData);
        TranslationCache._updateAccessOrder(key);
        
        // 异步保存到本地存储
        TranslationCache._saveToStorage();
        
        Logger.debug('TranslationCache', `缓存已保存: ${word} -> ${translation}`);
    }

    /**
     * 清空所有缓存
     */
    public static clear(): void {
        TranslationCache._memoryCache.clear();
        TranslationCache._accessOrder = [];
        
        try {
            sys.localStorage.removeItem(TRANSLATION_CACHE_CONFIG.STORAGE_KEY);
            Logger.info('TranslationCache', '翻译缓存已清空');
        } catch (error) {
            Logger.error('TranslationCache', '清空本地存储失败', error as Error);
        }
    }

    /**
     * 获取缓存统计信息
     */
    public static getStats(): { size: number; maxSize: number; hitRate: number } {
        return {
            size: TranslationCache._memoryCache.size,
            maxSize: TRANSLATION_CACHE_CONFIG.MAX_SIZE,
            hitRate: 0 // 可以后续添加命中率统计
        };
    }

    /**
     * 清理过期缓存
     */
    private static _cleanExpiredCache(): void {
        const expiredKeys: string[] = [];
        
        for (const [key, cached] of TranslationCache._memoryCache) {
            if (cached.isExpired()) {
                expiredKeys.push(key);
            }
        }

        for (const key of expiredKeys) {
            TranslationCache._memoryCache.delete(key);
            TranslationCache._removeFromAccessOrder(key);
        }

        // 静默清理过期缓存
    }

    /**
     * LRU淘汰策略
     */
    private static _evictLRU(): void {
        if (TranslationCache._accessOrder.length > 0) {
            const lruKey = TranslationCache._accessOrder.shift()!;
            TranslationCache._memoryCache.delete(lruKey);
        }
    }

    /**
     * 更新访问顺序
     */
    private static _updateAccessOrder(key: string): void {
        TranslationCache._removeFromAccessOrder(key);
        TranslationCache._accessOrder.push(key);
    }

    /**
     * 从访问顺序中移除
     */
    private static _removeFromAccessOrder(key: string): void {
        const index = TranslationCache._accessOrder.indexOf(key);
        if (index > -1) {
            TranslationCache._accessOrder.splice(index, 1);
        }
    }

    /**
     * 从本地存储加载缓存
     */
    private static _loadFromStorage(): void {
        try {
            const stored = sys.localStorage.getItem(TRANSLATION_CACHE_CONFIG.STORAGE_KEY);
            if (stored) {
                const data = JSON.parse(stored);
                for (const [key, value] of Object.entries(data)) {
                    const cachedData: CachedTranslation = {
                        translation: (value as any).translation,
                        timestamp: (value as any).timestamp,
                        isExpired(): boolean {
                            const expireTime = TRANSLATION_CACHE_CONFIG.EXPIRATION_HOURS * 60 * 60 * 1000;
                            return Date.now() - this.timestamp > expireTime;
                        }
                    };
                    
                    if (!cachedData.isExpired()) {
                        TranslationCache._memoryCache.set(key, cachedData);
                        TranslationCache._accessOrder.push(key);
                    }
                }
                Logger.info('TranslationCache', `从本地存储加载缓存: ${TranslationCache._memoryCache.size}个`);
            }
        } catch (error) {
            Logger.warn('TranslationCache', '从本地存储加载缓存失败', error as Error);
        }
    }

    /**
     * 保存缓存到本地存储
     */
    private static _saveToStorage(): void {
        try {
            const data: Record<string, any> = {};
            for (const [key, value] of TranslationCache._memoryCache) {
                data[key] = {
                    translation: value.translation,
                    timestamp: value.timestamp
                };
            }
            
            sys.localStorage.setItem(TRANSLATION_CACHE_CONFIG.STORAGE_KEY, JSON.stringify(data));
        } catch (error) {
            Logger.warn('TranslationCache', '保存缓存到本地存储失败', error as Error);
        }
    }
}
