import { Logger } from './Logger';
import { PRONUNCIATION_API_CONFIG } from '../Game/Constants/PronunciationConstants';

/**
 * 音频URL生成器
 * 
 * 负责生成有道词典语音API的请求URL，遵循单一职责原则。
 * 提供灵活的参数配置和URL构建功能。
 * 
 * 核心功能：
 * - 生成标准的有道词典语音API URL
 * - 支持英式/美式发音选择
 * - 参数验证和错误处理
 * - URL编码和格式化
 * 
 * API格式：https://dict.youdao.com/dictvoice?type=0&audio=单词
 * - type: 0=英式发音, 1=美式发音
 * - audio: 要发音的单词
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22
 */
export class AudioUrlGenerator {
    
    /**
     * 生成单词发音的音频URL
     * @param word 要发音的单词
     * @param type 发音类型（0=英式，1=美式），默认为英式
     * @returns 生成的音频URL，如果参数无效则返回null
     */
    public static generatePronunciationUrl(word: string, type: number = PRONUNCIATION_API_CONFIG.DEFAULT_TYPE): string | null {
        try {
            // 参数验证
            if (!AudioUrlGenerator._validateWord(word)) {
                Logger.error('AudioUrlGenerator', `无效的单词参数: "${word}"`);
                return null;
            }

            if (!AudioUrlGenerator._validateType(type)) {
                Logger.error('AudioUrlGenerator', `无效的发音类型: ${type}`);
                return null;
            }

            // 清理和格式化单词
            const cleanWord = AudioUrlGenerator._cleanWord(word);
            if (!cleanWord) {
                Logger.error('AudioUrlGenerator', `单词清理后为空: "${word}"`);
                return null;
            }

            // 构建URL
            const url = AudioUrlGenerator._buildUrl(cleanWord, type);
            return url;

        } catch (error) {
            Logger.error('AudioUrlGenerator', '生成音频URL时发生异常', error as Error);
            return null;
        }
    }

    /**
     * 批量生成多个单词的音频URL
     * @param words 单词数组
     * @param type 发音类型（0=英式，1=美式），默认为英式
     * @returns URL映射表，键为单词，值为对应的URL
     */
    public static generateBatchUrls(words: string[], type: number = PRONUNCIATION_API_CONFIG.DEFAULT_TYPE): Map<string, string> {
        const urlMap = new Map<string, string>();

        if (!Array.isArray(words) || words.length === 0) {
            Logger.warn('AudioUrlGenerator', '单词数组为空或无效');
            return urlMap;
        }

        let successCount = 0;
        let failureCount = 0;

        for (const word of words) {
            const url = AudioUrlGenerator.generatePronunciationUrl(word, type);
            if (url) {
                urlMap.set(word, url);
                successCount++;
            } else {
                failureCount++;
            }
        }

        Logger.info('AudioUrlGenerator', `批量生成URL完成: 成功${successCount}个, 失败${failureCount}个`);
        return urlMap;
    }

    /**
     * 验证单词参数是否有效
     * @param word 要验证的单词
     * @returns 是否有效
     */
    private static _validateWord(word: string): boolean {
        // 检查是否为字符串
        if (typeof word !== 'string') {
            return false;
        }

        // 检查是否为空或只包含空白字符
        if (!word || word.trim().length === 0) {
            return false;
        }

        // 检查长度限制（单词长度通常不超过50个字符）
        if (word.length > 50) {
            return false;
        }

        // 检查是否包含有效字符（字母、数字、连字符、撇号）
        const validPattern = /^[a-zA-Z0-9\-']+$/;
        if (!validPattern.test(word.trim())) {
            return false;
        }

        return true;
    }

    /**
     * 验证发音类型参数是否有效
     * @param type 发音类型
     * @returns 是否有效
     */
    private static _validateType(type: number): boolean {
        // 检查是否为数字
        if (typeof type !== 'number') {
            return false;
        }

        // 检查是否为有效值（0或1）
        if (type !== 0 && type !== 1) {
            return false;
        }

        return true;
    }

    /**
     * 清理和格式化单词
     * @param word 原始单词
     * @returns 清理后的单词
     */
    private static _cleanWord(word: string): string {
        // 去除首尾空白字符
        let cleanWord = word.trim();

        // 转换为小写（有道API对大小写不敏感，但统一使用小写）
        cleanWord = cleanWord.toLowerCase();

        // 移除多余的空格和特殊字符
        cleanWord = cleanWord.replace(/\s+/g, '');

        return cleanWord;
    }

    /**
     * 构建完整的API URL
     * @param word 清理后的单词
     * @param type 发音类型
     * @returns 完整的URL
     */
    private static _buildUrl(word: string, type: number): string {
        const baseUrl = PRONUNCIATION_API_CONFIG.BASE_URL;
        
        // URL编码单词参数
        const encodedWord = encodeURIComponent(word);
        
        // 构建查询参数
        const params = new URLSearchParams({
            type: type.toString(),
            audio: encodedWord
        });

        // 组合完整URL
        const fullUrl = `${baseUrl}?${params.toString()}`;
        
        return fullUrl;
    }

    /**
     * 解析音频URL中的单词和类型
     * @param url 音频URL
     * @returns 解析结果，包含单词和类型，如果解析失败则返回null
     */
    public static parseAudioUrl(url: string): { word: string; type: number } | null {
        try {
            if (!url || typeof url !== 'string') {
                return null;
            }

            const urlObj = new URL(url);
            
            // 检查是否为有道词典API
            if (!urlObj.hostname.includes('youdao.com')) {
                return null;
            }

            // 提取参数
            const audioParam = urlObj.searchParams.get('audio');
            const typeParam = urlObj.searchParams.get('type');

            if (!audioParam || !typeParam) {
                return null;
            }

            const word = decodeURIComponent(audioParam);
            const type = parseInt(typeParam, 10);

            // 验证解析结果
            if (!AudioUrlGenerator._validateWord(word) || !AudioUrlGenerator._validateType(type)) {
                return null;
            }

            return { word, type };

        } catch (error) {
            Logger.error('AudioUrlGenerator', '解析音频URL失败', error as Error);
            return null;
        }
    }

    /**
     * 检查URL是否为有效的有道词典语音API URL
     * @param url 要检查的URL
     * @returns 是否为有效的语音API URL
     */
    public static isValidPronunciationUrl(url: string): boolean {
        const parsed = AudioUrlGenerator.parseAudioUrl(url);
        return parsed !== null;
    }

    /**
     * 获取发音类型的描述文本
     * @param type 发音类型
     * @returns 类型描述
     */
    public static getTypeDescription(type: number): string {
        switch (type) {
            case 0:
                return '英式发音';
            case 1:
                return '美式发音';
            default:
                return '未知类型';
        }
    }


}
