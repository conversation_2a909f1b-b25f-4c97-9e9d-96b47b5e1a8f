import { Component, _decorator } from 'cc';
import { Logger } from '../../Utils/Logger';
import { WordPronunciationManager } from '../../Core/WordPronunciationManager';
import { PRONUNCIATION_PERFORMANCE_CONFIG } from '../Constants/PronunciationConstants';

const { ccclass, property } = _decorator;

/**
 * 单词发音服务
 * 
 * 作为Game Logic层的服务组件，负责封装单词发音的业务逻辑。
 * 遵循单一职责原则，专注于发音功能的业务逻辑处理。
 * 
 * 核心职责：
 * - 封装发音功能的业务逻辑
 * - 提供游戏层面的发音接口
 * - 处理发音相关的游戏事件
 * - 管理发音功能的游戏状态
 * 
 * 设计特点：
 * - 业务逻辑封装，隔离底层实现
 * - 游戏事件集成，响应游戏状态变化
 * - 性能优化，避免重复请求
 * - 错误处理，提供优雅降级
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22
 */
@ccclass('WordPronunciationService')
export class WordPronunciationService extends Component {
    
    /**
     * 单例实例
     */
    private static _instance: WordPronunciationService | null = null;
    
    // 🔧 已移除重复播放缓存 - 允许自由重复播放
    
    /**
     * 播放队列（处理快速连续的发音请求）
     */
    private _playQueue: string[] = [];
    
    /**
     * 是否正在播放
     */
    private _isPlaying: boolean = false;
    
    /**
     * 发音管理器引用
     */
    private _pronunciationManager: WordPronunciationManager | null = null;

    // 🔧 已移除重复播放控制 - 简化发音系统

    /**
     * 获取单例实例
     * @returns WordPronunciationService实例或null
     */
    public static getInstance(): WordPronunciationService | null {
        return WordPronunciationService._instance;
    }

    /**
     * 组件加载时的初始化
     */
    onLoad(): void {
        // 单例模式实现
        if (WordPronunciationService._instance === null) {
            WordPronunciationService._instance = this;
            Logger.info('WordPronunciationService', '单词发音服务初始化完成');
        } else {
            // 销毁重复实例
            Logger.warn('WordPronunciationService', '检测到重复实例，销毁当前节点');
            this.node.destroy();
            return;
        }
    }

    /**
     * 组件启动时的设置
     */
    start(): void {
        if (WordPronunciationService._instance === this) {
            this._setupService();
        }
    }

    /**
     * 组件销毁时的清理
     */
    onDestroy(): void {
        // 检查是否在持久化节点中，如果是则不清理单例实例
        if (this.node.parent && this.node.parent.name === '管理器') {
            Logger.info('WordPronunciationService', '发音服务在持久化节点中，跳过单例清理');
            return;
        }

        if (WordPronunciationService._instance === this) {
            this._cleanup();
            WordPronunciationService._instance = null;
            Logger.info('WordPronunciationService', '单词发音服务已清理');
        }
    }

    /**
     * 播放单词发音（主要接口）
     * @param word 要发音的单词
     * @param force 是否强制播放（忽略重复检查）
     * @returns 是否成功启动播放
     */
    public async playWordPronunciation(word: string, force: boolean = false): Promise<boolean> {
        try {
            // 参数验证
            if (!word || typeof word !== 'string' || word.trim().length === 0) {
                Logger.warn('WordPronunciationService', '无效的单词参数');
                return false;
            }

            const cleanWord = word.trim().toLowerCase();

            // 检查发音管理器，如果不存在或不可用则尝试重新连接
            if (!this._pronunciationManager || !this._pronunciationManager.isAvailable()) {
                Logger.warn('WordPronunciationService', '发音管理器不可用，尝试重新连接');
                this._reconnectToManager();

                // 重新检查管理器状态
                if (!this._pronunciationManager || !this._pronunciationManager.isAvailable()) {
                    Logger.warn('WordPronunciationService', '重新连接后发音管理器仍不可用');
                    return false;
                }
            }

            // 🔧 已移除重复播放检查 - 允许自由重复播放任何单词

            // 添加到播放队列
            this._playQueue.push(cleanWord);
            Logger.info('WordPronunciationService', `单词 "${cleanWord}" 已添加到播放队列 (队列长度: ${this._playQueue.length})`);

            // 处理播放队列
            this._processPlayQueue();

            return true;

        } catch (error) {
            Logger.error('WordPronunciationService', `播放单词 "${word}" 发音时发生异常`, error as Error);
            return false;
        }
    }

    /**
     * 批量预加载单词发音
     * @param words 单词数组
     * @returns 预加载是否成功
     */
    public async preloadWordPronunciations(words: string[]): Promise<boolean> {
        if (!this._pronunciationManager) {
            Logger.warn('WordPronunciationService', '发音管理器不可用，无法预加载');
            return false;
        }

        if (!Array.isArray(words) || words.length === 0) {
            Logger.warn('WordPronunciationService', '预加载单词列表为空');
            return false;
        }

        try {
            // 清理和去重单词列表
            const cleanWords = words
                .map(word => word.trim().toLowerCase())
                .filter(word => word.length > 0)
                .filter((word, index, array) => array.indexOf(word) === index);

            if (cleanWords.length === 0) {
                Logger.warn('WordPronunciationService', '清理后的单词列表为空');
                return false;
            }

            Logger.info('WordPronunciationService', `开始预加载${cleanWords.length}个单词的发音`);

            const result = await this._pronunciationManager.preloadWordPronunciations(cleanWords);
            
            if (result) {
                Logger.success('WordPronunciationService', '单词发音预加载成功');
            } else {
                Logger.warn('WordPronunciationService', '单词发音预加载失败');
            }

            return result;

        } catch (error) {
            Logger.error('WordPronunciationService', '预加载单词发音时发生异常', error as Error);
            return false;
        }
    }

    /**
     * 检查发音功能是否启用
     * @returns 是否启用
     */
    public isEnabled(): boolean {
        // 🔧 如果管理器不存在，尝试重新连接
        if (!this._pronunciationManager) {
            Logger.debug('WordPronunciationService', 'isEnabled检查时发现管理器不存在，尝试重新连接');
            this._reconnectToManager();
        }

        // 🔧 检查管理器是否可用
        const isAvailable = this._pronunciationManager?.isAvailable() || false;

        if (!isAvailable && this._pronunciationManager) {
            Logger.debug('WordPronunciationService', 'isEnabled检查时发现管理器不可用');
        }

        return isAvailable;
    }

    /**
     * 获取发音服务状态
     * @returns 服务状态信息
     */
    public getServiceStatus(): {
        enabled: boolean;
        isPlaying: boolean;
        queueLength: number;
        managerAvailable: boolean;
    } {
        return {
            enabled: this.isEnabled(),
            isPlaying: this._isPlaying,
            queueLength: this._playQueue.length,
            managerAvailable: this._pronunciationManager !== null
        };
    }

    /**
     * 清空播放队列
     */
    public clearPlayQueue(): void {
        this._playQueue = [];
        Logger.info('WordPronunciationService', '播放队列已清空');
    }

    // 🔧 已移除重复播放缓存相关方法 - 简化API接口

    /**
     * 设置服务
     */
    private _setupService(): void {
        try {
            // 获取发音管理器实例
            this._pronunciationManager = WordPronunciationManager.getInstance();

            if (!this._pronunciationManager) {
                Logger.warn('WordPronunciationService', '未找到发音管理器实例');
                return;
            }

            Logger.info('WordPronunciationService', '发音服务设置完成');

        } catch (error) {
            Logger.error('WordPronunciationService', '发音服务设置失败', error as Error);
        }
    }

    /**
     * 重新连接到发音管理器
     * 用于场景切换后重新建立连接
     */
    private _reconnectToManager(): void {
        try {
            Logger.info('WordPronunciationService', '尝试重新连接发音管理器');

            // 重新获取发音管理器实例
            this._pronunciationManager = WordPronunciationManager.getInstance();

            if (this._pronunciationManager) {
                // 🔧 验证管理器是否真正可用
                const isAvailable = this._pronunciationManager.isAvailable();
                const config = this._pronunciationManager.getConfig();

                if (isAvailable && config.enabled) {
                    Logger.success('WordPronunciationService', '发音管理器重新连接成功且功能可用');
                } else {
                    Logger.warn('WordPronunciationService', `发音管理器重新连接成功但功能不可用: 可用=${isAvailable}, 启用=${config.enabled}`);

                    // 🔧 如果管理器不可用，尝试强制重新初始化
                    if (!isAvailable) {
                        Logger.info('WordPronunciationService', '尝试强制重新初始化发音管理器');
                        this._pronunciationManager.forceReinitialize();
                    }
                }
            } else {
                Logger.warn('WordPronunciationService', '发音管理器重新连接失败 - 实例不存在');
            }

        } catch (error) {
            Logger.error('WordPronunciationService', '重新连接发音管理器时发生异常', error as Error);
        }
    }

    /**
     * 处理播放队列
     */
    private async _processPlayQueue(): Promise<void> {
        // 如果正在播放或队列为空，直接返回
        if (this._isPlaying || this._playQueue.length === 0) {
            return;
        }

        this._isPlaying = true;

        try {
            while (this._playQueue.length > 0) {
                const word = this._playQueue.shift();
                if (!word) continue;

                Logger.info('WordPronunciationService', `开始处理队列中的单词: "${word}"`);

                // 播放发音
                const success = await this._playWordInternal(word);

                if (success) {
                    Logger.info('WordPronunciationService', `单词 "${word}" 播放成功`);
                } else {
                    Logger.warn('WordPronunciationService', `单词 "${word}" 播放失败`);
                }

                // 添加播放间隔，避免播放过于频繁
                if (this._playQueue.length > 0) {
                    await this._delay(PRONUNCIATION_PERFORMANCE_CONFIG.PLAY_DELAY);
                }
            }

        } catch (error) {
            Logger.error('WordPronunciationService', '处理播放队列时发生异常', error as Error);
        } finally {
            this._isPlaying = false;
        }
    }

    /**
     * 内部播放方法
     * @param word 单词
     * @returns 是否成功
     */
    private async _playWordInternal(word: string): Promise<boolean> {
        if (!this._pronunciationManager) {
            Logger.error('WordPronunciationService', `单词 "${word}" 播放失败：发音管理器不存在`);
            return false;
        }

        try {
            Logger.info('WordPronunciationService', `调用发音管理器播放单词: "${word}"`);
            const result = await this._pronunciationManager.playWordPronunciation(word);

            if (result) {
                Logger.success('WordPronunciationService', `单词 "${word}" 发音播放成功`);
            } else {
                Logger.warn('WordPronunciationService', `单词 "${word}" 发音播放失败 - 管理器返回false`);
            }

            return result;

        } catch (error) {
            Logger.error('WordPronunciationService', `播放单词 "${word}" 时发生异常`, error as Error);
            return false;
        }
    }

    /**
     * 延迟工具方法
     * @param ms 延迟毫秒数
     */
    private _delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 清理资源
     */
    private _cleanup(): void {
        try {
            // 清空队列
            this.clearPlayQueue();

            // 重置状态
            this._isPlaying = false;
            this._pronunciationManager = null;

            Logger.info('WordPronunciationService', '发音服务资源清理完成');

        } catch (error) {
            Logger.error('WordPronunciationService', '发音服务清理失败', error as Error);
        }
    }
}
