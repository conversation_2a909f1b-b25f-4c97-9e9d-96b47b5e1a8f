import { IWordData } from '../../Data/WordDatabase';
import { Logger } from '../Logger';
import { GameUtils } from '../GameUtils';
import { LetterDistributionManager } from './LetterDistributionManager';
import { SpatialDistributionManager } from './SpatialDistributionManager';
import { GridSpatialAnalyzer } from './GridSpatialAnalyzer';
// 移除WordPlacementValidator导入，功能已整合到本文件
import { SpatialQualityEvaluator } from './SpatialQualityEvaluator';

/**
 * 单词放置验证报告接口
 */
export interface WordPlacementReport {
    isComplete: boolean;
    totalWords: number;
    placedWords: PlacedWordInfo[];
    missingWords: string[];
    gridFillRate: number;
    qualityScore: number;
    directionDistribution: Map<string, number>;
    errors: string[];
    suggestions: string[];
}

/**
 * 已放置单词信息接口
 */
export interface PlacedWordInfo {
    word: string;
    startRow: number;
    startCol: number;
    endRow: number;
    endCol: number;
    direction: string;
    length: number;
    sharedLetters: number;
}

/**
 * 强制放置结果接口
 */
export interface ForcePlacementResult {
    success: boolean;
    placedWords: string[];
    failedWords: string[];
    modifiedGrid: string[][];
    conflicts: string[];
}

/**
 * 方向分配策略接口
 */
export interface DirectionAllocationStrategy {
    directions: DirectionInfo[];
    allocation: Map<string, number>; // 方向名称 -> 分配的单词数量
    totalWords: number;
}

/**
 * 方向信息接口
 */
export interface DirectionInfo {
    name: string;
    dr: number;
    dc: number;
    priority: number;
    allocatedCount: number;
}

/**
 * 🎯 智能单词放置引擎 v4.0
 *
 * 专业的8方向均匀分布算法，适配任意数量的单词，确保最优的空间分布和随机性。
 * 遵循单一职责原则，专注于单词放置的核心算法逻辑。
 *
 * 🚀 核心特性：
 * - 智能8方向均匀分布：自动适配任意数量单词到8个方向
 * - 专业算法设计：多层回退策略确保高成功率
 * - 空间分布优化：网格分区系统避免单词聚集
 * - 高性能实现：优化的算法减少计算开销
 *
 * 🎲 算法策略：
 * 1. 智能方向分配：≤8个单词时每个使用不同方向，>8个单词时均匀分配
 * 2. 优先级排序：长单词优先，确保更好的交叉效果
 * 3. 分区位置生成：3x3分区系统确保分散分布
 * 4. 多层回退机制：智能算法→回溯算法→宽松策略→保底方案
 *
 * 📊 验证标准：
 * - 8个方向均匀分布（根据单词数量智能分配）
 * - 空间分散性（避免过度集中）
 * - 布局随机性（每次生成不同结果）
 * - 高成功率（多层策略保证）
 *
 * 🔧 网格规格：
 * - 网格大小：9行 × 8列 = 72个字母位置
 * - 支持方向：水平、垂直、对角线（8个方向）
 * - 单词长度：通常为5个字母
 * - 单词数量：适配任意数量（智能分配到8个方向）
 *
 * <AUTHOR>
 * @version 4.0 - 智能8方向分布版本
 * @since 2025-07-23
 */
export class WordPlacementEngine {

    // 网格常量
    private static readonly GRID_ROWS = 9;
    private static readonly GRID_COLS = 8;
    private static readonly TOTAL_LETTERS = 72;

    /**
     * 从单词生成字母数组 - 主入口方法
     * @param words 单词数据数组
     * @returns 字母字符数组
     */
    public static generateLettersFromWords(words: IWordData[]): string[] {
        if (GameUtils.isArrayEmpty(words)) {
            Logger.error('WordPlacementEngine', '单词列表为空！');
            return [];
        }

        // 尝试生成一个包含所有单词的字母网格
        const gridResult = this.generateCrosswordGrid(words);

        if (gridResult.success) {
            return gridResult.letters;
        } else {
            Logger.warn('WordPlacementEngine', '无法生成交叉网格，使用简化策略');
            const simplifiedResult = this._generateSimplifiedGrid(words);
            return simplifiedResult.letters;
        }
    }

    /**
     * 生成单词搜索网格 - 使用确定性放置策略
     * @param words 单词数据数组
     * @returns 生成结果
     */
    public static generateCrosswordGrid(words: IWordData[]): { success: boolean, letters: string[] } {
        // 🎲 随机性增强：在每次生成开始时重置随机状态
        this._enhanceRandomSeed();

        // 创建空网格
        const grid = this._createEmptyGrid();

        // 🔧 优化：多阶段重试策略
        let result = { success: false };
        let attempts = 0;
        const maxAttempts = 60; // 增加重试次数

        while (!result.success && attempts < maxAttempts) {
            attempts++;

            // 重新创建空网格
            const freshGrid = this._createEmptyGrid();

            // 🎯 简化的两层回退策略
            if (attempts <= 40) {
                // 第一阶段：回溯算法（最有效的策略）
                result = this._placeWordsWithBacktracking(freshGrid, words);
            } else {
                // 第二阶段：随机分布策略（保底策略）
                // 使用保底策略
                result = this._placeWordsWithRandomStrategy(freshGrid, words);
            }

            if (result.success) {
                // 成功时复制网格
                this._copyGridTo(freshGrid, grid);
                break;
            } else if (attempts % 10 === 0) {
                Logger.warn('WordPlacementEngine', `已尝试${attempts}次，继续重试...`);
            }
        }

        if (!result.success) {
            Logger.warn('WordPlacementEngine', `所有策略失败，已尝试${attempts}次`);
            Logger.warn('WordPlacementEngine', `单词列表: ${words.map(w => w.word).join(', ')}`);
            return this._generateSimplifiedGrid(words);
        }

        // 🔧 关键步骤：生成完整性验证
        const verificationReport = this._verifyWordPlacement(grid, words);

        if (!verificationReport.isComplete) {
            Logger.warn('WordPlacementEngine', `生成验证失败：${verificationReport.missingWords.length}个单词未放置`);
            Logger.warn('WordPlacementEngine', `缺失单词: ${verificationReport.missingWords.join(', ')}`);

            // 🔧 尝试补救缺失的单词
            const remedyResult = this._remedyMissingWords(grid, verificationReport.missingWords, words);

            if (remedyResult.success) {
                Logger.info('WordPlacementEngine', `补救成功：已放置${remedyResult.placedWords.length}个缺失单词`);
                // 重新验证
                const finalReport = this._verifyWordPlacement(remedyResult.modifiedGrid, words);
                if (finalReport.isComplete) {
                    this._copyGridTo(remedyResult.modifiedGrid, grid);
                } else {
                    Logger.warn('WordPlacementEngine', '补救后仍有单词缺失，触发强制完整性保证');
                    const forceResult = this._forceCompleteGeneration(grid, words);
                    if (forceResult.success) {
                        this._copyGridTo(forceResult.modifiedGrid, grid);
                    } else {
                        Logger.warn('WordPlacementEngine', '强制完整性保证失败，返回简化网格');
                        return this._generateSimplifiedGrid(words);
                    }
                }
            } else {
                Logger.warn('WordPlacementEngine', '补救失败，触发强制完整性保证');
                const forceResult = this._forceCompleteGeneration(grid, words);
                if (forceResult.success) {
                    this._copyGridTo(forceResult.modifiedGrid, grid);
                } else {
                    Logger.warn('WordPlacementEngine', '强制完整性保证失败，返回简化网格');
                    return this._generateSimplifiedGrid(words);
                }
            }
        }

        // 填充剩余空位
        this._fillEmptyPositions(grid);

        // 优化字母分布，避免连续字母问题
        LetterDistributionManager.optimizeDistribution(grid);

        // 🔧 最终验证：确保所有单词都在最终网格中
        const finalVerification = this._verifyWordPlacement(grid, words);
        if (!finalVerification.isComplete) {
            Logger.warn('WordPlacementEngine', '最终验证失败！强制重新生成');
            return this._generateSimplifiedGrid(words);
        }

        // 输出验证报告
        this.outputVerificationReport(finalVerification);

        // 转换为一维数组
        const letters = this._gridToArray(grid);

        return {
            success: true,
            letters: letters
        };
    }

    /**
     * 创建空网格
     * @returns 9行8列的空网格
     */
    private static _createEmptyGrid(): string[][] {
        const grid: string[][] = [];
        for (let row = 0; row < this.GRID_ROWS; row++) {
            grid[row] = [];
            for (let col = 0; col < this.GRID_COLS; col++) {
                grid[row][col] = '';
            }
        }
        return grid;
    }

    /**
     * 🎯 随机分布策略 - 使用随机位置生成的简化算法
     * 适配任意数量的单词，实现方向分配和随机空间分布
     * @param grid 网格
     * @param words 单词数据
     * @returns 放置结果
     */
    private static _placeWordsWithRandomStrategy(grid: string[][], words: IWordData[]): { success: boolean } {
        // 启动随机分布算法

        // 1. 创建方向分配策略
        const strategy = this._createIntelligentDirectionStrategy(words);

        // 2. 按优先级排序单词（长单词优先，确保更好的交叉效果）
        const prioritizedWords = this._prioritizeWords(words);

        // 3. 使用随机位置生成执行放置
        const placementResult = this._executeSimplifiedPlacement(grid, prioritizedWords, strategy);

        if (placementResult.success) {
            // 随机分布算法成功
            return { success: true };
        } else {
            // 随机分布算法部分成功
            return { success: false };
        }
    }

    /**
     * 使用专业回溯算法放置所有单词 - 强制8方向分布版本
     * 确保8个单词严格分布在8个不同方向上
     * @param grid 网格
     * @param words 单词数据
     * @returns 放置结果
     */
    private static _placeWordsWithBacktracking(grid: string[][], words: IWordData[]): { success: boolean } {
        // 定义8个方向
        const allDirections = [
            { name: 'rightdown', dr: 1, dc: 1 },   // 对角线右下
            { name: 'leftup', dr: -1, dc: -1 },    // 对角线左上
            { name: 'rightup', dr: -1, dc: 1 },    // 对角线右上
            { name: 'leftdown', dr: 1, dc: -1 },   // 对角线左下
            { name: 'right', dr: 0, dc: 1 },       // 水平向右
            { name: 'left', dr: 0, dc: -1 },       // 水平向左
            { name: 'down', dr: 1, dc: 0 },        // 垂直向下
            { name: 'up', dr: -1, dc: 0 }          // 垂直向上
        ];

        // 创建方向分配策略：为每个单词预分配一个唯一方向
        const directionAssignment = this._createDirectionAssignment(words, allDirections);

        // 生成分散的位置序列
        const positions = SpatialDistributionManager.generateDistributedPositions();

        // 使用栈实现回溯算法，严格按照方向分配
        const stack: Array<{
            grid: string[][],
            wordIndex: number,
            positions: number[],
            placedWords: Array<{word: string, direction: string, row: number, col: number}>
        }> = [];

        // 初始化栈
        stack.push({
            grid: this._copyGrid(grid),
            wordIndex: 0,
            positions: [...positions],
            placedWords: []
        });

        while (stack.length > 0) {
            const current = stack[stack.length - 1];

            // 如果所有单词都已放置，成功
            if (current.wordIndex >= directionAssignment.length) {
                // 复制最终网格状态
                this._copyGridTo(current.grid, grid);
                return { success: true };
            }

            const assignment = directionAssignment[current.wordIndex];
            const word = assignment.word.toUpperCase();
            const direction = assignment.direction;
            let placed = false;

            // 尝试所有可用位置
            while (current.positions.length > 0 && !placed) {
                const pos = current.positions.pop()!;
                const row = Math.floor(pos / 8);
                const col = pos % 8;

                const result = this._tryPlaceWordAt(current.grid, word, row, col, direction);

                if (result.success) {
                    // 记录放置信息
                    const newPlacedWords = [...current.placedWords, {
                        word: word,
                        direction: direction.name,
                        row: row,
                        col: col
                    }];

                    // 推入下一个单词的状态
                    stack.push({
                        grid: result.newGrid,
                        wordIndex: current.wordIndex + 1,
                        positions: SpatialDistributionManager.generateSpatiallyAwarePositions(result.newGrid, newPlacedWords), // 🔧 空间感知位置生成
                        placedWords: newPlacedWords
                    });
                    placed = true;
                }
            }

            if (!placed) {
                // 🔧 优化：增强回溯机制
                stack.pop();

                // 如果回溯到第一个单词仍然失败，尝试重新分配方向
                if (stack.length === 0 && current.wordIndex === 0) {
                    Logger.warn('WordPlacementEngine', '当前方向分配无法完成，尝试重新分配');
                    // 这里可以触发重新分配方向的逻辑
                    return { success: false };
                }
            }
        }

        Logger.error('WordPlacementEngine', '强制8方向分布算法失败，无法放置所有单词');
        return { success: false };
    }

    /**
     * 创建方向分配策略（优化版本）
     * 确保8个单词严格分布在8个不同方向，长单词优先分配对角线方向
     * @param words 单词数组
     * @param directions 方向数组
     * @returns 方向分配结果
     */
    private static _createDirectionAssignment(words: IWordData[], directions: any[]): Array<{word: string, direction: any}> {
        const assignment: Array<{word: string, direction: any}> = [];

        // 🔧 优化策略1：按单词长度排序，长单词优先，相同长度随机排序
        const sortedWords = [...words].sort((a, b) => {
            const lengthDiff = b.word.length - a.word.length;
            if (lengthDiff !== 0) {
                return lengthDiff;
            }
            // 相同长度的单词随机排序
            return Math.random() - 0.5;
        });

        // 🔧 优化策略2：分类方向 - 对角线方向适合长单词，直线方向适合短单词
        const diagonalDirections = directions.filter(d =>
            d.name.includes('up') || d.name.includes('down')
        ).filter(d => d.dr !== 0 && d.dc !== 0);

        const straightDirections = directions.filter(d =>
            d.dr === 0 || d.dc === 0
        );

        // 打乱方向顺序以增加随机性（每次调用都重新打乱）
        this._shuffleArray(diagonalDirections);
        this._shuffleArray(straightDirections);

        // 额外的随机性增强：再次打乱
        this._shuffleArray(diagonalDirections);
        this._shuffleArray(straightDirections);

        // 🔧 优化策略3：智能分配 - 长单词优先分配对角线方向
        const availableDirections = [...diagonalDirections, ...straightDirections];
        const usedDirections = new Set<string>();

        for (let i = 0; i < sortedWords.length && assignment.length < 8; i++) {
            const word = sortedWords[i];

            // 为当前单词找到最适合的未使用方向（增加随机性）
            let selectedDirection = null;
            let candidateDirections: any[] = [];

            // 长单词(5字母)优先选择对角线方向，但从可用的对角线方向中随机选择
            if (word.word.length >= 5) {
                candidateDirections = diagonalDirections.filter(d => !usedDirections.has(d.name));
                if (candidateDirections.length > 0) {
                    // 从候选方向中随机选择
                    const randomIndex = Math.floor(Math.random() * candidateDirections.length);
                    selectedDirection = candidateDirections[randomIndex];
                }
            }

            // 如果没有合适的对角线方向，或者是短单词，从直线方向中随机选择
            if (!selectedDirection) {
                candidateDirections = straightDirections.filter(d => !usedDirections.has(d.name));
                if (candidateDirections.length > 0) {
                    const randomIndex = Math.floor(Math.random() * candidateDirections.length);
                    selectedDirection = candidateDirections[randomIndex];
                }
            }

            // 如果还没有找到，从所有剩余方向中随机选择
            if (!selectedDirection) {
                candidateDirections = availableDirections.filter(d => !usedDirections.has(d.name));
                if (candidateDirections.length > 0) {
                    const randomIndex = Math.floor(Math.random() * candidateDirections.length);
                    selectedDirection = candidateDirections[randomIndex];
                }
            }

            if (selectedDirection) {
                assignment.push({
                    word: word.word,
                    direction: selectedDirection
                });
                usedDirections.add(selectedDirection.name);
            }
        }

        // 🔧 诊断：确保正好有8个分配
        if (assignment.length !== 8) {
            Logger.warn('WordPlacementEngine', `方向分配异常：期望8个，实际${assignment.length}个`);
            Logger.warn('WordPlacementEngine', `输入单词数量: ${words.length}, 可用方向数量: ${directions.length}`);
        }

        // 🔧 诊断：输出分配结果
        const assignmentSummary = assignment.map(a => `${a.word}(${a.word.length}字母)->${a.direction.name}`).join(', ');
        Logger.debug('WordPlacementEngine', `方向分配完成: ${assignmentSummary}`);

        return assignment;
    }

    /**
     * 生成高级空间分布位置序列
     * 实现象限均匀分布、最小距离约束和随机性优化
     * @returns 优化的位置数组
     */
    private static _generateDistributedPositions(): number[] {
        Logger.info('WordPlacementEngine', '🎯 启动高级空间分布算法');

        // 🔧 步骤1：创建象限分布系统
        const quadrants = this._createQuadrantSystem();

        // 🔧 步骤2：为每个象限生成候选位置
        const quadrantPositions = this._generateQuadrantPositions(quadrants);

        // 🔧 步骤3：应用空间分散策略
        const distributedPositions = this._applySpatialDistributionStrategy(quadrantPositions);

        // 🔧 步骤4：添加随机性增强
        const finalPositions = this._enhanceRandomness(distributedPositions);

        Logger.info('WordPlacementEngine', `✅ 生成 ${finalPositions.length} 个分布式位置`);
        return finalPositions;
    }

    /**
     * 创建象限系统 - 将9x8网格划分为4个象限
     * @returns 象限信息数组
     */
    private static _createQuadrantSystem(): QuadrantInfo[] {
        const quadrants: QuadrantInfo[] = [];

        // 9x8网格的象限划分
        const midRow = Math.floor(this.GRID_ROWS / 2); // 4
        const midCol = Math.floor(this.GRID_COLS / 2); // 4

        // 象限1：左上 (0-4, 0-3)
        quadrants.push({
            id: 1,
            startRow: 0,
            endRow: midRow,
            startCol: 0,
            endCol: midCol - 1,
            centerRow: Math.floor(midRow / 2),
            centerCol: Math.floor((midCol - 1) / 2),
            wordCount: 0,
            positions: []
        });

        // 象限2：右上 (0-4, 4-7)
        quadrants.push({
            id: 2,
            startRow: 0,
            endRow: midRow,
            startCol: midCol,
            endCol: this.GRID_COLS - 1,
            centerRow: Math.floor(midRow / 2),
            centerCol: midCol + Math.floor((this.GRID_COLS - midCol) / 2),
            wordCount: 0,
            positions: []
        });

        // 象限3：左下 (5-8, 0-3)
        quadrants.push({
            id: 3,
            startRow: midRow + 1,
            endRow: this.GRID_ROWS - 1,
            startCol: 0,
            endCol: midCol - 1,
            centerRow: midRow + 1 + Math.floor((this.GRID_ROWS - midRow - 1) / 2),
            centerCol: Math.floor((midCol - 1) / 2),
            wordCount: 0,
            positions: []
        });

        // 象限4：右下 (5-8, 4-7)
        quadrants.push({
            id: 4,
            startRow: midRow + 1,
            endRow: this.GRID_ROWS - 1,
            startCol: midCol,
            endCol: this.GRID_COLS - 1,
            centerRow: midRow + 1 + Math.floor((this.GRID_ROWS - midRow - 1) / 2),
            centerCol: midCol + Math.floor((this.GRID_COLS - midCol) / 2),
            wordCount: 0,
            positions: []
        });

        Logger.info('WordPlacementEngine', `📐 创建4象限系统：${quadrants.map(q => `Q${q.id}(${q.startRow}-${q.endRow},${q.startCol}-${q.endCol})`).join(', ')}`);
        return quadrants;
    }

    /**
     * 为每个象限生成候选位置
     * @param quadrants 象限信息
     * @returns 象限位置映射
     */
    private static _generateQuadrantPositions(quadrants: QuadrantInfo[]): Map<number, number[]> {
        const quadrantPositions = new Map<number, number[]>();

        for (const quadrant of quadrants) {
            const positions: number[] = [];

            // 生成象限内的所有位置
            for (let row = quadrant.startRow; row <= quadrant.endRow; row++) {
                for (let col = quadrant.startCol; col <= quadrant.endCol; col++) {
                    const position = row * this.GRID_COLS + col;
                    positions.push(position);
                }
            }

            // 按距离象限中心的远近排序，优先选择分散的位置
            positions.sort((a, b) => {
                const rowA = Math.floor(a / this.GRID_COLS);
                const colA = a % this.GRID_COLS;
                const rowB = Math.floor(b / this.GRID_COLS);
                const colB = b % this.GRID_COLS;

                const distA = Math.abs(rowA - quadrant.centerRow) + Math.abs(colA - quadrant.centerCol);
                const distB = Math.abs(rowB - quadrant.centerRow) + Math.abs(colB - quadrant.centerCol);

                return distB - distA; // 距离远的优先
            });

            quadrantPositions.set(quadrant.id, positions);
            Logger.info('WordPlacementEngine', `📍 象限${quadrant.id}生成${positions.length}个候选位置`);
        }

        return quadrantPositions;
    }

    /**
     * 应用空间分散策略
     * @param quadrantPositions 象限位置映射
     * @returns 分散的位置数组
     */
    private static _applySpatialDistributionStrategy(quadrantPositions: Map<number, number[]>): number[] {
        const distributedPositions: number[] = [];
        const minDistance = 2; // 单词起始位置之间的最小距离

        // 🔧 策略1：象限轮转分布 - 确保每个象限都有单词
        const quadrantIds = [1, 2, 3, 4];
        GameUtils.shuffleArray(quadrantIds); // 随机化象限访问顺序

        let currentQuadrantIndex = 0;
        let positionsPerQuadrant = Math.ceil(72 / 4); // 每象限大约18个位置

        for (let i = 0; i < 72; i++) {
            const currentQuadrant = quadrantIds[currentQuadrantIndex];
            const quadrantPositions_current = quadrantPositions.get(currentQuadrant) || [];

            // 从当前象限选择一个位置，确保与已选位置保持最小距离
            let selectedPosition = -1;

            for (const position of quadrantPositions_current) {
                if (this._isPositionValidForDistribution(position, distributedPositions, minDistance)) {
                    selectedPosition = position;
                    break;
                }
            }

            // 如果当前象限没有合适位置，尝试其他象限
            if (selectedPosition === -1) {
                for (const quadrantId of quadrantIds) {
                    if (quadrantId === currentQuadrant) continue;

                    const altQuadrantPositions = quadrantPositions.get(quadrantId) || [];
                    for (const position of altQuadrantPositions) {
                        if (this._isPositionValidForDistribution(position, distributedPositions, minDistance)) {
                            selectedPosition = position;
                            break;
                        }
                    }
                    if (selectedPosition !== -1) break;
                }
            }

            // 如果仍然没有找到，降低距离要求
            if (selectedPosition === -1) {
                for (const quadrantId of quadrantIds) {
                    const quadrantPositions_fallback = quadrantPositions.get(quadrantId) || [];
                    for (const position of quadrantPositions_fallback) {
                        if (!distributedPositions.includes(position)) {
                            selectedPosition = position;
                            break;
                        }
                    }
                    if (selectedPosition !== -1) break;
                }
            }

            if (selectedPosition !== -1) {
                distributedPositions.push(selectedPosition);
            }

            // 轮转到下一个象限
            if ((i + 1) % Math.ceil(positionsPerQuadrant / 4) === 0) {
                currentQuadrantIndex = (currentQuadrantIndex + 1) % 4;
            }
        }

        Logger.info('WordPlacementEngine', `🎯 空间分散策略完成，生成${distributedPositions.length}个分布位置`);
        return distributedPositions;
    }

    /**
     * 检查位置是否符合分布要求
     * @param position 候选位置
     * @param existingPositions 已选位置
     * @param minDistance 最小距离
     * @returns 是否有效
     */
    private static _isPositionValidForDistribution(
        position: number,
        existingPositions: number[],
        minDistance: number
    ): boolean {
        const row = Math.floor(position / this.GRID_COLS);
        const col = position % this.GRID_COLS;

        for (const existingPos of existingPositions) {
            const existingRow = Math.floor(existingPos / this.GRID_COLS);
            const existingCol = existingPos % this.GRID_COLS;

            const distance = Math.abs(row - existingRow) + Math.abs(col - existingCol);
            if (distance < minDistance) {
                return false;
            }
        }

        return true;
    }

    /**
     * 增强随机性
     * @param positions 位置数组
     * @returns 随机性增强的位置数组
     */
    private static _enhanceRandomness(positions: number[]): number[] {
        // 🔧 策略1：分段随机化
        const segmentSize = 8; // 每8个位置为一段
        const enhancedPositions: number[] = [];

        for (let i = 0; i < positions.length; i += segmentSize) {
            const segment = positions.slice(i, i + segmentSize);
            GameUtils.shuffleArray(segment);
            enhancedPositions.push(...segment);
        }

        // 🔧 策略2：随机交换
        const swapCount = Math.floor(enhancedPositions.length / 4);
        for (let i = 0; i < swapCount; i++) {
            const index1 = Math.floor(Math.random() * enhancedPositions.length);
            const index2 = Math.floor(Math.random() * enhancedPositions.length);

            [enhancedPositions[index1], enhancedPositions[index2]] =
            [enhancedPositions[index2], enhancedPositions[index1]];
        }

        Logger.info('WordPlacementEngine', `🎲 随机性增强完成，执行${swapCount}次随机交换`);
        return enhancedPositions;
    }

    /**
     * 生成空间感知的位置序列
     * 基于已放置单词的位置，优先选择能够增加空间分散度的位置
     * @param grid 当前网格状态
     * @param placedWords 已放置的单词信息
     * @returns 空间优化的位置数组
     */
    private static _generateSpatiallyAwarePositions(
        grid: string[][],
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): number[] {
        // 🔧 步骤1：分析当前空间分布
        const spatialAnalysis = this._analyzeSpatialDistribution(placedWords);

        // 🔧 步骤2：识别空白区域和拥挤区域
        const regionAnalysis = this._analyzeRegionDensity(grid, placedWords);

        // 🔧 步骤3：生成优先级位置序列
        const prioritizedPositions = this._generatePrioritizedPositions(spatialAnalysis, regionAnalysis);

        Logger.info('WordPlacementEngine', `🎯 空间感知位置生成：${prioritizedPositions.length}个位置，优先空白区域`);
        return prioritizedPositions;
    }

    /**
     * 分析当前空间分布情况
     * @param placedWords 已放置的单词
     * @returns 空间分析结果
     */
    private static _analyzeSpatialDistribution(
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): {
        quadrantCounts: Map<number, number>;
        occupiedPositions: Set<number>;
        centerOfMass: {row: number, col: number};
        spreadRadius: number;
    } {
        const quadrantCounts = new Map<number, number>();
        const occupiedPositions = new Set<number>();
        let totalRow = 0, totalCol = 0;

        // 初始化象限计数
        for (let i = 1; i <= 4; i++) {
            quadrantCounts.set(i, 0);
        }

        for (const wordInfo of placedWords) {
            const row = wordInfo.row;
            const col = wordInfo.col;

            // 确定象限
            const quadrant = SpatialDistributionManager.getQuadrantForPosition(row, col);
            quadrantCounts.set(quadrant, (quadrantCounts.get(quadrant) || 0) + 1);

            // 记录占用位置
            const position = row * this.GRID_COLS + col;
            occupiedPositions.add(position);

            // 计算质心
            totalRow += row;
            totalCol += col;
        }

        const centerOfMass = placedWords.length > 0 ? {
            row: totalRow / placedWords.length,
            col: totalCol / placedWords.length
        } : { row: 4, col: 3 };

        // 计算分布半径
        let maxDistance = 0;
        for (const wordInfo of placedWords) {
            const distance = Math.sqrt(
                Math.pow(wordInfo.row - centerOfMass.row, 2) +
                Math.pow(wordInfo.col - centerOfMass.col, 2)
            );
            maxDistance = Math.max(maxDistance, distance);
        }

        return {
            quadrantCounts,
            occupiedPositions,
            centerOfMass,
            spreadRadius: maxDistance
        };
    }



    /**
     * 分析区域密度
     * @param grid 网格
     * @param placedWords 已放置单词
     * @returns 区域分析结果
     */
    private static _analyzeRegionDensity(
        grid: string[][],
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): {
        densityMap: number[][];
        emptyRegions: number[];
        crowdedRegions: number[];
        preferredRegions: number[];
    } {
        // 创建密度地图
        const densityMap: number[][] = [];
        for (let row = 0; row < this.GRID_ROWS; row++) {
            densityMap[row] = new Array(this.GRID_COLS).fill(0);
        }

        // 计算每个位置的密度（周围3x3区域的单词数量）
        for (const wordInfo of placedWords) {
            for (let dr = -1; dr <= 1; dr++) {
                for (let dc = -1; dc <= 1; dc++) {
                    const newRow = wordInfo.row + dr;
                    const newCol = wordInfo.col + dc;

                    if (newRow >= 0 && newRow < this.GRID_ROWS &&
                        newCol >= 0 && newCol < this.GRID_COLS) {
                        densityMap[newRow][newCol]++;
                    }
                }
            }
        }

        // 分类区域
        const emptyRegions: number[] = [];
        const crowdedRegions: number[] = [];
        const preferredRegions: number[] = [];

        for (let row = 0; row < this.GRID_ROWS; row++) {
            for (let col = 0; col < this.GRID_COLS; col++) {
                const position = row * this.GRID_COLS + col;
                const density = densityMap[row][col];

                if (density === 0) {
                    emptyRegions.push(position);
                } else if (density >= 3) {
                    crowdedRegions.push(position);
                } else {
                    preferredRegions.push(position);
                }
            }
        }

        return {
            densityMap,
            emptyRegions,
            crowdedRegions,
            preferredRegions
        };
    }

    /**
     * 生成优先级位置序列
     * @param spatialAnalysis 空间分析结果
     * @param regionAnalysis 区域分析结果
     * @returns 优先级排序的位置数组
     */
    private static _generatePrioritizedPositions(
        spatialAnalysis: any,
        regionAnalysis: any
    ): number[] {
        const prioritizedPositions: number[] = [];

        // 🔧 优先级1：空白区域中的象限平衡位置
        const balancedEmptyPositions = this._getQuadrantBalancedPositions(
            regionAnalysis.emptyRegions,
            spatialAnalysis.quadrantCounts
        );

        // 🔧 优先级2：远离质心的空白位置
        const distantEmptyPositions = this._getDistantPositions(
            regionAnalysis.emptyRegions,
            spatialAnalysis.centerOfMass
        );

        // 🔧 优先级3：适度密度区域的边缘位置
        const edgePreferredPositions = this._getEdgePositions(regionAnalysis.preferredRegions);

        // 🔧 优先级4：其他空白位置
        const remainingEmptyPositions = regionAnalysis.emptyRegions.filter(pos =>
            !balancedEmptyPositions.includes(pos) &&
            !distantEmptyPositions.includes(pos)
        );

        // 🔧 优先级5：适度密度区域
        const remainingPreferredPositions = regionAnalysis.preferredRegions.filter(pos =>
            !edgePreferredPositions.includes(pos)
        );

        // 组合并随机化各优先级
        GameUtils.shuffleArray(balancedEmptyPositions);
        GameUtils.shuffleArray(distantEmptyPositions);
        GameUtils.shuffleArray(edgePreferredPositions);
        GameUtils.shuffleArray(remainingEmptyPositions);
        GameUtils.shuffleArray(remainingPreferredPositions);

        // 按优先级顺序添加
        prioritizedPositions.push(...balancedEmptyPositions);
        prioritizedPositions.push(...distantEmptyPositions);
        prioritizedPositions.push(...edgePreferredPositions);
        prioritizedPositions.push(...remainingEmptyPositions);
        prioritizedPositions.push(...remainingPreferredPositions);

        Logger.info('WordPlacementEngine',
            `📊 优先级分布：平衡空白${balancedEmptyPositions.length}，远距空白${distantEmptyPositions.length}，边缘适度${edgePreferredPositions.length}`);

        return prioritizedPositions;
    }

    /**
     * 获取象限平衡的位置
     * @param emptyPositions 空白位置
     * @param quadrantCounts 象限计数
     * @returns 平衡位置数组
     */
    private static _getQuadrantBalancedPositions(
        emptyPositions: number[],
        quadrantCounts: Map<number, number>
    ): number[] {
        // 找出单词数量最少的象限
        let minCount = Infinity;
        let targetQuadrants: number[] = [];

        for (const [quadrant, count] of quadrantCounts) {
            if (count < minCount) {
                minCount = count;
                targetQuadrants = [quadrant];
            } else if (count === minCount) {
                targetQuadrants.push(quadrant);
            }
        }

        // 从目标象限中选择空白位置
        const balancedPositions: number[] = [];
        for (const position of emptyPositions) {
            const row = Math.floor(position / this.GRID_COLS);
            const col = position % this.GRID_COLS;
            const quadrant = SpatialDistributionManager.getQuadrantForPosition(row, col);

            if (targetQuadrants.includes(quadrant)) {
                balancedPositions.push(position);
            }
        }

        return balancedPositions;
    }

    /**
     * 获取远离质心的位置
     * @param positions 候选位置
     * @param centerOfMass 质心
     * @returns 远距离位置数组
     */
    private static _getDistantPositions(
        positions: number[],
        centerOfMass: {row: number, col: number}
    ): number[] {
        const positionsWithDistance = positions.map(pos => {
            const row = Math.floor(pos / this.GRID_COLS);
            const col = pos % this.GRID_COLS;
            const distance = Math.sqrt(
                Math.pow(row - centerOfMass.row, 2) +
                Math.pow(col - centerOfMass.col, 2)
            );
            return { position: pos, distance };
        });

        // 按距离排序，选择距离较远的位置
        positionsWithDistance.sort((a, b) => b.distance - a.distance);

        // 选择前50%的远距离位置
        const halfCount = Math.ceil(positionsWithDistance.length / 2);
        return positionsWithDistance.slice(0, halfCount).map(item => item.position);
    }

    /**
     * 获取边缘位置
     * @param positions 候选位置
     * @returns 边缘位置数组
     */
    private static _getEdgePositions(positions: number[]): number[] {
        return positions.filter(pos => {
            const row = Math.floor(pos / this.GRID_COLS);
            const col = pos % this.GRID_COLS;

            // 边缘定义：靠近网格边界
            return row === 0 || row === this.GRID_ROWS - 1 ||
                   col === 0 || col === this.GRID_COLS - 1;
        });
    }







    /**
     * 生成优化的位置序列（基于当前网格状态）
     * @param grid 当前网格状态
     * @param wordIndex 当前单词索引
     * @returns 优化的位置数组
     */
    private static _generateOptimizedPositions(grid: string[][], wordIndex: number): number[] {
        const positions: number[] = [];
        const occupiedPositions: number[] = [];
        const emptyPositions: number[] = [];

        // 分类位置：已占用 vs 空闲
        for (let row = 0; row < this.GRID_ROWS; row++) {
            for (let col = 0; col < this.GRID_COLS; col++) {
                const pos = row * this.GRID_COLS + col;
                if (grid[row][col] !== '') {
                    occupiedPositions.push(pos);
                } else {
                    emptyPositions.push(pos);
                }
            }
        }

        // 优化策略：根据单词索引调整位置优先级
        if (wordIndex < 3) {
            // 前3个单词：优先尝试空闲位置，然后是已占用位置（支持字母共享）
            positions.push(...GameUtils.shuffleArray(emptyPositions));
            positions.push(...GameUtils.shuffleArray(occupiedPositions));
        } else {
            // 后续单词：优先尝试已占用位置（增加字母共享机会），然后是空闲位置
            positions.push(...GameUtils.shuffleArray(occupiedPositions));
            positions.push(...GameUtils.shuffleArray(emptyPositions));
        }

        return positions;
    }

    /**
     * 打乱数组（Fisher-Yates算法）
     * @param array 要打乱的数组
     */
    private static _shuffleArray<T>(array: T[]): void {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    /**
     * 增强随机种子，确保每次生成都有不同的随机性
     * 通过多次随机调用来"预热"随机数生成器
     */
    private static _enhanceRandomSeed(): void {
        // 基于当前时间戳生成随机种子增强
        const timestamp = Date.now();
        const randomSeed = timestamp % 10000;

        // 执行多次随机调用来"预热"随机数生成器
        for (let i = 0; i < randomSeed % 100 + 10; i++) {
            Math.random();
        }

        Logger.debug('WordPlacementEngine', `🎲 随机种子已增强，预热${randomSeed % 100 + 10}次`);
    }



    /**
     * 计算位置方差
     */
    private static _calculatePositionVariance(results: Array<{success: boolean, wordPositions: any[]}>): number {
        const successResults = results.filter(r => r.success);
        if (successResults.length < 2) return 0;

        // 简化的方差计算
        const positions = successResults.flatMap(r => r.wordPositions.map(wp => wp.row * 8 + wp.col));
        const mean = positions.reduce((sum, pos) => sum + pos, 0) / positions.length;
        const variance = positions.reduce((sum, pos) => sum + Math.pow(pos - mean, 2), 0) / positions.length;

        return variance;
    }

    /**
     * 尝试在指定位置和方向放置单词
     * @param grid 网格
     * @param word 单词
     * @param row 起始行
     * @param col 起始列
     * @param direction 方向
     * @returns 放置结果
     */
    private static _tryPlaceWordAt(grid: string[][], word: string, row: number, col: number, direction: any): { success: boolean, newGrid?: string[][] } {
        // 检查是否可以放置
        if (!this._canPlaceWordAt(grid, word, row, col, direction)) {
            return { success: false };
        }

        // 创建新网格并放置单词
        const newGrid = this._copyGrid(grid);
        this._placeWordAt(newGrid, word, row, col, direction);

        return { success: true, newGrid };
    }

    /**
     * 检查是否可以在指定位置放置单词（统一版本）
     * @param grid 网格
     * @param word 单词
     * @param row 起始行
     * @param col 起始列
     * @param direction 方向
     * @param mode 约束模式：'strict'(严格), 'loose'(宽松), 'very_loose'(非常宽松), 'force'(强制)
     * @returns 是否可以放置
     */
    private static _canPlaceWordAt(
        grid: string[][],
        word: string,
        row: number,
        col: number,
        direction: any,
        mode: 'strict' | 'loose' | 'very_loose' | 'force' = 'strict'
    ): boolean {
        // 预检查：确保整个单词都在网格边界内
        const endRow = row + (word.length - 1) * direction.dr;
        const endCol = col + (word.length - 1) * direction.dc;

        if (endRow < 0 || endRow >= this.GRID_ROWS || endCol < 0 || endCol >= this.GRID_COLS) {
            return false;
        }

        // 强制模式：只检查边界，覆盖任何现有字母
        if (mode === 'force') {
            return true;
        }

        // 字母冲突检测和共享统计
        let sharedLetterCount = 0;
        let consecutiveSharedCount = 0;
        let maxConsecutiveShared = 0;

        for (let i = 0; i < word.length; i++) {
            const newRow = row + i * direction.dr;
            const newCol = col + i * direction.dc;

            // 双重边界检查（安全措施）
            if (newRow < 0 || newRow >= this.GRID_ROWS || newCol < 0 || newCol >= this.GRID_COLS) {
                return false;
            }

            const currentLetter = grid[newRow][newCol];
            const targetLetter = word[i];

            if (currentLetter !== '') {
                if (currentLetter === targetLetter) {
                    // 字母匹配，可以共享
                    sharedLetterCount++;
                    consecutiveSharedCount++;
                    maxConsecutiveShared = Math.max(maxConsecutiveShared, consecutiveSharedCount);
                } else {
                    // 字母冲突，无法放置
                    return false;
                }
            } else {
                consecutiveSharedCount = 0;
            }
        }

        // 根据模式应用不同的约束
        switch (mode) {
            case 'strict':
                // 严格模式：原有的严格约束
                const sharedRatio = sharedLetterCount / word.length;

                // 避免过度字母共享（超过40%）
                if (sharedRatio > 0.4) {
                    return false;
                }

                // 避免连续共享字母过多（超过2个连续字母）
                if (maxConsecutiveShared > 2) {
                    return false;
                }

                // 确保至少有一个新字母被放置
                if (sharedLetterCount === word.length) {
                    return false;
                }

                // 检查周围环境
                return this._validateWordPlacementEnvironment(grid, word, row, col, direction);

            case 'loose':
                // 宽松模式：允许更多共享，但至少要有一个新字母
                return sharedLetterCount < word.length;

            case 'very_loose':
                // 非常宽松模式：只要没有字母冲突就可以
                return true;

            default:
                return false;
        }
    }

    /**
     * 验证单词放置的周围环境
     * @param grid 网格
     * @param word 单词
     * @param row 起始行
     * @param col 起始列
     * @param direction 方向
     * @returns 是否通过环境验证
     */
    private static _validateWordPlacementEnvironment(grid: string[][], word: string, row: number, col: number, direction: any): boolean {
        // 检查单词两端是否会创建意外的字母连接
        const beforeRow = row - direction.dr;
        const beforeCol = col - direction.dc;
        const afterRow = row + word.length * direction.dr;
        const afterCol = col + word.length * direction.dc;

        // 检查单词前方是否有字母（可能形成更长的单词）
        if (beforeRow >= 0 && beforeRow < this.GRID_ROWS &&
            beforeCol >= 0 && beforeCol < this.GRID_COLS &&
            grid[beforeRow][beforeCol] !== '') {
            // 如果前方有字母，需要确保不会形成无意义的组合
            return false;
        }

        // 检查单词后方是否有字母
        if (afterRow >= 0 && afterRow < this.GRID_ROWS &&
            afterCol >= 0 && afterCol < this.GRID_COLS &&
            grid[afterRow][afterCol] !== '') {
            return false;
        }

        return true;
    }

    /**
     * 在指定位置放置单词
     * @param grid 网格
     * @param word 单词
     * @param row 起始行
     * @param col 起始列
     * @param direction 方向
     */
    private static _placeWordAt(grid: string[][], word: string, row: number, col: number, direction: any): void {
        for (let i = 0; i < word.length; i++) {
            const newRow = row + i * direction.dr;
            const newCol = col + i * direction.dc;
            grid[newRow][newCol] = word[i];
        }
    }

    /**
     * 复制网格
     * @param grid 源网格
     * @returns 新网格
     */
    private static _copyGrid(grid: string[][]): string[][] {
        return grid.map(row => [...row]);
    }

    /**
     * 将源网格复制到目标网格
     * @param source 源网格
     * @param target 目标网格
     */
    private static _copyGridTo(source: string[][], target: string[][]): void {
        for (let row = 0; row < this.GRID_ROWS; row++) {
            for (let col = 0; col < this.GRID_COLS; col++) {
                target[row][col] = source[row][col];
            }
        }
    }

    /**
     * 填充空位置
     * @param grid 网格
     */
    private static _fillEmptyPositions(grid: string[][]): void {
        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        
        for (let row = 0; row < this.GRID_ROWS; row++) {
            for (let col = 0; col < this.GRID_COLS; col++) {
                if (grid[row][col] === '') {
                    grid[row][col] = letters[Math.floor(Math.random() * letters.length)];
                }
            }
        }
    }

    /**
     * 将网格转换为一维数组
     * @param grid 网格
     * @returns 字母数组
     */
    private static _gridToArray(grid: string[][]): string[] {
        const letters: string[] = [];
        for (let row = 0; row < this.GRID_ROWS; row++) {
            for (let col = 0; col < this.GRID_COLS; col++) {
                letters.push(grid[row][col]);
            }
        }
        return letters;
    }

    /**
     * 生成简化网格（备用方案）- 确保100%单词放置成功，优化空间分布
     * @param words 单词数组
     * @returns 字母数组
     */
    private static _generateSimplifiedGrid(words: IWordData[]): { success: boolean, letters: string[] } {
        Logger.warn('WordPlacementEngine', '使用高级简化网格生成策略 - 确保100%完整性和良好分布');

        // 🔧 创建空网格
        const grid = this._createEmptyGrid();

        // 🔧 使用多方向策略，确保空间分布
        const distributedDirections = [
            { name: 'right', dr: 0, dc: 1, priority: 1 },      // 水平向右
            { name: 'down', dr: 1, dc: 0, priority: 1 },       // 垂直向下
            { name: 'rightdown', dr: 1, dc: 1, priority: 2 },  // 对角线右下
            { name: 'rightup', dr: -1, dc: 1, priority: 2 },   // 对角线右上
            { name: 'left', dr: 0, dc: -1, priority: 3 },      // 水平向左
            { name: 'up', dr: -1, dc: 0, priority: 3 },        // 垂直向上
            { name: 'leftdown', dr: 1, dc: -1, priority: 4 },  // 对角线左下
            { name: 'leftup', dr: -1, dc: -1, priority: 4 }    // 对角线左上
        ];

        // 按长度排序，长单词优先
        const sortedWords = [...words].sort((a, b) => b.word.length - a.word.length);
        let placedCount = 0;
        const placedWords: Array<{word: string, direction: string, row: number, col: number}> = [];

        // 🔧 第一阶段：使用空间分布算法放置单词
        const distributedPositions = SpatialDistributionManager.generateDistributedPositions();

        for (const wordData of sortedWords) {
            const word = wordData.word.toUpperCase();
            let placed = false;

            // 🔧 优先使用分布式位置
            for (const position of distributedPositions) {
                if (placed) break;

                const row = Math.floor(position / this.GRID_COLS);
                const col = position % this.GRID_COLS;

                // 🔧 按优先级尝试方向，确保方向多样性
                const prioritizedDirections = this._getPrioritizedDirectionsForPosition(
                    distributedDirections, placedWords, row, col
                );

                for (const direction of prioritizedDirections) {
                    const result = this._tryPlaceWordAt(grid, word, row, col, direction);
                    if (result.success) {
                        this._copyGridTo(result.newGrid, grid);
                        placedWords.push({
                            word: word,
                            direction: direction.name,
                            row: row,
                            col: col
                        });
                        placed = true;
                        placedCount++;
                        Logger.info('WordPlacementEngine', `分布式放置成功: ${word} 在 (${row},${col}) ${direction.name}方向`);
                        break;
                    }
                }
            }

            // 🔧 第二阶段：如果分布式放置失败，使用空间感知位置
            if (!placed) {
                const spatialPositions = SpatialDistributionManager.generateSpatiallyAwarePositions(grid, placedWords);

                for (const position of spatialPositions) {
                    if (placed) break;

                    const row = Math.floor(position / this.GRID_COLS);
                    const col = position % this.GRID_COLS;

                    for (const direction of distributedDirections) {
                        const result = this._tryPlaceWordAt(grid, word, row, col, direction);
                        if (result.success) {
                            this._copyGridTo(result.newGrid, grid);
                            placedWords.push({
                                word: word,
                                direction: direction.name,
                                row: row,
                                col: col
                            });
                            placed = true;
                            placedCount++;
                            Logger.info('WordPlacementEngine', `空间感知放置成功: ${word} 在 (${row},${col}) ${direction.name}方向`);
                            break;
                        }
                    }
                }
            }

            if (!placed) {
                Logger.debug('WordPlacementEngine', `高级简化策略无法放置单词: ${word}，尝试其他策略`);
            }
        }

        // 🔧 第三阶段：智能强制放置未放置的单词
        if (placedCount < words.length) {
            Logger.warn('WordPlacementEngine', `${words.length - placedCount} 个单词需要智能强制放置`);
            placedCount += this._intelligentForcePlacement(grid, sortedWords, placedWords);
        }

        // 填充剩余空位
        this._fillEmptyPositions(grid);

        // 🔧 最终验证
        const finalReport = this._verifyWordPlacement(grid, words);
        if (!finalReport.isComplete) {
            Logger.warn('WordPlacementEngine', `高级简化策略最终验证失败！缺失: ${finalReport.missingWords.join(', ')}`);
        } else {
            Logger.success('WordPlacementEngine', `高级简化策略成功：所有 ${words.length} 个单词已放置，空间分布良好`);
        }

        // 转换为字母数组
        const letters = this._gridToArray(grid);
        return { success: true, letters: letters };
    }

    /**
     * 将字母数组转换为网格
     * @param letters 字母数组
     * @returns 网格
     */
    private static _lettersToGrid(letters: string[]): string[][] {
        const grid: string[][] = [];
        for (let row = 0; row < this.GRID_ROWS; row++) {
            grid[row] = [];
            for (let col = 0; col < this.GRID_COLS; col++) {
                const index = row * this.GRID_COLS + col;
                grid[row][col] = letters[index] || '';
            }
        }
        return grid;
    }







    /**
     * 获取算法性能统计信息
     * @returns 性能统计对象
     */
    public static getPerformanceStats(): {
        totalAttempts: number;
        successfulGenerations: number;
        averageAttempts: number;
        successRate: number;
    } {
        // 这里可以添加实际的统计逻辑
        return {
            totalAttempts: 0,
            successfulGenerations: 0,
            averageAttempts: 0,
            successRate: 0
        };
    }

    /**
     * 验证生成的网格质量
     * @param grid 字母网格
     * @param words 目标单词
     * @returns 质量评分 (0-100)
     */
    public static evaluateGridQuality(grid: string[][], words: IWordData[]): number {
        let score = 100;

        // 检查单词分布均匀性
        const directionUsage = new Map<string, number>();
        // 这里可以添加实际的质量评估逻辑

        return Math.max(0, Math.min(100, score));
    }

    /**
     * 验证单词放置的完整性
     * @param grid 字母网格
     * @param targetWords 目标单词列表
     * @returns 验证报告
     */
    private static _verifyWordPlacement(grid: string[][], targetWords: IWordData[]): WordPlacementReport {
        const report: WordPlacementReport = {
            isComplete: false,
            totalWords: targetWords.length,
            placedWords: [],
            missingWords: [],
            gridFillRate: 0,
            qualityScore: 0,
            directionDistribution: new Map(),
            errors: [],
            suggestions: []
        };

        // 🔧 步骤1：搜索所有已放置的单词
        const placedWordsMap = new Map<string, PlacedWordInfo>();

        for (const wordData of targetWords) {
            const word = wordData.word.toUpperCase();
            const foundPlacements = this._findWordInGrid(grid, word);

            if (foundPlacements.length > 0) {
                // 选择最佳放置（通常是第一个找到的）
                const bestPlacement = foundPlacements[0];
                placedWordsMap.set(word, bestPlacement);
                report.placedWords.push(bestPlacement);

                // 统计方向分布
                const dirCount = report.directionDistribution.get(bestPlacement.direction) || 0;
                report.directionDistribution.set(bestPlacement.direction, dirCount + 1);
            } else {
                report.missingWords.push(word);
                report.errors.push(`单词 "${word}" 未在网格中找到`);
            }
        }

        // 🔧 步骤2：计算完整性和质量指标
        report.isComplete = report.missingWords.length === 0;
        report.gridFillRate = this._calculateGridFillRate(grid);
        report.qualityScore = this._calculateQualityScore(grid, report);

        // 🔧 步骤2.5：评估空间分布质量
        if (report.placedWords.length > 0) {
            const spatialQuality = SpatialQualityEvaluator.evaluateSpatialDistributionQuality(
                report.placedWords.map(w => ({
                    word: w.word,
                    direction: w.direction,
                    row: w.startRow,
                    col: w.startCol
                }))
            );

            // 将空间分布评分纳入总质量评分
            report.qualityScore = (report.qualityScore * 0.7) + (spatialQuality.overallScore * 0.3);

            // 添加空间分布建议
            report.suggestions.push(`空间分布评分: ${spatialQuality.overallScore.toFixed(1)}/100`);
            spatialQuality.details.forEach(detail => report.suggestions.push(`  - ${detail}`));
        }

        // 🔧 步骤3：生成建议
        if (!report.isComplete) {
            report.suggestions.push(`需要放置 ${report.missingWords.length} 个缺失单词`);
            report.suggestions.push(`缺失单词: ${report.missingWords.join(', ')}`);
        }

        if (report.directionDistribution.size < 8) {
            report.suggestions.push(`方向分布不均：只使用了 ${report.directionDistribution.size}/8 个方向`);
        }

        return report;
    }



    /**
     * 补救缺失的单词
     * @param grid 当前网格
     * @param missingWords 缺失的单词列表
     * @param allWords 所有目标单词
     * @returns 补救结果
     */
    private static _remedyMissingWords(
        grid: string[][],
        missingWords: string[],
        allWords: IWordData[]
    ): ForcePlacementResult {
        const result: ForcePlacementResult = {
            success: false,
            placedWords: [],
            failedWords: [...missingWords],
            modifiedGrid: this._copyGrid(grid),
            conflicts: []
        };

        Logger.info('WordPlacementEngine', `开始补救 ${missingWords.length} 个缺失单词`);

        // 🔧 补救策略1：在现有网格中寻找空间
        for (const word of missingWords) {
            const placed = this._tryPlaceMissingWord(result.modifiedGrid, word);
            if (placed) {
                result.placedWords.push(word);
                result.failedWords = result.failedWords.filter(w => w !== word);
                Logger.info('WordPlacementEngine', `成功补救单词: ${word}`);
            } else {
                Logger.warn('WordPlacementEngine', `无法补救单词: ${word}`);
            }
        }

        result.success = result.failedWords.length === 0;

        if (result.success) {
            Logger.info('WordPlacementEngine', `补救完成：所有 ${missingWords.length} 个单词已成功放置`);
        } else {
            Logger.warn('WordPlacementEngine', `补救部分成功：${result.placedWords.length}/${missingWords.length} 个单词已放置`);
        }

        return result;
    }

    /**
     * 尝试放置单个缺失的单词
     * @param grid 网格
     * @param word 单词
     * @returns 是否成功放置
     */
    private static _tryPlaceMissingWord(grid: string[][], word: string): boolean {
        const directions = [
            { name: 'right', dr: 0, dc: 1 },
            { name: 'down', dr: 1, dc: 0 },
            { name: 'rightdown', dr: 1, dc: 1 },
            { name: 'rightup', dr: -1, dc: 1 },
            { name: 'left', dr: 0, dc: -1 },
            { name: 'up', dr: -1, dc: 0 },
            { name: 'leftup', dr: -1, dc: -1 },
            { name: 'leftdown', dr: 1, dc: -1 }
        ];

        // 🔧 策略1：尝试在空白区域放置
        for (const direction of directions) {
            for (let row = 0; row < grid.length; row++) {
                for (let col = 0; col < grid[row].length; col++) {
                    if (this._canPlaceWordAt(grid, word, row, col, direction, 'loose')) {
                        this._placeWordAt(grid, word, row, col, direction);
                        return true;
                    }
                }
            }
        }

        // 🔧 策略2：允许更多字母共享的宽松放置
        for (const direction of directions) {
            for (let row = 0; row < grid.length; row++) {
                for (let col = 0; col < grid[row].length; col++) {
                    if (this._canPlaceWordAt(grid, word, row, col, direction, 'very_loose')) {
                        this._placeWordAt(grid, word, row, col, direction);
                        return true;
                    }
                }
            }
        }

        return false;
    }





    /**
     * 强制完整性保证 - 确保所有单词都被放置
     * @param grid 当前网格
     * @param allWords 所有目标单词
     * @returns 强制放置结果
     */
    private static _forceCompleteGeneration(grid: string[][], allWords: IWordData[]): ForcePlacementResult {
        Logger.warn('WordPlacementEngine', '启动高级强制完整性保证机制');

        // 🔧 使用分布式策略重新开始
        const cleanGrid = this._createEmptyGrid();
        const result: ForcePlacementResult = {
            success: false,
            placedWords: [],
            failedWords: [],
            modifiedGrid: cleanGrid,
            conflicts: []
        };

        // 按单词长度排序，长单词优先
        const sortedWords = [...allWords].sort((a, b) => b.word.length - a.word.length);

        // 🔧 使用多方向分布策略
        const distributedDirections = [
            { name: 'right', dr: 0, dc: 1, priority: 1 },
            { name: 'down', dr: 1, dc: 0, priority: 1 },
            { name: 'rightdown', dr: 1, dc: 1, priority: 2 },
            { name: 'rightup', dr: -1, dc: 1, priority: 2 },
            { name: 'left', dr: 0, dc: -1, priority: 3 },
            { name: 'up', dr: -1, dc: 0, priority: 3 }
        ];

        // 🔧 生成分布式位置序列
        const distributedPositions = SpatialDistributionManager.generateDistributedPositions();
        const placedWordInfo: Array<{word: string, direction: string, row: number, col: number}> = [];

        for (const wordData of sortedWords) {
            const word = wordData.word.toUpperCase();
            let placed = false;

            // 🔧 第一阶段：使用分布式位置
            for (const position of distributedPositions) {
                if (placed) break;

                const row = Math.floor(position / this.GRID_COLS);
                const col = position % this.GRID_COLS;

                // 按优先级尝试方向
                const prioritizedDirections = distributedDirections.sort((a, b) => a.priority - b.priority);

                for (const direction of prioritizedDirections) {
                    if (this._canPlaceWordAt(cleanGrid, word, row, col, direction, 'force')) {
                        this._placeWordAt(cleanGrid, word, row, col, direction);
                        result.placedWords.push(word);
                        placedWordInfo.push({
                            word: word,
                            direction: direction.name,
                            row: row,
                            col: col
                        });
                        placed = true;
                        Logger.info('WordPlacementEngine', `分布式强制放置: ${word} 在 (${row},${col}) ${direction.name}方向`);
                        break;
                    }
                }
            }

            // 🔧 第二阶段：如果分布式放置失败，使用避开策略
            if (!placed) {
                const avoidancePositions = this._generateAvoidancePositions(placedWordInfo);

                for (const position of avoidancePositions) {
                    if (placed) break;

                    const row = Math.floor(position / this.GRID_COLS);
                    const col = position % this.GRID_COLS;

                    for (const direction of distributedDirections) {
                        if (this._canPlaceWordAt(cleanGrid, word, row, col, direction, 'force')) {
                            this._placeWordAt(cleanGrid, word, row, col, direction);
                            result.placedWords.push(word);
                            placedWordInfo.push({
                                word: word,
                                direction: direction.name,
                                row: row,
                                col: col
                            });
                            placed = true;
                            Logger.info('WordPlacementEngine', `避开策略强制放置: ${word} 在 (${row},${col}) ${direction.name}方向`);
                            break;
                        }
                    }
                }
            }

            // 🔧 第三阶段：最后手段，任意位置放置
            if (!placed) {
                for (let row = 0; row < this.GRID_ROWS && !placed; row++) {
                    for (let col = 0; col < this.GRID_COLS && !placed; col++) {
                        // 只使用水平方向，确保可靠性
                        if (col + word.length <= this.GRID_COLS) {
                            this._placeWordAt(cleanGrid, word, row, col, { dr: 0, dc: 1 });
                            result.placedWords.push(word);
                            placedWordInfo.push({
                                word: word,
                                direction: 'right',
                                row: row,
                                col: col
                            });
                            placed = true;
                            Logger.warn('WordPlacementEngine', `最后手段强制放置: ${word} 在 (${row},${col}) 水平方向`);
                        }
                    }
                }
            }

            if (!placed) {
                result.failedWords.push(word);
                Logger.error('WordPlacementEngine', `所有策略均失败，无法放置: ${word}`);
            }
        }

        result.success = result.failedWords.length === 0;

        if (result.success) {
            Logger.info('WordPlacementEngine', `强制完整性保证成功：所有 ${allWords.length} 个单词已放置`);
        } else {
            Logger.error('WordPlacementEngine', `强制完整性保证失败：${result.failedWords.length} 个单词无法放置`);
        }

        return result;
    }



    /**
     * 计算网格填充率
     * @param grid 字母网格
     * @returns 填充率 (0-1)
     */
    private static _calculateGridFillRate(grid: string[][]): number {
        let filledCells = 0;
        const totalCells = this.GRID_ROWS * this.GRID_COLS;

        for (const row of grid) {
            for (const cell of row) {
                if (cell !== '') {
                    filledCells++;
                }
            }
        }

        return filledCells / totalCells;
    }

    /**
     * 计算网格质量评分
     * @param grid 字母网格
     * @param report 验证报告
     * @returns 质量评分 (0-100)
     */
    private static _calculateQualityScore(grid: string[][], report: WordPlacementReport): number {
        let score = 100;

        // 扣分项
        score -= report.missingWords.length * 20; // 每个缺失单词扣20分
        score -= Math.max(0, 8 - report.directionDistribution.size) * 5; // 方向不足扣分

        // 填充率影响
        if (report.gridFillRate < 0.7) {
            score -= (0.7 - report.gridFillRate) * 50;
        }

        return Math.max(0, Math.min(100, score));
    }



    /**
     * 复制网格
     * @param sourceGrid 源网格
     * @returns 新网格
     */
    private static _copyGrid(sourceGrid: string[][]): string[][] {
        const newGrid: string[][] = [];
        for (let row = 0; row < sourceGrid.length; row++) {
            newGrid[row] = [...sourceGrid[row]];
        }
        return newGrid;
    }

    // ==================== 单词放置验证方法（从WordPlacementValidator整合） ====================

    /**
     * 验证单词放置的完整性（公共接口）
     * @param grid 字母网格
     * @param targetWords 目标单词列表
     * @returns 验证报告
     */
    public static verifyWordPlacement(grid: string[][], targetWords: IWordData[]): WordPlacementReport {
        return this._verifyWordPlacement(grid, targetWords);
    }

    /**
     * 在网格中查找指定单词的所有放置位置（公共接口）
     * @param grid 字母网格
     * @param word 目标单词
     * @returns 找到的放置信息数组
     */
    public static findWordInGrid(grid: string[][], word: string): PlacedWordInfo[] {
        return this._findWordInGrid(grid, word);
    }

    /**
     * 输出验证报告（公共接口）
     * @param report 验证报告
     */
    public static outputVerificationReport(report: WordPlacementReport): void {
        Logger.info('WordPlacementEngine', '=== 生成验证报告 ===');
        Logger.info('WordPlacementEngine', `完整性: ${report.isComplete ? '✅ 完整' : '❌ 不完整'}`);
        Logger.info('WordPlacementEngine', `单词统计: ${report.placedWords.length}/${report.totalWords} 已放置`);
        Logger.info('WordPlacementEngine', `网格填充率: ${(report.gridFillRate * 100).toFixed(1)}%`);
        Logger.info('WordPlacementEngine', `质量评分: ${report.qualityScore.toFixed(1)}/100`);
        Logger.info('WordPlacementEngine', `方向分布: ${report.directionDistribution.size}/8 个方向`);

        if (report.placedWords.length > 0) {
            const wordList = report.placedWords.map(w => `${w.word}(${w.direction})`).join(', ');
            Logger.info('WordPlacementEngine', `已放置单词: ${wordList}`);
        }

        if (report.missingWords.length > 0) {
            Logger.warn('WordPlacementEngine', `缺失单词: ${report.missingWords.join(', ')}`);
        }

        if (report.errors.length > 0) {
            report.errors.forEach(error => Logger.error('WordPlacementEngine', error));
        }

        Logger.info('WordPlacementEngine', '=== 报告结束 ===');
    }

    /**
     * 在网格中查找指定单词的所有放置位置（私有实现）
     * @param grid 字母网格
     * @param word 目标单词
     * @returns 找到的放置信息数组
     */
    private static _findWordInGrid(grid: string[][], word: string): PlacedWordInfo[] {
        const placements: PlacedWordInfo[] = [];
        const directions = [
            { name: 'right', dr: 0, dc: 1 },
            { name: 'left', dr: 0, dc: -1 },
            { name: 'down', dr: 1, dc: 0 },
            { name: 'up', dr: -1, dc: 0 },
            { name: 'rightdown', dr: 1, dc: 1 },
            { name: 'leftup', dr: -1, dc: -1 },
            { name: 'rightup', dr: -1, dc: 1 },
            { name: 'leftdown', dr: 1, dc: -1 }
        ];

        // 搜索每个起始位置和方向
        for (let row = 0; row < grid.length; row++) {
            for (let col = 0; col < grid[row].length; col++) {
                for (const direction of directions) {
                    const placement = this._checkWordAtPosition(grid, word, row, col, direction);
                    if (placement) {
                        placements.push(placement);
                    }
                }
            }
        }

        return placements;
    }

    /**
     * 检查指定位置和方向是否存在目标单词
     * @param grid 字母网格
     * @param word 目标单词
     * @param startRow 起始行
     * @param startCol 起始列
     * @param direction 方向
     * @returns 放置信息或null
     */
    private static _checkWordAtPosition(
        grid: string[][],
        word: string,
        startRow: number,
        startCol: number,
        direction: {name: string, dr: number, dc: number}
    ): PlacedWordInfo | null {
        const endRow = startRow + (word.length - 1) * direction.dr;
        const endCol = startCol + (word.length - 1) * direction.dc;

        // 边界检查
        if (!grid || grid.length === 0 || !grid[0] ||
            endRow < 0 || endRow >= grid.length ||
            endCol < 0 || endCol >= grid[0].length) {
            return null;
        }

        // 检查每个字母是否匹配
        let sharedLetters = 0;
        for (let i = 0; i < word.length; i++) {
            const row = startRow + i * direction.dr;
            const col = startCol + i * direction.dc;

            if (grid[row][col] !== word[i]) {
                return null;
            }
        }

        return {
            word: word,
            startRow: startRow,
            startCol: startCol,
            endRow: endRow,
            endCol: endCol,
            direction: direction.name,
            length: word.length,
            sharedLetters: sharedLetters
        };
    }

    /**
     * 获取位置的优先级方向列表
     * @param allDirections 所有可用方向
     * @param placedWords 已放置的单词
     * @param row 目标行
     * @param col 目标列
     * @returns 按优先级排序的方向列表
     */
    private static _getPrioritizedDirectionsForPosition(
        allDirections: Array<{name: string, dr: number, dc: number, priority: number}>,
        placedWords: Array<{word: string, direction: string, row: number, col: number}>,
        row: number,
        col: number
    ): Array<{name: string, dr: number, dc: number, priority: number}> {

        // 🔧 分析已使用的方向
        const usedDirections = new Set(placedWords.map(w => w.direction));

        // 🔧 计算象限分布
        const quadrant = SpatialDistributionManager.getQuadrantForPosition(row, col);

        // 🔧 根据象限和已使用方向调整优先级
        const prioritizedDirections = [...allDirections].map(dir => {
            let adjustedPriority = dir.priority;

            // 如果方向已被大量使用，降低优先级
            const directionUsageCount = placedWords.filter(w => w.direction === dir.name).length;
            if (directionUsageCount >= 2) {
                adjustedPriority += 2; // 降低优先级
            }

            // 根据象限位置调整方向偏好
            if (quadrant === 1) { // 左上象限，偏好向右下的方向
                if (dir.name === 'right' || dir.name === 'down' || dir.name === 'rightdown') {
                    adjustedPriority -= 1;
                }
            } else if (quadrant === 2) { // 右上象限，偏好向左下的方向
                if (dir.name === 'left' || dir.name === 'down' || dir.name === 'leftdown') {
                    adjustedPriority -= 1;
                }
            } else if (quadrant === 3) { // 左下象限，偏好向右上的方向
                if (dir.name === 'right' || dir.name === 'up' || dir.name === 'rightup') {
                    adjustedPriority -= 1;
                }
            } else { // 右下象限，偏好向左上的方向
                if (dir.name === 'left' || dir.name === 'up' || dir.name === 'leftup') {
                    adjustedPriority -= 1;
                }
            }

            return { ...dir, adjustedPriority };
        });

        // 按调整后的优先级排序
        prioritizedDirections.sort((a, b) => a.adjustedPriority - b.adjustedPriority);

        return prioritizedDirections;
    }

    /**
     * 智能强制放置未放置的单词 - 优化版本，避免过度集中
     * @param grid 当前网格
     * @param sortedWords 排序后的单词列表
     * @param placedWords 已放置的单词列表
     * @returns 成功放置的单词数量
     */
    private static _intelligentForcePlacement(
        grid: string[][],
        sortedWords: IWordData[],
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): number {
        let forcePlacedCount = 0;

        // 🔧 优化：生成更分散的位置序列，避免集中
        const distributedPositions = this._generateDistributedEmptyPositions(grid, placedWords);

        // 🔧 多方向策略，优先使用对角线方向增加分散度
        const forceDirections = [
            { name: 'rightdown', dr: 1, dc: 1, priority: 1 },   // 对角线优先
            { name: 'rightup', dr: -1, dc: 1, priority: 1 },
            { name: 'leftdown', dr: 1, dc: -1, priority: 1 },
            { name: 'leftup', dr: -1, dc: -1, priority: 1 },
            { name: 'right', dr: 0, dc: 1, priority: 2 },       // 水平垂直次优先
            { name: 'down', dr: 1, dc: 0, priority: 2 },
            { name: 'left', dr: 0, dc: -1, priority: 3 },
            { name: 'up', dr: -1, dc: 0, priority: 3 }
        ];

        for (const wordData of sortedWords) {
            const word = wordData.word.toUpperCase();

            // 检查是否已放置
            const found = this._findWordInGrid(grid, word);
            if (found.length > 0) continue;

            let forcePlaced = false;

            // 🔧 策略1：优先使用分散的空白位置，避免覆盖
            for (const position of distributedPositions) {
                if (forcePlaced) break;

                const row = Math.floor(position / this.GRID_COLS);
                const col = position % this.GRID_COLS;

                // 按优先级尝试方向
                const sortedDirections = forceDirections.sort((a, b) => a.priority - b.priority);

                for (const direction of sortedDirections) {
                    // 🔧 严格检查：确保不会覆盖已有单词的关键字母
                    if (this._canPlaceWordWithoutConflict(grid, word, row, col, direction, placedWords)) {
                        this._placeWordAt(grid, word, row, col, direction);
                        placedWords.push({
                            word: word,
                            direction: direction.name,
                            row: row,
                            col: col
                        });
                        forcePlaced = true;
                        forcePlacedCount++;
                        Logger.info('WordPlacementEngine', `智能分散放置: ${word} 在 (${row},${col}) ${direction.name}方向`);
                        break;
                    }
                }
            }

            // 🔧 策略2：如果分散放置失败，使用边缘区域
            if (!forcePlaced) {
                const edgePositions = this._generateEdgePositions();

                for (const position of edgePositions) {
                    if (forcePlaced) break;

                    const row = Math.floor(position / this.GRID_COLS);
                    const col = position % this.GRID_COLS;

                    // 优先使用水平方向，在边缘区域放置
                    if (col + word.length <= this.GRID_COLS) {
                        if (this._canPlaceWordWithoutConflict(grid, word, row, col, { dr: 0, dc: 1 }, placedWords)) {
                            this._placeWordAt(grid, word, row, col, { dr: 0, dc: 1 });
                            placedWords.push({
                                word: word,
                                direction: 'right',
                                row: row,
                                col: col
                            });
                            forcePlaced = true;
                            forcePlacedCount++;
                            Logger.info('WordPlacementEngine', `边缘区域放置: ${word} 在 (${row},${col}) 水平方向`);
                            break;
                        }
                    }
                }
            }

            // 🔧 策略3：最后手段 - 在确保最小冲突的情况下强制放置
            if (!forcePlaced) {
                Logger.warn('WordPlacementEngine', `单词 ${word} 需要最小冲突强制放置`);
                const result = this._forceMinimalConflictPlacement(grid, word, placedWords);
                if (result.success) {
                    placedWords.push(result.placement);
                    forcePlaced = true;
                    forcePlacedCount++;
                }
            }
        }

        return forcePlacedCount;
    }

    /**
     * 生成分散的空白位置序列
     * @param grid 当前网格
     * @param placedWords 已放置的单词列表
     * @returns 分散的空白位置数组
     */
    private static _generateDistributedEmptyPositions(
        grid: string[][],
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): number[] {
        const emptyPositions: number[] = [];
        const occupiedZones = new Set<number>();

        // 🔧 标记已占用区域（包括缓冲区）
        for (const placed of placedWords) {
            for (let i = 0; i < placed.word.length; i++) {
                const directions = this._getDirectionVector(placed.direction);
                const row = placed.row + i * directions.dr;
                const col = placed.col + i * directions.dc;

                // 标记该位置及其周围1格范围为占用区域
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        const bufferRow = row + dr;
                        const bufferCol = col + dc;
                        if (bufferRow >= 0 && bufferRow < this.GRID_ROWS &&
                            bufferCol >= 0 && bufferCol < this.GRID_COLS) {
                            occupiedZones.add(bufferRow * this.GRID_COLS + bufferCol);
                        }
                    }
                }
            }
        }

        // 🔧 生成分散的空白位置，优先选择远离占用区域的位置
        for (let row = 0; row < this.GRID_ROWS; row++) {
            for (let col = 0; col < this.GRID_COLS; col++) {
                const position = row * this.GRID_COLS + col;

                // 跳过占用区域
                if (occupiedZones.has(position)) continue;

                // 检查是否为空白位置
                if (grid[row][col] === '') {
                    emptyPositions.push(position);
                }
            }
        }

        // 🔧 按距离已占用区域的远近排序，远的优先
        emptyPositions.sort((a, b) => {
            const distA = this._calculateMinDistanceToOccupied(a, occupiedZones);
            const distB = this._calculateMinDistanceToOccupied(b, occupiedZones);
            return distB - distA; // 距离远的优先
        });

        Logger.info('WordPlacementEngine', `生成${emptyPositions.length}个分散空白位置`);
        return emptyPositions;
    }

    /**
     * 生成边缘区域位置
     * @returns 边缘位置数组
     */
    private static _generateEdgePositions(): number[] {
        const edgePositions: number[] = [];

        // 🔧 优先使用四个角落和边缘区域
        const edgeRegions = [
            // 顶部边缘
            { startRow: 0, endRow: 1, startCol: 0, endCol: this.GRID_COLS - 1 },
            // 底部边缘
            { startRow: this.GRID_ROWS - 2, endRow: this.GRID_ROWS - 1, startCol: 0, endCol: this.GRID_COLS - 1 },
            // 左侧边缘
            { startRow: 2, endRow: this.GRID_ROWS - 3, startCol: 0, endCol: 1 },
            // 右侧边缘
            { startRow: 2, endRow: this.GRID_ROWS - 3, startCol: this.GRID_COLS - 2, endCol: this.GRID_COLS - 1 }
        ];

        for (const region of edgeRegions) {
            for (let row = region.startRow; row <= region.endRow; row++) {
                for (let col = region.startCol; col <= region.endCol; col++) {
                    edgePositions.push(row * this.GRID_COLS + col);
                }
            }
        }

        // 随机打乱边缘位置
        GameUtils.shuffleArray(edgePositions);
        return edgePositions;
    }

    /**
     * 生成避开已占用区域的位置序列
     * @param placedWords 已放置的单词列表
     * @returns 避开区域的位置数组
     */
    private static _generateAvoidancePositions(
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): number[] {
        const avoidancePositions: number[] = [];
        const occupiedPositions = new Set<number>();

        // 先添加检查单词放置冲突的辅助方法
    }

    /**
     * 检查单词是否可以在指定位置放置而不产生严重冲突
     * @param grid 当前网格
     * @param word 要放置的单词
     * @param row 起始行
     * @param col 起始列
     * @param direction 方向
     * @param placedWords 已放置的单词列表
     * @returns 是否可以放置
     */
    private static _canPlaceWordWithoutConflict(
        grid: string[][],
        word: string,
        row: number,
        col: number,
        direction: { dr: number, dc: number },
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): boolean {
        // 🔧 检查边界
        const endRow = row + (word.length - 1) * direction.dr;
        const endCol = col + (word.length - 1) * direction.dc;

        if (endRow < 0 || endRow >= this.GRID_ROWS ||
            endCol < 0 || endCol >= this.GRID_COLS) {
            return false;
        }

        // 🔧 检查每个字母位置
        let conflictCount = 0;
        const maxAllowedConflicts = Math.floor(word.length * 0.3); // 最多允许30%的字母冲突

        for (let i = 0; i < word.length; i++) {
            const currentRow = row + i * direction.dr;
            const currentCol = col + i * direction.dc;
            const currentLetter = word[i];
            const gridLetter = grid[currentRow][currentCol];

            // 如果网格位置不为空且字母不匹配，计为冲突
            if (gridLetter !== '' && gridLetter !== currentLetter) {
                conflictCount++;

                // 🔧 检查是否会破坏已放置单词的完整性
                if (this._wouldBreakExistingWord(currentRow, currentCol, currentLetter, placedWords, grid)) {
                    return false; // 严重冲突，不允许放置
                }
            }
        }

        // 允许少量冲突，但不能超过阈值
        return conflictCount <= maxAllowedConflicts;
    }

    /**
     * 检查放置字母是否会破坏已存在单词的完整性
     * @param row 行位置
     * @param col 列位置
     * @param newLetter 新字母
     * @param placedWords 已放置的单词列表
     * @param grid 当前网格
     * @returns 是否会破坏现有单词
     */
    private static _wouldBreakExistingWord(
        row: number,
        col: number,
        newLetter: string,
        placedWords: Array<{word: string, direction: string, row: number, col: number}>,
        grid: string[][]
    ): boolean {
        // 🔧 检查该位置是否是某个已放置单词的关键字母
        for (const placed of placedWords) {
            const directions = this._getDirectionVector(placed.direction);

            for (let i = 0; i < placed.word.length; i++) {
                const wordRow = placed.row + i * directions.dr;
                const wordCol = placed.col + i * directions.dc;

                if (wordRow === row && wordCol === col) {
                    // 这个位置是已放置单词的一部分
                    const expectedLetter = placed.word[i];
                    if (expectedLetter !== newLetter) {
                        // 新字母与期望字母不匹配，会破坏单词
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 最小冲突强制放置
     * @param grid 当前网格
     * @param word 要放置的单词
     * @param placedWords 已放置的单词列表
     * @returns 放置结果
     */
    private static _forceMinimalConflictPlacement(
        grid: string[][],
        word: string,
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): { success: boolean, placement?: {word: string, direction: string, row: number, col: number} } {

        const directions = [
            { name: 'right', dr: 0, dc: 1 },
            { name: 'down', dr: 1, dc: 0 }
        ];

        let bestPlacement: any = null;
        let minConflicts = Infinity;

        // 🔧 尝试所有可能位置，找到冲突最少的
        for (let row = 0; row < this.GRID_ROWS; row++) {
            for (let col = 0; col < this.GRID_COLS; col++) {
                for (const direction of directions) {
                    const endRow = row + (word.length - 1) * direction.dr;
                    const endCol = col + (word.length - 1) * direction.dc;

                    if (endRow >= 0 && endRow < this.GRID_ROWS &&
                        endCol >= 0 && endCol < this.GRID_COLS) {

                        const conflicts = this._countPlacementConflicts(grid, word, row, col, direction);

                        if (conflicts < minConflicts) {
                            minConflicts = conflicts;
                            bestPlacement = {
                                word: word,
                                direction: direction.name,
                                row: row,
                                col: col
                            };
                        }
                    }
                }
            }
        }

        if (bestPlacement && minConflicts < word.length) {
            const directions = this._getDirectionVector(bestPlacement.direction);
            this._placeWordAt(grid, word, bestPlacement.row, bestPlacement.col, directions);
            Logger.warn('WordPlacementEngine', `最小冲突放置: ${word} 在 (${bestPlacement.row},${bestPlacement.col}) ${bestPlacement.direction}方向，冲突数: ${minConflicts}`);
            return { success: true, placement: bestPlacement };
        }

        return { success: false };
    }

    /**
     * 计算放置单词的冲突数量
     * @param grid 当前网格
     * @param word 单词
     * @param row 起始行
     * @param col 起始列
     * @param direction 方向
     * @returns 冲突数量
     */
    private static _countPlacementConflicts(
        grid: string[][],
        word: string,
        row: number,
        col: number,
        direction: { dr: number, dc: number }
    ): number {
        let conflicts = 0;

        for (let i = 0; i < word.length; i++) {
            const currentRow = row + i * direction.dr;
            const currentCol = col + i * direction.dc;
            const gridLetter = grid[currentRow][currentCol];

            if (gridLetter !== '' && gridLetter !== word[i]) {
                conflicts++;
            }
        }

        return conflicts;
    }

    /**
     * 计算位置到占用区域的最小距离
     * @param position 位置
     * @param occupiedZones 占用区域集合
     * @returns 最小距离
     */
    private static _calculateMinDistanceToOccupied(position: number, occupiedZones: Set<number>): number {
        const row = Math.floor(position / this.GRID_COLS);
        const col = position % this.GRID_COLS;

        let minDistance = Infinity;

        for (const occupied of occupiedZones) {
            const occupiedRow = Math.floor(occupied / this.GRID_COLS);
            const occupiedCol = occupied % this.GRID_COLS;

            const distance = Math.abs(row - occupiedRow) + Math.abs(col - occupiedCol);
            minDistance = Math.min(minDistance, distance);
        }

        return minDistance === Infinity ? 0 : minDistance;
    }

    /**
     * 获取方向向量
     * @param directionName 方向名称
     * @returns 方向向量
     */
    private static _getDirectionVector(directionName: string): { dr: number, dc: number } {
        const directionMap: { [key: string]: { dr: number, dc: number } } = {
            'right': { dr: 0, dc: 1 },
            'left': { dr: 0, dc: -1 },
            'down': { dr: 1, dc: 0 },
            'up': { dr: -1, dc: 0 },
            'rightdown': { dr: 1, dc: 1 },
            'rightup': { dr: -1, dc: 1 },
            'leftdown': { dr: 1, dc: -1 },
            'leftup': { dr: -1, dc: -1 }
        };

        return directionMap[directionName] || { dr: 0, dc: 1 };
    }

    /**
     * 继续原有的避开位置生成方法
     */
    private static _continueGenerateAvoidancePositions(
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): number[] {
        const avoidancePositions: number[] = [];
        const occupiedPositions = new Set<number>();

        // 🔧 标记所有已占用的位置
        for (const placedWord of placedWords) {
            const directions = {
                'right': { dr: 0, dc: 1 },
                'down': { dr: 1, dc: 0 },
                'rightdown': { dr: 1, dc: 1 },
                'rightup': { dr: -1, dc: 1 },
                'left': { dr: 0, dc: -1 },
                'up': { dr: -1, dc: 0 },
                'leftdown': { dr: 1, dc: -1 },
                'leftup': { dr: -1, dc: -1 }
            };

            const direction = directions[placedWord.direction as keyof typeof directions];
            if (direction) {
                for (let i = 0; i < placedWord.word.length; i++) {
                    const row = placedWord.row + i * direction.dr;
                    const col = placedWord.col + i * direction.dc;
                    const position = row * this.GRID_COLS + col;
                    occupiedPositions.add(position);

                    // 🔧 添加缓冲区域（周围1格）
                    for (let dr = -1; dr <= 1; dr++) {
                        for (let dc = -1; dc <= 1; dc++) {
                            const bufferRow = row + dr;
                            const bufferCol = col + dc;
                            if (bufferRow >= 0 && bufferRow < this.GRID_ROWS &&
                                bufferCol >= 0 && bufferCol < this.GRID_COLS) {
                                const bufferPosition = bufferRow * this.GRID_COLS + bufferCol;
                                occupiedPositions.add(bufferPosition);
                            }
                        }
                    }
                }
            }
        }

        // 🔧 生成避开已占用区域的位置
        for (let row = 0; row < this.GRID_ROWS; row++) {
            for (let col = 0; col < this.GRID_COLS; col++) {
                const position = row * this.GRID_COLS + col;
                if (!occupiedPositions.has(position)) {
                    avoidancePositions.push(position);
                }
            }
        }

        // 🔧 随机打乱避开位置，增加分布随机性
        GameUtils.shuffleArray(avoidancePositions);

        return avoidancePositions;
    }

    /**
     * 生成随机位置序列
     * @returns 随机位置数组
     */
    private static _generateRandomPositions(): number[] {
        const positions: number[] = [];

        // 生成所有可能的位置
        for (let i = 0; i < this.TOTAL_LETTERS; i++) {
            positions.push(i);
        }

        // 随机打乱位置
        for (let i = positions.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [positions[i], positions[j]] = [positions[j], positions[i]];
        }

        return positions;
    }

    /**
     * 生成分散的位置序列
     * @param placedWords 已放置的单词列表
     * @returns 分散的位置数组
     */
    private static _generateScatteredPositions(
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): number[] {
        const scatteredPositions: number[] = [];

        // 🔧 计算已放置单词的重心
        let centerRow = 0;
        let centerCol = 0;
        if (placedWords.length > 0) {
            for (const word of placedWords) {
                centerRow += word.row;
                centerCol += word.col;
            }
            centerRow = Math.floor(centerRow / placedWords.length);
            centerCol = Math.floor(centerCol / placedWords.length);
        } else {
            centerRow = Math.floor(this.GRID_ROWS / 2);
            centerCol = Math.floor(this.GRID_COLS / 2);
        }

        // 🔧 生成远离重心的位置
        const allPositions: Array<{position: number, distance: number}> = [];

        for (let row = 0; row < this.GRID_ROWS; row++) {
            for (let col = 0; col < this.GRID_COLS; col++) {
                const position = row * this.GRID_COLS + col;
                const distance = Math.abs(row - centerRow) + Math.abs(col - centerCol);
                allPositions.push({ position, distance });
            }
        }

        // 🔧 按距离排序，远的优先
        allPositions.sort((a, b) => b.distance - a.distance);

        // 🔧 选择前50%的位置作为分散位置
        const selectedCount = Math.floor(allPositions.length * 0.5);
        for (let i = 0; i < selectedCount; i++) {
            scatteredPositions.push(allPositions[i].position);
        }

        return scatteredPositions;
    }

    // ==================== 🎯 智能8方向分布算法核心方法 ====================

    /**
     * 创建智能方向分配策略
     * 根据单词数量智能分配到8个方向，确保均匀分布
     * @param words 单词数据数组
     * @returns 方向分配策略
     */
    private static _createIntelligentDirectionStrategy(words: IWordData[]): DirectionAllocationStrategy {
        // 定义8个标准方向
        const baseDirections: DirectionInfo[] = [
            { name: 'right', dr: 0, dc: 1, priority: 1, allocatedCount: 0 },
            { name: 'down', dr: 1, dc: 0, priority: 1, allocatedCount: 0 },
            { name: 'rightdown', dr: 1, dc: 1, priority: 2, allocatedCount: 0 },
            { name: 'rightup', dr: -1, dc: 1, priority: 2, allocatedCount: 0 },
            { name: 'left', dr: 0, dc: -1, priority: 3, allocatedCount: 0 },
            { name: 'up', dr: -1, dc: 0, priority: 3, allocatedCount: 0 },
            { name: 'leftdown', dr: 1, dc: -1, priority: 4, allocatedCount: 0 },
            { name: 'leftup', dr: -1, dc: -1, priority: 4, allocatedCount: 0 }
        ];

        const totalWords = words.length;
        const allocation = new Map<string, number>();

        if (totalWords <= 8) {
            // 单词数量≤8：每个单词使用不同方向
            const shuffledDirections = [...baseDirections];
            GameUtils.shuffleArray(shuffledDirections);

            for (let i = 0; i < totalWords; i++) {
                const direction = shuffledDirections[i];
                allocation.set(direction.name, 1);
                direction.allocatedCount = 1;
            }
        } else {
            // 单词数量>8：均匀分配到8个方向
            const baseAllocation = Math.floor(totalWords / 8);
            const remainder = totalWords % 8;

            // 基础分配
            for (const direction of baseDirections) {
                allocation.set(direction.name, baseAllocation);
                direction.allocatedCount = baseAllocation;
            }

            // 分配剩余单词（优先分配给高优先级方向）
            const sortedDirections = [...baseDirections].sort((a, b) => a.priority - b.priority);
            for (let i = 0; i < remainder; i++) {
                const direction = sortedDirections[i];
                allocation.set(direction.name, allocation.get(direction.name)! + 1);
                direction.allocatedCount++;
            }
        }

        return {
            directions: baseDirections,
            allocation: allocation,
            totalWords: totalWords
        };
    }

    /**
     * 格式化方向策略信息（用于日志输出）
     * @param strategy 方向分配策略
     * @returns 格式化字符串
     */
    private static _formatDirectionStrategy(strategy: DirectionAllocationStrategy): string {
        const parts: string[] = [];
        for (const [direction, count] of strategy.allocation) {
            if (count > 0) {
                parts.push(`${direction}:${count}`);
            }
        }
        return parts.join(', ');
    }

    /**
     * 按优先级排序单词
     * 长单词优先，相同长度的单词随机排序
     * @param words 单词数据数组
     * @returns 排序后的单词数组
     */
    private static _prioritizeWords(words: IWordData[]): IWordData[] {
        const prioritized = [...words];

        // 按长度降序排序，长单词优先
        prioritized.sort((a, b) => {
            const lengthDiff = b.word.length - a.word.length;
            if (lengthDiff !== 0) return lengthDiff;

            // 相同长度的单词随机排序
            return Math.random() - 0.5;
        });

        return prioritized;
    }





    /**
     * 执行简化放置算法（随机位置生成）
     * @param grid 网格
     * @param words 排序后的单词数组
     * @param strategy 方向分配策略
     * @returns 放置结果
     */
    private static _executeSimplifiedPlacement(
        grid: string[][],
        words: IWordData[],
        strategy: DirectionAllocationStrategy
    ): { success: boolean, placedCount: number } {
        const placedWords: Array<{word: string, direction: string, row: number, col: number}> = [];
        const directionQueues = new Map<string, IWordData[]>();

        // 1. 根据策略将单词分配到方向队列
        this._distributeWordsToDirections(words, strategy, directionQueues);

        // 2. 生成随机位置序列
        let availablePositions = this._generateRandomPositions();

        // 3. 按方向依次放置单词
        for (const direction of strategy.directions) {
            if (direction.allocatedCount === 0) continue;

            const directionWords = directionQueues.get(direction.name) || [];

            for (const wordData of directionWords) {
                const word = wordData.word.toUpperCase();
                let placed = false;

                // 尝试放置单词
                for (let i = 0; i < availablePositions.length && !placed; i++) {
                    const position = availablePositions[i];
                    const row = Math.floor(position / this.GRID_COLS);
                    const col = position % this.GRID_COLS;

                    const result = this._tryPlaceWordAt(grid, word, row, col, {
                        name: direction.name,
                        dr: direction.dr,
                        dc: direction.dc
                    });

                    if (result.success) {
                        this._copyGridTo(result.newGrid, grid);
                        placedWords.push({
                            word: word,
                            direction: direction.name,
                            row: row,
                            col: col
                        });
                        availablePositions.splice(i, 1);
                        placed = true;
                        Logger.debug('WordPlacementEngine',
                            `简化策略放置: ${word} 在 (${row},${col}) ${direction.name}方向`);
                    }
                }

                if (!placed) {
                    Logger.debug('WordPlacementEngine', `简化策略无法放置: ${word}`);
                    return { success: false, placedCount: placedWords.length };
                }
            }
        }

        return { success: true, placedCount: placedWords.length };
    }

    /**
     * 将单词分配到方向队列
     * @param words 单词数组
     * @param strategy 方向分配策略
     * @param directionQueues 方向队列映射
     */
    private static _distributeWordsToDirections(
        words: IWordData[],
        strategy: DirectionAllocationStrategy,
        directionQueues: Map<string, IWordData[]>
    ): void {
        // 初始化队列
        for (const direction of strategy.directions) {
            if (direction.allocatedCount > 0) {
                directionQueues.set(direction.name, []);
            }
        }

        // 分配单词到队列
        let wordIndex = 0;
        for (const direction of strategy.directions) {
            if (direction.allocatedCount > 0) {
                const queue = directionQueues.get(direction.name)!;
                for (let i = 0; i < direction.allocatedCount && wordIndex < words.length; i++) {
                    queue.push(words[wordIndex++]);
                }
            }
        }
    }



    /**
     * 分析方向分布情况
     * @param placedWords 已放置的单词
     * @returns 方向分布字符串
     */
    private static _analyzeDirectionDistribution(
        placedWords: Array<{word: string, direction: string, row: number, col: number}>
    ): string {
        const distribution = new Map<string, number>();

        for (const word of placedWords) {
            const count = distribution.get(word.direction) || 0;
            distribution.set(word.direction, count + 1);
        }

        const parts: string[] = [];
        for (const [direction, count] of distribution) {
            parts.push(`${direction}:${count}`);
        }

        return parts.join(', ');
    }


}
