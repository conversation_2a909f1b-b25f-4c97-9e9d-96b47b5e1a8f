import { Logger } from './Logger';
import { TranslationUrlGenerator } from './TranslationUrlGenerator';
import { TRANSLATION_API_CONFIG, TRANSLATION_PERFORMANCE_CONFIG, TranslationErrorCode } from '../Game/Constants/TranslationConstants';

/**
 * 翻译结果接口
 */
export interface TranslationResult {
    /** 是否成功 */
    success: boolean;
    /** 原始单词 */
    word: string;
    /** 翻译结果 */
    translation?: string;
    /** 错误代码 */
    errorCode?: TranslationErrorCode;
    /** 错误信息 */
    errorMessage?: string;
    /** 请求耗时（毫秒） */
    duration?: number;
}

/**
 * 翻译API客户端
 * 
 * 负责与有道翻译API的网络通信，复用PronunciationApiClient的架构模式。
 * 提供稳定可靠的翻译服务接口。
 * 
 * 核心功能：
 * - 网络请求管理
 * - 重试机制
 * - 并发控制
 * - 错误处理
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-04
 */
export class TranslationApiClient {
    
    /**
     * 当前活跃的请求数量
     */
    private static _activeRequests: number = 0;
    
    /**
     * 请求队列
     */
    private static _requestQueue: Array<{
        word: string;
        resolve: (result: TranslationResult) => void;
        reject: (error: Error) => void;
    }> = [];

    /**
     * 获取单词翻译
     * @param word 要翻译的单词
     * @returns 翻译结果Promise
     */
    public static async getWordTranslation(word: string): Promise<TranslationResult> {
        return new Promise((resolve, reject) => {
            // 检查并发限制
            if (TranslationApiClient._activeRequests >= TRANSLATION_PERFORMANCE_CONFIG.MAX_CONCURRENT_REQUESTS) {
                // 添加到队列
                if (TranslationApiClient._requestQueue.length < TRANSLATION_PERFORMANCE_CONFIG.MAX_QUEUE_SIZE) {
                    TranslationApiClient._requestQueue.push({ word, resolve, reject });
                } else {
                    reject(new Error('请求队列已满'));
                }
                return;
            }

            // 立即处理请求
            TranslationApiClient._processTranslationRequest(word, resolve, reject);
        });
    }

    /**
     * 处理翻译请求
     */
    private static async _processTranslationRequest(
        word: string,
        resolve: (result: TranslationResult) => void,
        reject: (error: Error) => void
    ): Promise<void> {
        TranslationApiClient._activeRequests++;
        const startTime = Date.now();

        try {
            Logger.info('TranslationApiClient', `开始翻译请求: ${word}`);

            // 验证单词参数
            if (!TranslationUrlGenerator.validateWord(word)) {
                resolve(TranslationApiClient._createErrorResult(
                    word,
                    TranslationErrorCode.INVALID_PARAMETER,
                    '单词参数无效'
                ));
                return;
            }

            // 执行翻译请求（带重试机制）
            const result = await TranslationApiClient._translateWithRetry(word);
            
            const duration = Date.now() - startTime;
            result.duration = duration;

            if (result.success) {
                Logger.success('TranslationApiClient', `翻译成功: ${word} -> ${result.translation}, 耗时: ${duration}ms`);
            } else {
                Logger.warn('TranslationApiClient', `翻译失败: ${word}, 错误: ${result.errorMessage}`);
            }

            resolve(result);

        } catch (error) {
            const duration = Date.now() - startTime;
            Logger.error('TranslationApiClient', `翻译请求异常: ${word}`, error as Error);
            
            resolve(TranslationApiClient._createErrorResult(
                word,
                TranslationErrorCode.NETWORK_ERROR,
                (error as Error).message,
                duration
            ));
        } finally {
            // 减少活跃请求计数
            TranslationApiClient._activeRequests--;
            // 处理队列中的下一个请求
            TranslationApiClient._processQueue();
        }
    }

    /**
     * 带重试机制的翻译请求
     */
    private static async _translateWithRetry(word: string): Promise<TranslationResult> {
        let lastError: string = '';

        for (let attempt = 1; attempt <= TRANSLATION_API_CONFIG.RETRY_ATTEMPTS; attempt++) {
            try {
                const result = await TranslationApiClient._fetchTranslation('', word);
                
                if (result.success) {
                    if (attempt > 1) {
                        Logger.info('TranslationApiClient', `翻译在第${attempt}次尝试后成功: ${word}`);
                    }
                    return result;
                }
                
                lastError = result.errorMessage || '未知错误';
                
                // 如果不是最后一次尝试，等待后重试
                if (attempt < TRANSLATION_API_CONFIG.RETRY_ATTEMPTS) {
                    Logger.warn('TranslationApiClient', `翻译第${attempt}次失败: ${word}, ${TRANSLATION_API_CONFIG.RETRY_DELAY}ms后重试`);
                    await TranslationApiClient._delay(TRANSLATION_API_CONFIG.RETRY_DELAY);
                }
                
            } catch (error) {
                lastError = (error as Error).message;
                
                if (attempt < TRANSLATION_API_CONFIG.RETRY_ATTEMPTS) {
                    Logger.warn('TranslationApiClient', `翻译第${attempt}次异常: ${word}, ${TRANSLATION_API_CONFIG.RETRY_DELAY}ms后重试`);
                    await TranslationApiClient._delay(TRANSLATION_API_CONFIG.RETRY_DELAY);
                }
            }
        }

        return TranslationApiClient._createErrorResult(
            word,
            TranslationErrorCode.NETWORK_ERROR,
            `重试${TRANSLATION_API_CONFIG.RETRY_ATTEMPTS}次后仍失败: ${lastError}`
        );
    }

    /**
     * 执行网络请求获取翻译
     */
    private static async _fetchTranslation(unused: string, word: string): Promise<TranslationResult> {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), TRANSLATION_API_CONFIG.TIMEOUT);

        try {
            // 构建POST请求的表单数据 - 使用有道翻译的正确参数格式
            const timestamp = Date.now().toString();
            const formData = new URLSearchParams({
                'i': word,                                    // 要翻译的文本
                'from': 'AUTO',                              // 源语言（自动检测）
                'to': 'AUTO',                                // 目标语言（自动检测）
                'smartresult': 'dict',                       // 智能结果类型
                'client': 'fanyideskweb',                    // 客户端类型
                'salt': timestamp,                           // 时间戳作为盐值
                'sign': '',                                  // 签名（暂时为空）
                'lts': timestamp,                            // 时间戳
                'bv': '',                                    // 浏览器版本（暂时为空）
                'doctype': TRANSLATION_API_CONFIG.DOC_TYPE, // 文档类型
                'version': '2.1',                            // API版本
                'keyfrom': 'fanyi.web',                      // 来源
                'action': 'FY_BY_REALTlME'                   // 动作类型
            });

            Logger.info('TranslationApiClient', `发送翻译请求: ${word}`);
            Logger.debug('TranslationApiClient', `请求参数: ${formData.toString()}`);

            const response = await fetch(TRANSLATION_API_CONFIG.BASE_URL, {
                method: 'POST',
                signal: controller.signal,
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    'Accept': 'application/json, text/javascript, */*; q=0.01',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Referer': 'https://fanyi.youdao.com/',
                    'Origin': 'https://fanyi.youdao.com',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const responseText = await response.text();
            Logger.info('TranslationApiClient', `API响应状态: ${response.status}, 长度: ${responseText.length}`);
            Logger.debug('TranslationApiClient', `API响应内容: ${responseText.substring(0, 300)}...`);

            // 检查响应是否为空
            if (!responseText || responseText.trim().length === 0) {
                throw new Error('API返回空响应');
            }

            // 尝试解析JSON
            let data;
            try {
                data = JSON.parse(responseText);
                Logger.debug('TranslationApiClient', `JSON解析成功，数据类型: ${typeof data}`);
            } catch (parseError) {
                Logger.warn('TranslationApiClient', `JSON解析失败，尝试HTML解析: ${(parseError as Error).message}`);
                // 如果不是JSON，可能是HTML页面，尝试从HTML中提取翻译
                return await TranslationApiClient._parseHtmlResponse(responseText, word);
            }

            // 解析JSON响应
            return TranslationApiClient._parseJsonResponse(data, word);

        } catch (error) {
            clearTimeout(timeoutId);

            if (error.name === 'AbortError') {
                return TranslationApiClient._createErrorResult(
                    word,
                    TranslationErrorCode.REQUEST_TIMEOUT,
                    '请求超时'
                );
            }

            throw error;
        }
    }

    /**
     * 解析JSON响应
     */
    private static _parseJsonResponse(data: any, word: string): TranslationResult {
        try {
            Logger.debug('TranslationApiClient', `解析JSON响应: ${JSON.stringify(data).substring(0, 200)}...`);

            // 有道翻译API的标准响应格式
            if (data.translateResult && Array.isArray(data.translateResult) && data.translateResult.length > 0) {
                const firstResult = data.translateResult[0];
                if (Array.isArray(firstResult) && firstResult.length > 0) {
                    const translationObj = firstResult[0];
                    if (translationObj && translationObj.tgt) {
                        const translation = translationObj.tgt.trim();
                        if (translation && translation !== word.toLowerCase()) {
                            Logger.success('TranslationApiClient', `JSON解析成功: ${word} -> ${translation}`);
                            return {
                                success: true,
                                word,
                                translation
                            };
                        }
                    }
                }
            }

            // 尝试其他可能的响应格式
            if (data.translation && typeof data.translation === 'string') {
                const translation = data.translation.trim();
                if (translation && translation !== word.toLowerCase()) {
                    return {
                        success: true,
                        word,
                        translation
                    };
                }
            }

            // 检查是否有错误信息
            if (data.errorCode && data.errorCode !== 0) {
                return TranslationApiClient._createErrorResult(
                    word,
                    TranslationErrorCode.INVALID_API_RESPONSE,
                    `API错误码: ${data.errorCode}`
                );
            }

            Logger.warn('TranslationApiClient', `JSON响应中未找到有效翻译: ${word}`);
            return TranslationApiClient._createErrorResult(
                word,
                TranslationErrorCode.EMPTY_TRANSLATION,
                '翻译结果为空或无效'
            );
        } catch (error) {
            Logger.error('TranslationApiClient', `JSON解析异常: ${word}`, error as Error);
            return TranslationApiClient._createErrorResult(
                word,
                TranslationErrorCode.INVALID_API_RESPONSE,
                `JSON解析失败: ${(error as Error).message}`
            );
        }
    }

    /**
     * 解析HTML响应（备用方案）
     */
    private static async _parseHtmlResponse(html: string, word: string): Promise<TranslationResult> {
        try {
            Logger.warn('TranslationApiClient', `收到HTML响应，可能是反爬虫机制: ${word}`);

            // 检查是否是错误页面
            if (html.includes('<!DOCTYPE') || html.includes('<html')) {
                Logger.warn('TranslationApiClient', 'API返回HTML页面，可能需要验证或API已变更');

                // 返回错误，让系统使用本地词典
                return TranslationApiClient._createErrorResult(
                    word,
                    TranslationErrorCode.INVALID_API_RESPONSE,
                    'API返回HTML页面，可能被反爬虫拦截'
                );
            }

            // 如果不是标准HTML，尝试简单的文本提取
            const cleanText = html.replace(/<[^>]*>/g, '').trim();
            if (cleanText && cleanText !== word && cleanText.length < 50) {
                Logger.info('TranslationApiClient', `从响应中提取到文本: ${word} -> ${cleanText}`);
                return {
                    success: true,
                    word,
                    translation: cleanText
                };
            }

            return TranslationApiClient._createErrorResult(
                word,
                TranslationErrorCode.EMPTY_TRANSLATION,
                'HTML响应中未找到有效翻译'
            );
        } catch (error) {
            Logger.error('TranslationApiClient', `HTML解析异常: ${word}`, error as Error);
            return TranslationApiClient._createErrorResult(
                word,
                TranslationErrorCode.INVALID_API_RESPONSE,
                `HTML解析失败: ${(error as Error).message}`
            );
        }
    }

    /**
     * 处理请求队列
     */
    private static _processQueue(): void {
        if (TranslationApiClient._requestQueue.length > 0 &&
            TranslationApiClient._activeRequests < TRANSLATION_PERFORMANCE_CONFIG.MAX_CONCURRENT_REQUESTS) {

            const { word, resolve, reject } = TranslationApiClient._requestQueue.shift()!;
            TranslationApiClient._processTranslationRequest(word, resolve, reject);
        }
    }

    /**
     * 创建错误结果
     */
    private static _createErrorResult(
        word: string,
        errorCode: TranslationErrorCode,
        errorMessage: string,
        duration?: number
    ): TranslationResult {
        return {
            success: false,
            word,
            errorCode,
            errorMessage,
            duration
        };
    }

    /**
     * 延迟函数
     */
    private static _delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取客户端状态
     */
    public static getStatus(): {
        activeRequests: number;
        queueLength: number;
        maxConcurrent: number;
    } {
        return {
            activeRequests: TranslationApiClient._activeRequests,
            queueLength: TranslationApiClient._requestQueue.length,
            maxConcurrent: TRANSLATION_PERFORMANCE_CONFIG.MAX_CONCURRENT_REQUESTS
        };
    }
}
