import { _decorator, Component } from 'cc';
import { Logger } from '../../Utils/Logger';
import { IWordData } from '../../Data/WordDatabase';
import { WordValidator, GridConnectivityResult } from './WordValidator';

const { ccclass } = _decorator;

/**
 * 游戏可玩性验证器
 * 专门负责验证生成的字母网格是否能够保证游戏的可玩性
 * 遵循单一职责原则，专注于可玩性验证逻辑
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-18
 */
@ccclass('GameplayValidator')
export class GameplayValidator extends Component {

    /**
     * 单例实例
     */
    private static _instance: GameplayValidator | null = null;

    /**
     * 内部验证器
     */
    private _wordValidator: WordValidator | null = null;

    /**
     * 验证统计信息
     */
    private _validationStats = {
        totalValidations: 0,
        successfulValidations: 0,
        failedValidations: 0,
        averageAttempts: 0
    };

    /**
     * 获取单例实例
     */
    public static getInstance(): GameplayValidator | null {
        return GameplayValidator._instance;
    }

    /**
     * 组件初始化
     */
    onLoad(): void {
        if (GameplayValidator._instance === null) {
            GameplayValidator._instance = this;
            this._wordValidator = new WordValidator();
            Logger.info('GameplayValidator', '游戏可玩性验证器初始化完成');
        } else {
            Logger.warn('GameplayValidator', '检测到重复的GameplayValidator实例');
            this.node.destroy();
        }
    }

    /**
     * 验证字母网格的游戏可玩性
     * @param letterGrid 字母网格 (9x8)
     * @param targetWords 目标单词列表
     * @returns 可玩性验证结果
     */
    public validateGameplayability(letterGrid: string[][], targetWords: IWordData[]): GameplayabilityResult {
        if (!this._wordValidator) {
            Logger.error('GameplayValidator', '验证器未初始化');
            return this._createFailureResult('验证器未初始化');
        }

        if (!letterGrid || !targetWords || targetWords.length === 0) {
            Logger.error('GameplayValidator', '输入参数无效');
            return this._createFailureResult('输入参数无效');
        }

        try {
            // 设置目标单词
            this._wordValidator.setTargetWords(targetWords);

            // 执行连接性验证
            const connectivityResult = this._wordValidator.validateGridConnectivity(letterGrid);

            // 更新统计信息
            this._updateValidationStats(connectivityResult.isValid);

            // 构建结果
            const result: GameplayabilityResult = {
                isPlayable: connectivityResult.isValid,
                connectivityResult: connectivityResult,
                validationTime: Date.now(),
                recommendations: this._generateRecommendations(connectivityResult),
                summary: this._generateSummary(connectivityResult)
            };

            if (!result.isPlayable) {
                Logger.warn('GameplayValidator',
                    `游戏可玩性验证失败：${connectivityResult.unconnectableWords.length} 个单词无法连接`);
            }

            return result;

        } catch (error) {
            Logger.error('GameplayValidator', '验证过程中发生异常', error as Error);
            this._updateValidationStats(false);
            return this._createFailureResult(`验证异常: ${(error as Error).message}`);
        }
    }

    /**
     * 快速验证字母网格的基本可玩性
     * 仅检查字母存在性，不进行完整路径搜索
     * @param letterGrid 字母网格
     * @param targetWords 目标单词列表
     * @returns 是否通过基本验证
     */
    public quickValidatePlayability(letterGrid: string[][], targetWords: IWordData[]): boolean {
        if (!this._wordValidator) {
            Logger.error('GameplayValidator', '验证器未初始化');
            return false;
        }

        try {
            this._wordValidator.setTargetWords(targetWords);
            return this._wordValidator.quickValidateGridLetters(letterGrid);
        } catch (error) {
            Logger.error('GameplayValidator', '快速验证失败', error as Error);
            return false;
        }
    }

    /**
     * 获取验证统计信息
     * @returns 统计信息对象
     */
    public getValidationStats(): ValidationStatistics {
        return {
            ...this._validationStats,
            successRate: this._validationStats.totalValidations > 0 
                ? this._validationStats.successfulValidations / this._validationStats.totalValidations 
                : 0
        };
    }

    /**
     * 重置验证统计信息
     */
    public resetValidationStats(): void {
        this._validationStats = {
            totalValidations: 0,
            successfulValidations: 0,
            failedValidations: 0,
            averageAttempts: 0
        };
        Logger.info('GameplayValidator', '验证统计信息已重置');
    }

    /**
     * 创建失败结果
     * @param reason 失败原因
     * @returns 失败结果对象
     */
    private _createFailureResult(reason: string): GameplayabilityResult {
        return {
            isPlayable: false,
            connectivityResult: {
                isValid: false,
                connectableWords: [],
                unconnectableWords: [],
                totalWords: 0,
                connectableCount: 0,
                validationDetails: []
            },
            validationTime: Date.now(),
            recommendations: [`建议重新生成字母网格：${reason}`],
            summary: `验证失败：${reason}`
        };
    }

    /**
     * 更新验证统计信息
     * @param success 是否成功
     */
    private _updateValidationStats(success: boolean): void {
        this._validationStats.totalValidations++;
        if (success) {
            this._validationStats.successfulValidations++;
        } else {
            this._validationStats.failedValidations++;
        }
    }

    /**
     * 生成改进建议
     * @param connectivityResult 连接性验证结果
     * @returns 建议列表
     */
    private _generateRecommendations(connectivityResult: GridConnectivityResult): string[] {
        const recommendations: string[] = [];

        if (!connectivityResult.isValid) {
            recommendations.push('建议重新生成字母网格以确保所有单词可连接');
            
            if (connectivityResult.unconnectableWords.length > 0) {
                recommendations.push(`特别关注无法连接的单词: ${connectivityResult.unconnectableWords.join(', ')}`);
            }

            if (connectivityResult.connectableCount < connectivityResult.totalWords / 2) {
                recommendations.push('当前网格质量较差，建议调整单词放置算法');
            }
        } else {
            recommendations.push('当前网格质量良好，所有单词都可连接');
        }

        return recommendations;
    }

    /**
     * 生成验证摘要
     * @param connectivityResult 连接性验证结果
     * @returns 摘要字符串
     */
    private _generateSummary(connectivityResult: GridConnectivityResult): string {
        const { connectableCount, totalWords, unconnectableWords } = connectivityResult;
        
        if (connectivityResult.isValid) {
            return `验证通过：所有 ${totalWords} 个单词都可连接`;
        } else {
            return `验证失败：${connectableCount}/${totalWords} 个单词可连接，无法连接: [${unconnectableWords.join(', ')}]`;
        }
    }

    /**
     * 组件销毁时清理资源
     */
    onDestroy(): void {
        if (GameplayValidator._instance === this) {
            GameplayValidator._instance = null;
            this._wordValidator = null;
            Logger.info('GameplayValidator', '游戏可玩性验证器已清理');
        }
    }
}

/**
 * 游戏可玩性验证结果接口
 */
export interface GameplayabilityResult {
    /** 是否可玩 */
    isPlayable: boolean;
    /** 连接性验证结果 */
    connectivityResult: GridConnectivityResult;
    /** 验证时间戳 */
    validationTime: number;
    /** 改进建议 */
    recommendations: string[];
    /** 验证摘要 */
    summary: string;
}

/**
 * 验证统计信息接口
 */
export interface ValidationStatistics {
    /** 总验证次数 */
    totalValidations: number;
    /** 成功验证次数 */
    successfulValidations: number;
    /** 失败验证次数 */
    failedValidations: number;
    /** 平均尝试次数 */
    averageAttempts: number;
    /** 成功率 */
    successRate: number;
}
