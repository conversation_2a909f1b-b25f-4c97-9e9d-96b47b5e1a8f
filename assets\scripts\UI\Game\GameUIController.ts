import { _decorator, Component, Node, Button } from 'cc';
import { SceneManager } from '../../Core/SceneManager';
import { GameDataManager } from '../../Core/GameDataManager';
import { CoreSystemManager } from '../../Core/CoreSystemManager';
import { WordAreaController } from '../../Game/Controllers/WordAreaController';
import { LetterGridController } from '../../Game/Controllers/LetterGridController';
import { WordConnectionController } from '../../Game/Controllers/WordConnectionController';

import { AnimationManager } from '../../Game/Animation/AnimationManager';
import { WordPronunciationManager } from '../../Core/WordPronunciationManager';
import { WordPronunciationService } from '../../Game/Services/WordPronunciationService';
import { Logger } from '../../Utils/Logger';
import { AudioIntegrationService } from '../../Core/AudioIntegrationService';
const { ccclass, property } = _decorator;

/**
 * 游戏界面控制器 - 负责游戏场景中的UI交互逻辑
 * 遵循单一职责原则，只处理游戏界面相关的UI交互
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-03
 */
@ccclass('GameUIController')
export class GameUIController extends Component {

    /**
     * 返回按钮节点引用
     * 需要在编辑器中手动拖拽赋值
     */
    @property(Node)
    backButton: Node = null!;

    /**
     * 暂停按钮节点引用
     */
    @property(Node)
    pauseButton: Node = null!;

    /**
     * 单词区域控制器节点引用
     * 需要在编辑器中拖拽包含WordAreaController脚本的节点
     */
    @property(Node)
    wordAreaNode: Node = null!;

    /**
     * 字母网格控制器节点引用
     * 需要在编辑器中拖拽包含LetterGridController脚本的节点
     */
    @property(Node)
    letterAreaNode: Node = null!;

    /**
     * 字母连线控制器节点引用
     * 需要在编辑器中拖拽包含WordConnectionController脚本的节点
     */
    @property(Node)
    connectionControllerNode: Node = null!;



    /**
     * 按钮组件缓存
     */
    private _backButtonComponent: Button | null = null;
    private _pauseButtonComponent: Button | null = null;

    /**
     * 单词区域控制器组件缓存
     */
    private _wordAreaController: WordAreaController | null = null;



    /**
     * 字母网格控制器组件缓存
     */
    private _letterGridController: LetterGridController | null = null;

    /**
     * 字母连线控制器组件缓存
     */
    private _connectionController: WordConnectionController | null = null;



    /**
     * 游戏数据管理器引用
     */
    private _gameDataManager: GameDataManager | null = null;

    /**
     * 音效集成服务引用
     */
    private _audioService: AudioIntegrationService | null = null;

    /**
     * 当前关卡ID
     */
    private _currentLevelId: number = 1;

    /**
     * 组件生命周期 - 初始化
     */
    onLoad() {
        Logger.info('GameUIController', '游戏界面控制器初始化');
        this._initializeComponents();
        this._initializeDataManager();
    }

    /**
     * 组件生命周期 - 启动
     */
    start() {
        this._bindEvents();
        this._initializeConnectionSystem();
        this._initializeGameContent();
        this._reinitializePronunciationSystem();
    }

    /**
     * 组件销毁时清理
     */
    onDestroy() {
        this._unbindEvents();
    }

    /**
     * 初始化组件引用
     */
    private _initializeComponents(): void {
        // 初始化返回按钮
        if (this.backButton) {
            this._backButtonComponent = this.backButton.getComponent(Button);
            if (!this._backButtonComponent) {
                Logger.warn('GameUIController', '返回按钮缺少Button组件');
            }
        }

        // 初始化暂停按钮
        if (this.pauseButton) {
            this._pauseButtonComponent = this.pauseButton.getComponent(Button);
            if (!this._pauseButtonComponent) {
                Logger.warn('GameUIController', '暂停按钮缺少Button组件');
            }
        }

        // 初始化单词区域控制器
        if (this.wordAreaNode) {
            this._wordAreaController = this.wordAreaNode.getComponent(WordAreaController);
            if (!this._wordAreaController) {
                Logger.error('GameUIController', '单词区域节点缺少WordAreaController组件！');
            } else {
                Logger.info('GameUIController', '单词区域控制器初始化成功');
            }
        } else {
            Logger.warn('GameUIController', '单词区域节点未设置');
        }

        // 初始化字母网格控制器
        if (this.letterAreaNode) {
            this._letterGridController = this.letterAreaNode.getComponent(LetterGridController);
            if (!this._letterGridController) {
                Logger.error('GameUIController', '字母区域节点缺少LetterGridController组件！');
            } else {
                Logger.info('GameUIController', '字母网格控制器初始化成功');
            }
        } else {
            Logger.warn('GameUIController', '字母区域节点未设置');
        }

        // 初始化字母连线控制器
        if (this.connectionControllerNode) {
            this._connectionController = this.connectionControllerNode.getComponent(WordConnectionController);
            if (!this._connectionController) {
                Logger.error('GameUIController', '连线控制器节点缺少WordConnectionController组件！');
            } else {
                Logger.info('GameUIController', '字母连线控制器初始化成功');
            }


        } else {
            Logger.warn('GameUIController', '连线控制器节点未设置');
        }



        Logger.info('GameUIController', '组件初始化完成');
    }

    /**
     * 初始化数据管理器引用
     */
    private _initializeDataManager(): void {
        // 首先尝试从CoreSystemManager获取
        const coreSystemManager = CoreSystemManager.getInstance();
        if (coreSystemManager) {
            this._gameDataManager = coreSystemManager.getGameDataManager();
            if (this._gameDataManager) {
                Logger.info('GameUIController', 'GameDataManager获取成功（通过CoreSystemManager）');
            }
        }

        // 回退到直接获取实例
        if (!this._gameDataManager) {
            this._gameDataManager = GameDataManager.getInstance();
            if (!this._gameDataManager) {
                Logger.warn('GameUIController', 'GameDataManager未初始化，将使用实时生成模式');
            } else {
                Logger.info('GameUIController', 'GameDataManager获取成功（直接获取）');
            }
        }

        // 初始化音效服务
        if (coreSystemManager) {
            this._audioService = coreSystemManager.getAudioIntegrationService();
            if (this._audioService) {
                Logger.info('GameUIController', '音效集成服务获取成功（通过CoreSystemManager）');
            }
        }

        // 回退到直接获取实例
        if (!this._audioService) {
            this._audioService = AudioIntegrationService.getInstance();
            if (!this._audioService) {
                Logger.warn('GameUIController', '音效集成服务未初始化，音效功能将不可用');
            } else {
                Logger.info('GameUIController', '音效集成服务获取成功（直接获取）');
            }
        }
    }

    /**
     * 初始化连线系统
     */
    private _initializeConnectionSystem(): void {
        Logger.info('GameUIController', '初始化连线系统');

        // 检查必要组件是否已初始化
        if (!this._letterGridController) {
            Logger.error('GameUIController', '字母网格控制器未初始化，无法设置连线系统');
            return;
        }

        if (!this._wordAreaController) {
            Logger.error('GameUIController', '单词区域控制器未初始化，无法设置连线系统');
            return;
        }

        if (!this._connectionController) {
            Logger.error('GameUIController', '连线控制器未初始化，无法设置连线系统');
            return;
        }

        // 设置连线控制器的依赖组件
        this._connectionController.letterGridController = this._letterGridController;
        this._connectionController.wordAreaController = this._wordAreaController;



        Logger.info('GameUIController', '连线系统初始化完成');
    }

    /**
     * 绑定事件监听
     */
    private _bindEvents(): void {
        // 绑定返回按钮事件
        if (this._backButtonComponent && this._backButtonComponent.node) {
            this._backButtonComponent.node.on(Button.EventType.CLICK, this._onBackButtonClicked, this);
        } else if (this.backButton) {
            Logger.warn('GameUIController', '返回按钮节点存在但缺少Button组件');
        }

        // 绑定暂停按钮事件
        if (this._pauseButtonComponent && this._pauseButtonComponent.node) {
            this._pauseButtonComponent.node.on(Button.EventType.CLICK, this._onPauseButtonClicked, this);
        }

        Logger.info('GameUIController', '事件绑定完成');
    }

    /**
     * 初始化游戏内容（使用预生成数据）
     */
    private _initializeGameContent(): void {
        Logger.info('GameUIController', '开始初始化游戏内容');

        // 立即使用预生成的数据，无需延迟
        this._loadPregeneratedGameData();
    }

    /**
     * 加载预生成的游戏数据
     */
    private _loadPregeneratedGameData(): void {
        if (!this._wordAreaController || !this._letterGridController) {
            Logger.error('GameUIController', '单词区域或字母网格控制器未初始化');
            return;
        }

        if (!this._gameDataManager) {
            Logger.warn('GameUIController', '数据管理器未初始化，回退到实时生成模式');
            this._generateLettersFromWords();
            return;
        }

        // 检查是否有预生成的数据
        const isPregenerated = this._gameDataManager.isLevelPregenerated(this._currentLevelId);
        if (!isPregenerated) {
            Logger.warn('GameUIController', `关卡 ${this._currentLevelId} 数据未预生成，回退到实时生成模式`);
            this._generateLettersFromWords();
            return;
        }

        // 获取预生成的数据
        const pregeneratedWords = this._gameDataManager.getPregeneratedWords(this._currentLevelId);
        const pregeneratedLetters = this._gameDataManager.getPregeneratedLetters(this._currentLevelId);

        if (!pregeneratedWords || !pregeneratedLetters) {
            Logger.error('GameUIController', `获取关卡 ${this._currentLevelId} 预生成数据失败`);
            this._generateLettersFromWords();
            return;
        }

        Logger.info('GameUIController', `预生成数据初始化完成 - 单词:${pregeneratedWords.length}, 字母:${pregeneratedLetters.length}`);

        // 立即设置单词区域（无闪烁）
        this._wordAreaController.setWordsDirectly(pregeneratedWords);

        // 立即设置字母网格（无闪烁）
        this._letterGridController.setLettersDirectly(pregeneratedLetters);

        // 初始化连线系统的目标单词
        this._setupConnectionTargets(pregeneratedWords);
    }

    /**
     * 回退到实时生成模式
     */
    private _fallbackToRealTimeGeneration(): void {
        Logger.info('GameUIController', '回退到实时生成模式');

        if (this._wordAreaController) {
            this._wordAreaController.reshuffleWords();
            // 触发单词重新布局
        }

        // 重新生成字母
        this._generateLettersFromWords();
    }

    /**
     * 根据单词区域的单词生成字母到9行8列网格
     */
    private _generateLettersFromWords(): void {
        if (!this._wordAreaController || !this._letterGridController) {
            Logger.error('GameUIController', '单词区域或字母网格控制器未初始化');
            return;
        }

        // 获取当前单词数据
        const currentWords = this._wordAreaController.getCurrentWords();
        if (!currentWords || currentWords.length === 0) {
            Logger.error('GameUIController', '无法获取当前单词数据');
            return;
        }

        // 开始生成字母网格

        // 生成并显示字母到网格
        this._letterGridController.generateAndDisplayLetters(currentWords);

        // 初始化连线系统的目标单词
        this._setupConnectionTargets(currentWords);

        // 字母网格生成完成
    }

    /**
     * 设置连线系统的目标单词
     * @param words 目标单词数组
     */
    private _setupConnectionTargets(words: any[]): void {
        if (!this._connectionController) {
            Logger.warn('GameUIController', '连线控制器未初始化，跳过目标单词设置');
            return;
        }

        // 设置连线系统目标单词

        // 设置连线控制器的目标单词
        this._connectionController.setTargetWords(words);

        // 连线系统设置完成
    }

    /**
     * 清理游戏状态（场景切换前调用）
     */
    private _cleanupGameState(): void {
        try {
            // 开始清理游戏状态

            // 清理动画管理器
            const animationManager = AnimationManager.getInstance();
            if (animationManager) {
                animationManager.stopAllAnimations();
            }



            // 游戏状态清理完成
        } catch (error) {
            Logger.warn('GameUIController', '清理游戏状态时发生错误', error as Error);
        }
    }

    /**
     * 解绑事件监听
     */
    private _unbindEvents(): void {
        // 解绑返回按钮事件
        if (this._backButtonComponent && this._backButtonComponent.node && this._backButtonComponent.node.isValid) {
            this._backButtonComponent.node.off(Button.EventType.CLICK, this._onBackButtonClicked, this);
        }

        // 解绑暂停按钮事件
        if (this._pauseButtonComponent && this._pauseButtonComponent.node && this._pauseButtonComponent.node.isValid) {
            this._pauseButtonComponent.node.off(Button.EventType.CLICK, this._onPauseButtonClicked, this);
        }

        // 事件解绑完成
    }

    /**
     * 返回按钮点击事件处理
     */
    private async _onBackButtonClicked(): Promise<void> {
        Logger.info('GameUIController', '返回按钮被点击');

        // 播放按钮点击音效
        if (this._audioService) {
            this._audioService.playButtonClickSound();
        }

        try {
            // 禁用按钮防止重复点击
            this._setBackButtonInteractable(false);

            // 主动清理游戏状态和动画
            this._cleanupGameState();

            // 获取场景管理器实例
            const sceneManager = SceneManager.getInstance();
            if (!sceneManager) {
                Logger.error('GameUIController', '场景管理器未找到！');
                this._setBackButtonInteractable(true);
                return;
            }

            // 切换回主菜单场景
            const success = await sceneManager.loadMainMenu();

            if (!success) {
                Logger.error('GameUIController', '返回主菜单失败！');
                this._setBackButtonInteractable(true);
                return;
            }

        } catch (error) {
            Logger.error('GameUIController', '返回主菜单时发生错误', error as Error);
            this._setBackButtonInteractable(true);
        }
    }

    /**
     * 暂停按钮点击事件处理
     */
    private _onPauseButtonClicked(): void {
        // 暂停按钮被点击

        // 播放按钮点击音效
        if (this._audioService) {
            this._audioService.playButtonClickSound();
        }

        // 暂停功能暂未实现，保持简洁
    }

    /**
     * 设置返回按钮可交互状态
     * @param interactable 是否可交互
     */
    private _setBackButtonInteractable(interactable: boolean): void {
        if (this._backButtonComponent) {
            this._backButtonComponent.interactable = interactable;
        }
    }

    /**
     * 公共方法：返回主菜单（供其他脚本调用）
     */
    public returnToMainMenu(): void {
        this._onBackButtonClicked();
    }

    /**
     * 公共方法：暂停游戏（供其他脚本调用）
     */
    public pauseGame(): void {
        this._onPauseButtonClicked();
    }

    /**
     * 公共方法：重新随机布局单词（供其他脚本调用）
     */
    public async reshuffleWords(): Promise<void> {
        Logger.debug('GameUIController', '触发单词重新布局');

        // 🔧 重要：重新初始化发音系统，确保重新开始游戏后发音正常
        this._reinitializePronunciationSystem();

        if (this._gameDataManager) {
            // 使用预生成模式：重新生成数据
            const regenerateSuccess = await this._gameDataManager.regenerateGameData(this._currentLevelId);
            if (regenerateSuccess) {
                this._loadPregeneratedGameData();
                Logger.info('GameUIController', '使用预生成模式重新布局完成');
            } else {
                Logger.warn('GameUIController', '预生成重新布局失败，回退到实时模式');
                this._fallbackToRealTimeGeneration();
            }
        } else {
            // 回退到实时生成模式
            this._fallbackToRealTimeGeneration();
        }
    }

    /**
     * 公共方法：重新随机布局字母（供其他脚本调用）
     */
    public async reshuffleLetters(): Promise<void> {
        Logger.debug('GameUIController', '触发字母网格重新布局');

        // 🔧 重要：重新初始化发音系统，确保重新开始游戏后发音正常
        this._reinitializePronunciationSystem();

        if (this._gameDataManager) {
            // 使用预生成模式：重新生成数据
            const regenerateSuccess = await this._gameDataManager.regenerateGameData(this._currentLevelId);
            if (regenerateSuccess) {
                this._loadPregeneratedGameData();
                Logger.info('GameUIController', '使用预生成模式重新布局完成');
            } else {
                Logger.warn('GameUIController', '预生成重新布局失败，回退到实时模式');
                this._fallbackToRealTimeGeneration();
            }
        } else {
            // 回退到实时生成模式
            this._fallbackToRealTimeGeneration();
        }
    }

    /**
     * 公共方法：切换关卡（供其他脚本调用）
     * @param levelId 目标关卡ID
     */
    public switchLevel(levelId: number): void {
        if (this._wordAreaController) {
            this._wordAreaController.switchToLevel(levelId);
            Logger.info('GameUIController', `切换到关卡 ${levelId}`);

            // 重新生成字母
            this._generateLettersFromWords();
        } else {
            Logger.warn('GameUIController', '单词区域控制器未初始化，无法切换关卡');
        }
    }

    /**
     * 公共方法：获取字母网格控制器（供其他脚本调用）
     * @returns 字母网格控制器实例
     */
    public getLetterGridController(): LetterGridController | null {
        return this._letterGridController;
    }

    /**
     * 公共方法：获取单词区域控制器（供其他脚本调用）
     * @returns 单词区域控制器实例
     */
    public getWordAreaController(): WordAreaController | null {
        return this._wordAreaController;
    }

    /**
     * 公共方法：获取字母连线控制器（供其他脚本调用）
     * @returns 字母连线控制器实例
     */
    public getConnectionController(): WordConnectionController | null {
        return this._connectionController;
    }



    /**
     * 公共方法：重置游戏状态
     */
    public resetGame(): void {
        Logger.info('GameUIController', '重置游戏状态');

        // 重置连线控制器
        if (this._connectionController) {
            this._connectionController.resetGame();
        }

        // 🔧 重要：重新初始化发音系统，确保重新开始游戏后发音正常
        this._reinitializePronunciationSystem();

        // 重新生成游戏内容
        this._initializeGameContent();

        Logger.info('GameUIController', '游戏状态重置完成');
    }

    /**
     * 重新初始化发音系统
     * 确保在场景切换后发音功能正常工作
     */
    private _reinitializePronunciationSystem(): void {
        try {
            Logger.info('GameUIController', '开始重新初始化发音系统');

            // 🔧 分阶段重新初始化发音系统，确保可靠性
            this.scheduleOnce(() => {
                // 第一阶段：重新初始化发音管理器
                const pronunciationManager = WordPronunciationManager.getInstance();
                if (pronunciationManager) {
                    pronunciationManager.forceReinitialize();
                    Logger.info('GameUIController', '发音管理器重新初始化已启动');
                } else {
                    Logger.warn('GameUIController', '未找到发音管理器实例');
                }

            }, 0.1); // 延迟100ms执行

            // 🔧 第二阶段：验证和重新连接发音服务
            this.scheduleOnce(() => {
                const pronunciationService = WordPronunciationService.getInstance();
                if (pronunciationService) {
                    // 通过调用服务状态检查来触发重新连接
                    const status = pronunciationService.getServiceStatus();
                    Logger.info('GameUIController', `发音服务状态: 启用=${status.enabled}, 管理器可用=${status.managerAvailable}`);

                    if (status.managerAvailable) {
                        Logger.success('GameUIController', '发音服务重新连接成功');

                        // 发音服务重新连接成功
                    } else {
                        Logger.warn('GameUIController', '发音服务重新连接失败，尝试再次重新连接');

                        // 🔧 如果失败，再次尝试重新连接
                        this.scheduleOnce(() => {
                            const retryStatus = pronunciationService.getServiceStatus();
                            Logger.info('GameUIController', `重试后发音服务状态: 启用=${retryStatus.enabled}, 管理器可用=${retryStatus.managerAvailable}`);
                        }, 1.0);
                    }
                } else {
                    Logger.warn('GameUIController', '未找到发音服务实例');
                }

            }, 0.3); // 延迟300ms执行，确保管理器初始化完成

        } catch (error) {
            Logger.error('GameUIController', '重新初始化发音系统时发生异常', error as Error);
        }
    }


}
