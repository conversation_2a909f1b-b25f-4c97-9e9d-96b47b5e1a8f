import { _decorator, Component, Node, Label, tween, Color } from 'cc';
import { WordDatabase, IWordData } from '../../Data/WordDatabase';
import { GameUtils } from '../../Utils/GameUtils';
import { Logger } from '../../Utils/Logger';
import { ColorThemeManager } from '../Managers/ColorThemeManager';
import { WordCompletionAnimator } from '../Components/WordCompletionAnimator';
const { ccclass, property } = _decorator;

/**
 * 单词区域控制器
 *
 * 负责管理游戏中单词显示区域的布局、状态和动画效果。
 * 提供单词的显示、完成状态管理和视觉反馈功能。
 *
 * 核心功能：
 * - 单词显示管理：显示当前关卡的目标单词列表
 * - 完成状态跟踪：跟踪每个单词的完成状态
 * - 颜色主题集成：为每个单词分配独特的颜色标识
 * - 完成动画：播放单词完成时的动画效果
 * - 进度统计：统计游戏进度和完成情况
 *
 * 视觉特性：
 * - 马卡龙色彩主题：为每个单词分配美观的颜色
 * - 完成状态动画：单词完成时的颜色过渡效果
 * - 响应式布局：适应不同数量的单词显示
 * - 状态指示：清晰的完成/未完成状态区分
 *
 * 技术实现：
 * - 动态Label创建和管理
 * - 高效的状态缓存机制
 * - 流畅的颜色过渡动画
 * - 完善的错误处理和边界检查
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-03
 */
@ccclass('WordAreaController')
export class WordAreaController extends Component {

    /**
     * 颜色过渡动画配置
     */
    private static readonly ANIMATION_CONFIG = {
        /** 动画持续时间（秒） */
        DURATION: 0.4,
        /** 缓动函数类型 */
        EASING: 'sineOut',
        /** 是否启用动画（可配置） */
        ENABLED: true
    } as const;

    /**
     * 单词节点引用数组 - 需要在编辑器中按顺序拖拽赋值
     * 顺序：单词1, 单词2, 单词3, 单词4, 单词5, 单词6, 单词7, 单词8
     */
    @property([Node])
    wordNodes: Node[] = [];

    /**
     * 单词完成动画器引用 - 需要在编辑器中拖拽赋值
     */
    @property(WordCompletionAnimator)
    wordCompletionAnimator: WordCompletionAnimator = null!;

    /**
     * 当前关卡ID
     */
    @property({ displayName: "关卡ID", tooltip: "当前要显示的关卡编号" })
    currentLevelId: number = 1;

    /**
     * 当前关卡的单词数据
     */
    private _currentLevelWords: IWordData[] = [];

    /**
     * 单词节点的Label组件缓存
     */
    private _wordLabels: Label[] = [];

    /**
     * 是否已初始化
     */
    private _isInitialized: boolean = false;

    /**
     * 颜色主题管理器
     */
    private _colorThemeManager: ColorThemeManager | null = null;

    /**
     * 已完成单词的状态跟踪
     * key: 单词文本（大写），value: 是否已完成
     */
    private _completedWordsStatus: Map<string, boolean> = new Map();

    /**
     * 正在播放动画的单词跟踪
     * key: 单词文本（大写），value: 动画Tween对象
     */
    private _activeAnimations: Map<string, any> = new Map();

    /**
     * 组件生命周期 - 初始化
     */
    onLoad() {
        this._initializeComponents();
    }

    /**
     * 组件生命周期 - 启动
     */
    start() {
        if (this._isInitialized) {
            this._loadAndDisplayWords();
        }
    }

    /**
     * 初始化组件引用
     */
    private _initializeComponents(): void {
        // 验证单词节点数量
        if (!this.wordNodes || this.wordNodes.length !== 8) {
            Logger.error('WordAreaController', `单词节点数量不正确！期望8个，实际: ${this.wordNodes?.length || 0}`);
            return;
        }

        // 获取所有单词节点的Label组件
        this._wordLabels = [];
        for (let i = 0; i < this.wordNodes.length; i++) {
            const wordNode = this.wordNodes[i];
            if (!wordNode) {
                Logger.error('WordAreaController', `单词节点 ${i + 1} 未设置！`);
                return;
            }

            const label = wordNode.getComponent(Label);
            if (!label) {
                Logger.error('WordAreaController', `单词节点 ${i + 1} 缺少Label组件！`);
                return;
            }

            this._wordLabels.push(label);
        }

        // 获取颜色主题管理器单例实例
        this._colorThemeManager = ColorThemeManager.getInstance();

        // 验证WordCompletionAnimator组件
        if (!this.wordCompletionAnimator) {
            Logger.warn('WordAreaController', 'WordCompletionAnimator未设置，将使用原有动画方式');
        } else {
            Logger.info('WordAreaController', 'WordCompletionAnimator组件已配置');
        }

        this._isInitialized = true;
    }

    /**
     * 加载并显示当前关卡的单词
     */
    private _loadAndDisplayWords(): void {
        // 验证关卡数据
        if (!WordDatabase.validateLevelData(this.currentLevelId)) {
            Logger.error('WordAreaController', `关卡 ${this.currentLevelId} 数据验证失败！`);
            return;
        }

        // 获取关卡单词数据
        this._currentLevelWords = WordDatabase.getLevelWords(this.currentLevelId);
        if (GameUtils.isArrayEmpty(this._currentLevelWords)) {
            Logger.error('WordAreaController', `关卡 ${this.currentLevelId} 没有单词数据！`);
            return;
        }

        // 验证单词数量与节点数量是否匹配
        if (!GameUtils.arraysLengthMatch(this._currentLevelWords, this._wordLabels)) {
            Logger.error('WordAreaController', '单词数量与节点数量不匹配！');
            return;
        }

        // 随机打乱单词顺序
        const shuffledWords = GameUtils.shuffleArray(this._currentLevelWords);

        // 显示单词到对应节点
        this._displayWordsToNodes(shuffledWords);
    }

    /**
     * 将单词显示到对应的节点上
     * @param words 要显示的单词数组
     */
    private _displayWordsToNodes(words: IWordData[]): void {
        for (let i = 0; i < words.length && i < this._wordLabels.length; i++) {
            const wordData = words[i];
            const label = this._wordLabels[i];

            if (wordData && label) {
                // 设置单词文本
                label.string = wordData.word;

                // 可以在这里设置其他样式属性
                // label.fontSize = 24;
                // label.color = Color.BLACK;
            }
        }
    }

    /**
     * 重新随机布局当前关卡的单词
     * 公共方法，可供其他脚本调用
     */
    public reshuffleWords(): void {
        if (!this._isInitialized || GameUtils.isArrayEmpty(this._currentLevelWords)) {
            Logger.warn('WordAreaController', '无法重新布局，组件未初始化或没有单词数据');
            return;
        }

        const shuffledWords = GameUtils.shuffleArray(this._currentLevelWords);
        this._displayWordsToNodes(shuffledWords);
    }

    /**
     * 切换到指定关卡
     * @param levelId 目标关卡ID
     */
    public switchToLevel(levelId: number): void {
        if (!WordDatabase.hasLevel(levelId)) {
            Logger.error('WordAreaController', `关卡 ${levelId} 不存在！`);
            return;
        }

        this.currentLevelId = levelId;
        
        if (this._isInitialized) {
            this._loadAndDisplayWords();
        }
    }

    /**
     * 获取当前显示的单词列表
     * @returns 当前单词数据数组
     */
    public getCurrentWords(): IWordData[] {
        return [...this._currentLevelWords];
    }

    /**
     * 获取指定位置的单词
     * @param index 节点索引（0-7）
     * @returns 单词数据，如果索引无效则返回null
     */
    public getWordAtPosition(index: number): string | null {
        if (!Number.isInteger(index) || index < 0 || index >= this._wordLabels.length) {
            Logger.warn('WordAreaController', `无效的节点索引: ${index}，期望范围: 0-${this._wordLabels.length - 1}`);
            return null;
        }

        const label = this._wordLabels[index];
        if (!label || !label.isValid) {
            Logger.warn('WordAreaController', `位置 ${index} 的标签组件无效`);
            return null;
        }

        return label.string || null;
    }

    /**
     * 清空所有单词显示
     */
    public clearAllWords(): void {
        for (const label of this._wordLabels) {
            if (label) {
                label.string = '';
            }
        }
    }

    /**
     * 直接设置单词数据（用于预生成模式，无闪烁显示）
     * @param words 单词数据数组
     */
    public setWordsDirectly(words: IWordData[]): void {
        if (!this._isInitialized) {
            Logger.error('WordAreaController', '组件未初始化，无法设置单词');
            return;
        }

        if (!words || words.length === 0) {
            Logger.error('WordAreaController', '单词数据为空');
            return;
        }

        // 更新内部数据
        this._currentLevelWords = [...words];

        // 立即显示单词，无延迟无闪烁
        this._displayWordsToNodes(this._currentLevelWords);
    }

    /**
     * 标记单词为已完成状态
     * @param word 已完成的单词
     * @param useAnimation 是否使用动画过渡（默认true）
     */
    public markWordAsCompleted(word: string, useAnimation: boolean = true): void {
        if (!this._isInitialized || !this._colorThemeManager) {
            Logger.error('WordAreaController', '组件未初始化，无法标记单词状态');
            return;
        }

        const upperWord = word.toUpperCase();

        // 更新状态跟踪
        this._completedWordsStatus.set(upperWord, true);

        // 使用新的动画系统或回退到原有方式
        if (useAnimation && this.wordCompletionAnimator) {
            this._playWordCompletionAnimation(upperWord);
        } else {
            // 回退到原有的颜色过渡动画
            this._updateWordVisualStatus(upperWord, useAnimation);
        }

        Logger.info('WordAreaController', `单词 "${upperWord}" 已标记为完成状态${useAnimation ? '(动画)' : '(即时)'}`);
    }

    /**
     * 检查单词是否已完成
     * @param word 要检查的单词
     * @returns 是否已完成
     */
    public isWordCompleted(word: string): boolean {
        const upperWord = word.toUpperCase();
        return this._completedWordsStatus.get(upperWord) || false;
    }

    /**
     * 获取未完成的单词列表
     * @returns 未完成的单词数组
     */
    public getUncompletedWords(): string[] {
        if (!this._isInitialized || GameUtils.isArrayEmpty(this._currentLevelWords)) {
            Logger.warn('WordAreaController', '组件未初始化或没有单词数据');
            return [];
        }

        const uncompletedWords: string[] = [];

        for (const wordData of this._currentLevelWords) {
            const upperWord = wordData.word.toUpperCase();
            if (!this._completedWordsStatus.get(upperWord)) {
                uncompletedWords.push(upperWord);
            }
        }

        return uncompletedWords;
    }

    /**
     * 更新单词的视觉状态
     * @param word 要更新的单词（大写）
     * @param useAnimation 是否使用动画过渡（默认false，保持向后兼容）
     */
    private _updateWordVisualStatus(word: string, useAnimation: boolean = false): void {
        if (!this._colorThemeManager) return;

        // 查找对应的Label
        const targetLabel = this._findLabelByWord(word);
        if (!targetLabel) {
            Logger.warn('WordAreaController', `未找到单词 "${word}" 对应的Label`);
            return;
        }

        const isCompleted = this._completedWordsStatus.get(word) || false;
        const targetColor = isCompleted
            ? this._colorThemeManager.getCompletedTextColor()
            : this._colorThemeManager.getDefaultTextColor();

        // 根据配置决定是否使用动画
        if (useAnimation && WordAreaController.ANIMATION_CONFIG.ENABLED && isCompleted) {
            this._animateColorTransition(targetLabel, targetColor, word);
        } else {
            // 即时更新颜色（fallback模式）
            targetLabel.color = targetColor;
            // 移除过详细的DEBUG日志以提升性能
        }
    }

    /**
     * 播放单词完成动画（使用新的动画系统，包含翻译显示）
     * @param word 已完成的单词（大写）
     */
    private _playWordCompletionAnimation(word: string): void {
        // 查找对应的单词节点
        const wordNode = this._findWordNodeByWord(word);
        if (!wordNode) {
            Logger.warn('WordAreaController', `未找到单词 "${word}" 对应的节点，回退到原有动画`);
            this._updateWordVisualStatus(word, true);
            return;
        }

        // 查找单词索引
        const wordIndex = this._findWordIndexByWord(word);
        if (wordIndex === -1) {
            Logger.warn('WordAreaController', `未找到单词 "${word}" 的索引，回退到原有动画`);
            this._updateWordVisualStatus(word, true);
            return;
        }

        // 使用WordCompletionAnimator播放完整动画序列（包含翻译）
        this.wordCompletionAnimator.playWordCompletionWithTranslation(word, wordNode, wordIndex)
            .then(() => {
                Logger.debug('WordAreaController', `单词 "${word}" 完整动画序列播放完成`);
            })
            .catch((error) => {
                Logger.error('WordAreaController', `单词 "${word}" 动画序列播放失败`, error);
                // 发生错误时回退到原有方式
                this._updateWordVisualStatus(word, true);
            });
    }

    /**
     * 查找单词对应的节点
     * @param word 单词（大写）
     * @returns 节点或null
     */
    private _findWordNodeByWord(word: string): Node | null {
        for (let i = 0; i < this._wordLabels.length; i++) {
            const label = this._wordLabels[i];
            if (label && label.string.toUpperCase() === word) {
                return label.node;
            }
        }
        return null;
    }

    /**
     * 查找单词对应的索引
     * @param word 单词（大写）
     * @returns 索引，如果未找到返回-1
     */
    private _findWordIndexByWord(word: string): number {
        for (let i = 0; i < this._wordLabels.length; i++) {
            const label = this._wordLabels[i];
            if (label && label.string.toUpperCase() === word) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 查找单词对应的Label组件
     * @param word 单词（大写）
     * @returns Label组件或null
     */
    private _findLabelByWord(word: string): Label | null {
        for (let i = 0; i < this._wordLabels.length; i++) {
            const label = this._wordLabels[i];
            if (label && label.string.toUpperCase() === word) {
                return label;
            }
        }
        return null;
    }

    /**
     * 播放颜色过渡动画
     * @param label 目标Label组件
     * @param targetColor 目标颜色
     * @param word 单词（用于跟踪）
     */
    private _animateColorTransition(label: Label, targetColor: Color, word: string): void {
        // 停止该单词的现有动画
        this._stopWordAnimation(word);

        const startColor = new Color(label.color);
        const animationTarget = { r: startColor.r, g: startColor.g, b: startColor.b };

        // 创建颜色过渡动画
        const colorTween = tween(animationTarget)
            .to(WordAreaController.ANIMATION_CONFIG.DURATION, {
                r: targetColor.r,
                g: targetColor.g,
                b: targetColor.b
            }, {
                easing: WordAreaController.ANIMATION_CONFIG.EASING,
                onUpdate: () => {
                    // 实时更新Label颜色
                    label.color = new Color(
                        Math.round(animationTarget.r),
                        Math.round(animationTarget.g),
                        Math.round(animationTarget.b),
                        targetColor.a
                    );
                },
                onComplete: () => {
                    // 动画完成，确保颜色精确
                    label.color = targetColor;
                    this._activeAnimations.delete(word);
                }
            })
            .start();

        // 记录动画以便管理
        this._activeAnimations.set(word, colorTween);
    }

    /**
     * 停止指定单词的动画
     * @param word 单词（大写）
     */
    private _stopWordAnimation(word: string): void {
        const existingTween = this._activeAnimations.get(word);
        if (existingTween) {
            existingTween.stop();
            this._activeAnimations.delete(word);
            Logger.debug('WordAreaController', `停止单词 "${word}" 的动画`);
        }
    }

    /**
     * 停止所有正在播放的动画
     */
    private _stopAllAnimations(): void {
        this._activeAnimations.forEach((tween, word) => {
            tween.stop();
            Logger.debug('WordAreaController', `停止单词 "${word}" 的动画`);
        });
        this._activeAnimations.clear();
        Logger.info('WordAreaController', '所有颜色动画已停止');
    }

    /**
     * 组件销毁时清理资源
     */
    onDestroy(): void {
        // 停止所有动画
        this._stopAllAnimations();

        // 清理数组引用
        if (this._wordLabels) {
            this._wordLabels = [];
        }
        if (this._currentLevelWords) {
            this._currentLevelWords = [];
        }

        // 清理状态跟踪
        if (this._completedWordsStatus) {
            this._completedWordsStatus.clear();
        }

        // 清理动画跟踪
        if (this._activeAnimations) {
            this._activeAnimations.clear();
        }

        // 清理颜色管理器引用
        this._colorThemeManager = null;
    }

    /**
     * 设置动画启用状态
     * @param enabled 是否启用动画
     */
    public static setAnimationEnabled(enabled: boolean): void {
        (WordAreaController.ANIMATION_CONFIG as any).ENABLED = enabled;
        Logger.info('WordAreaController', `颜色过渡动画${enabled ? '已启用' : '已禁用'}`);
    }

    /**
     * 获取动画启用状态
     * @returns 是否启用动画
     */
    public static isAnimationEnabled(): boolean {
        return WordAreaController.ANIMATION_CONFIG.ENABLED;
    }

    /**
     * 获取当前正在播放的动画数量
     * @returns 动画数量
     */
    public getActiveAnimationCount(): number {
        return this._activeAnimations.size;
    }
}
