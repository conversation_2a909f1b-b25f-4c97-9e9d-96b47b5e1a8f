import { IWordData } from '../../Data/WordDatabase';
import { LetterPosition } from '../../Game/Constants/GameConstants';
import { Logger } from '../../Utils/Logger';

/**
 * 单词验证器
 *
 * 负责验证用户连接的字母路径是否构成有效的目标单词，支持双向验证和
 * 复杂的路径分析。遵循单一职责原则，专注于单词验证逻辑的实现。
 *
 * 核心功能：
 * - 单词路径验证：检查字母连接路径是否构成有效单词
 * - 双向验证支持：支持正向和反向单词识别
 * - 路径连通性检查：验证字母连接路径的有效性
 * - 单词去重处理：避免重复验证相同的单词
 * - 目标单词管理：维护当前关卡的目标单词列表
 *
 * 验证算法：
 * - 路径提取：从字母位置序列提取单词字符串
 * - 正向匹配：检查提取的单词是否在目标列表中
 * - 反向匹配：检查单词的反向拼写是否在目标列表中
 * - 连通性验证：确保路径中相邻字母在网格中确实相邻
 * - 重复检查：避免同一单词被多次验证
 *
 * 技术特性：
 * - 高效的Map数据结构用于快速单词查找
 * - 缓存机制避免重复验证开销
 * - 支持最小单词长度限制
 * - 详细的验证日志用于调试和监控
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-07-04
 */
export class WordValidator {

    /**
     * 目标单词列表
     */
    private _targetWords: Map<string, IWordData> = new Map();

    /**
     * 已验证的单词缓存
     */
    private _validatedWords: Set<string> = new Set();

    /**
     * 最小单词长度
     */
    private static readonly MIN_WORD_LENGTH = 3;

    /**
     * 最大单词长度
     */
    private static readonly MAX_WORD_LENGTH = 8;

    /**
     * 构造函数
     */
    constructor() {
        // 单词验证器初始化
    }

    /**
     * 设置目标单词
     * @param words 目标单词数组
     */
    public setTargetWords(words: IWordData[]): void {
        this._targetWords.clear();
        this._validatedWords.clear();

        for (const word of words) {
            const upperWord = word.word.toUpperCase();
            this._targetWords.set(upperWord, word);
        }

    }

    /**
     * 验证单词是否有效（支持双向连接和共享字母）
     * @param word 要验证的单词
     * @param path 连线路径
     * @returns 是否有效
     */
    public validateWord(word: string, path: LetterPosition[]): boolean {
        const upperWord = word.toUpperCase();

        // 基本验证
        if (!this._isValidWordLength(upperWord)) {
            return false;
        }

        if (!this._isValidPath(path)) {
            return false;
        }

        if (this._isAlreadyValidated(upperWord)) {
            return false;
        }

        // 验证路径连续性
        if (!this._isPathContinuous(path)) {
            return false;
        }

        // 使用增强的双向验证算法
        const validationResult = this._validateWordWithBidirectionalSupport(upperWord, path);
        if (!validationResult.isValid) {
            return false;
        }

        // 标记为已验证
        this._validatedWords.add(upperWord);
        return true;
    }

    /**
     * 双向单词验证算法
     * @param word 要验证的单词
     * @param path 连线路径
     * @returns 验证结果
     */
    private _validateWordWithBidirectionalSupport(word: string, path: LetterPosition[]): {isValid: boolean, direction: 'forward' | 'reverse' | null} {
        if (!this._isTargetWord(word)) {
            return { isValid: false, direction: null };
        }

        // 检查正向匹配
        if (this._doesWordMatchPath(word, path)) {
            return { isValid: true, direction: 'forward' };
        }

        // 检查反向匹配
        const reversedWord = word.split('').reverse().join('');
        if (this._isTargetWord(reversedWord) && this._doesWordMatchPath(reversedWord, path)) {
            return { isValid: true, direction: 'reverse' };
        }

        return { isValid: false, direction: null };
    }

    /**
     * 检查单词长度是否有效
     * @param word 单词
     * @returns 是否有效
     */
    private _isValidWordLength(word: string): boolean {
        return word.length >= WordValidator.MIN_WORD_LENGTH && 
               word.length <= WordValidator.MAX_WORD_LENGTH;
    }

    /**
     * 检查路径是否有效
     * @param path 路径
     * @returns 是否有效
     */
    private _isValidPath(path: LetterPosition[]): boolean {
        return path && path.length >= WordValidator.MIN_WORD_LENGTH;
    }

    /**
     * 检查是否是目标单词
     * @param word 单词
     * @returns 是否是目标单词
     */
    private _isTargetWord(word: string): boolean {
        return this._targetWords.has(word);
    }

    /**
     * 检查单词是否已经被验证过
     * @param word 单词
     * @returns 是否已验证
     */
    private _isAlreadyValidated(word: string): boolean {
        return this._validatedWords.has(word);
    }

    /**
     * 检查路径是否连续
     * @param path 路径
     * @returns 是否连续
     */
    private _isPathContinuous(path: LetterPosition[]): boolean {
        if (path.length < 2) return true;

        for (let i = 1; i < path.length; i++) {
            const prev = path[i - 1];
            const curr = path[i];

            if (!this._arePositionsAdjacent(prev, curr)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查两个位置是否相邻
     * @param pos1 位置1
     * @param pos2 位置2
     * @returns 是否相邻
     */
    private _arePositionsAdjacent(pos1: LetterPosition, pos2: LetterPosition): boolean {
        const rowDiff = Math.abs(pos1.row - pos2.row);
        const colDiff = Math.abs(pos1.col - pos2.col);
        
        // 相邻包括8个方向：上下左右和4个对角线
        return rowDiff <= 1 && colDiff <= 1 && (rowDiff + colDiff > 0);
    }

    /**
     * 检查单词是否与路径匹配
     * @param word 单词
     * @param path 路径
     * @returns 是否匹配
     */
    private _doesWordMatchPath(word: string, path: LetterPosition[]): boolean {
        if (word.length !== path.length) {
            return false;
        }

        // 这里需要从实际的字母网格中获取字母来验证
        // 由于这个类是纯逻辑类，我们假设调用方已经确保了单词是从路径中正确提取的
        return true;
    }

    /**
     * 从路径提取所有可能的单词（正向和反向）
     * @param path 连线路径
     * @param letterGridController 字母网格控制器
     * @returns 可能的单词数组
     */
    public extractPossibleWordsFromPath(path: LetterPosition[], letterGridController: any): string[] {
        if (!path || path.length === 0) {
            return [];
        }

        // 提取正向单词
        let forwardWord = '';
        for (const pos of path) {
            const letter = letterGridController.getLetterAt(pos.row, pos.col);
            if (letter) {
                forwardWord += letter;
            } else {
                Logger.warn('WordValidator', `位置(${pos.row}, ${pos.col})没有字母`);
            }
        }

        const words = [];
        const upperForwardWord = forwardWord.toUpperCase();

        // 添加正向单词（如果是目标单词）
        if (this._isTargetWord(upperForwardWord)) {
            words.push(upperForwardWord);
        }

        // 添加反向单词（如果是目标单词且与正向不同）
        const reverseWord = upperForwardWord.split('').reverse().join('');
        if (reverseWord !== upperForwardWord && this._isTargetWord(reverseWord)) {
            words.push(reverseWord);
        }
        return words;
    }

    /**
     * 验证路径是否能形成任何有效的目标单词
     * @param path 连线路径
     * @param letterGridController 字母网格控制器
     * @returns 第一个有效的单词，如果没有则返回null
     */
    public findValidWordFromPath(path: LetterPosition[], letterGridController: any): string | null {
        const possibleWords = this.extractPossibleWordsFromPath(path, letterGridController);

        for (const word of possibleWords) {
            if (!this._isAlreadyValidated(word)) {
                return word;
            }
        }

        return null;
    }

    /**
     * 获取路径形成的所有未验证的有效单词
     * @param path 连线路径
     * @param letterGridController 字母网格控制器
     * @returns 未验证的有效单词数组
     */
    public getUnvalidatedWordsFromPath(path: LetterPosition[], letterGridController: any): string[] {
        const possibleWords = this.extractPossibleWordsFromPath(path, letterGridController);
        return possibleWords.filter(word => !this._isAlreadyValidated(word));
    }

    /**
     * 检查是否存在共享起始字母的多个目标单词
     * @param position 字母位置
     * @returns 共享该位置作为起始的目标单词列表
     */
    public getTargetWordsStartingAt(position: LetterPosition, letterGridController: any): string[] {
        if (!letterGridController) return [];

        const startLetter = letterGridController.getLetterAt(position.row, position.col);
        if (!startLetter) return [];

        const targetWords = this.getTargetWords();
        return targetWords.filter(word =>
            word.charAt(0) === startLetter.toUpperCase() && !this._isAlreadyValidated(word)
        );
    }

    /**
     * 获取目标单词信息
     * @param word 单词
     * @returns 单词信息
     */
    public getWordInfo(word: string): IWordData | null {
        const upperWord = word.toUpperCase();
        return this._targetWords.get(upperWord) || null;
    }

    /**
     * 获取所有目标单词
     * @returns 目标单词数组
     */
    public getTargetWords(): string[] {
        return Array.from(this._targetWords.keys());
    }

    /**
     * 获取已验证的单词
     * @returns 已验证单词数组
     */
    public getValidatedWords(): string[] {
        return Array.from(this._validatedWords);
    }

    /**
     * 获取剩余未找到的单词
     * @returns 剩余单词数组
     */
    public getRemainingWords(): string[] {
        const allWords = this.getTargetWords();
        return allWords.filter(word => !this._validatedWords.has(word));
    }

    /**
     * 检查是否所有单词都已找到
     * @returns 是否全部完成
     */
    public isAllWordsFound(): boolean {
        return this._validatedWords.size === this._targetWords.size;
    }

    /**
     * 获取完成进度
     * @returns 完成进度（0-1）
     */
    public getProgress(): number {
        if (this._targetWords.size === 0) return 0;
        return this._validatedWords.size / this._targetWords.size;
    }

    /**
     * 重置验证状态
     */
    public reset(): void {
        this._validatedWords.clear();
    }

    /**
     * 获取验证统计信息
     * @returns 统计信息
     */
    public getStats(): ValidationStats {
        return {
            totalWords: this._targetWords.size,
            foundWords: this._validatedWords.size,
            remainingWords: this._targetWords.size - this._validatedWords.size,
            progress: this.getProgress(),
            isCompleted: this.isAllWordsFound()
        };
    }

    /**
     * 验证路径是否包含重复位置
     * @param path 路径
     * @returns 是否包含重复
     */
    public hasRepeatedPositions(path: LetterPosition[]): boolean {
        const positionSet = new Set<string>();
        
        for (const pos of path) {
            const key = `${pos.row}-${pos.col}`;
            if (positionSet.has(key)) {
                return true;
            }
            positionSet.add(key);
        }
        
        return false;
    }

    /**
     * 获取路径的方向信息
     * @param path 路径
     * @returns 方向信息
     */
    public getPathDirection(path: LetterPosition[]): PathDirection {
        if (path.length < 2) {
            return PathDirection.NONE;
        }

        const start = path[0];
        const end = path[path.length - 1];
        
        const rowDiff = end.row - start.row;
        const colDiff = end.col - start.col;

        // 判断主要方向
        if (rowDiff === 0) {
            return colDiff > 0 ? PathDirection.HORIZONTAL_RIGHT : PathDirection.HORIZONTAL_LEFT;
        } else if (colDiff === 0) {
            return rowDiff > 0 ? PathDirection.VERTICAL_DOWN : PathDirection.VERTICAL_UP;
        } else if (Math.abs(rowDiff) === Math.abs(colDiff)) {
            if (rowDiff > 0 && colDiff > 0) return PathDirection.DIAGONAL_DOWN_RIGHT;
            if (rowDiff > 0 && colDiff < 0) return PathDirection.DIAGONAL_DOWN_LEFT;
            if (rowDiff < 0 && colDiff > 0) return PathDirection.DIAGONAL_UP_RIGHT;
            if (rowDiff < 0 && colDiff < 0) return PathDirection.DIAGONAL_UP_LEFT;
        }

        return PathDirection.IRREGULAR;
    }

    // ==================== 网格可连接性验证方法组 ====================

    /**
     * 验证字母网格中所有目标单词的可连接性
     * 确保每个目标单词都能在网格中找到至少一条有效的连接路径
     * @param letterGrid 字母网格 (9x8)
     * @returns 验证结果对象
     */
    public validateGridConnectivity(letterGrid: string[][]): GridConnectivityResult {
        if (!letterGrid || letterGrid.length !== 9 || letterGrid[0]?.length !== 8) {
            return {
                isValid: false,
                connectableWords: [],
                unconnectableWords: Array.from(this._targetWords.keys()),
                totalWords: this._targetWords.size,
                connectableCount: 0,
                validationDetails: []
            };
        }

        const connectableWords: string[] = [];
        const unconnectableWords: string[] = [];
        const validationDetails: WordValidationDetail[] = [];

        // 验证每个目标单词
        for (const [word, wordData] of this._targetWords) {
            const validationResult = this._findWordInGrid(word, letterGrid);

            if (validationResult.found) {
                connectableWords.push(word);
                validationDetails.push({
                    word: word,
                    isConnectable: true,
                    foundPaths: validationResult.paths,
                    reason: `找到 ${validationResult.paths.length} 条有效路径`
                });
            } else {
                unconnectableWords.push(word);
                validationDetails.push({
                    word: word,
                    isConnectable: false,
                    foundPaths: [],
                    reason: validationResult.reason || '未找到有效连接路径'
                });
            }
        }

        const isValid = unconnectableWords.length === 0;

        if (!isValid) {
            Logger.warn('WordValidator', `网格连接性验证失败：${unconnectableWords.length} 个单词无法连接: [${unconnectableWords.join(', ')}]`);
        }

        return {
            isValid,
            connectableWords,
            unconnectableWords,
            totalWords: this._targetWords.size,
            connectableCount: connectableWords.length,
            validationDetails
        };
    }

    /**
     * 在字母网格中查找指定单词的所有可能路径
     * @param word 目标单词
     * @param letterGrid 字母网格
     * @returns 查找结果
     */
    private _findWordInGrid(word: string, letterGrid: string[][]): WordSearchResult {
        const paths: LetterPosition[][] = [];
        const upperWord = word.toUpperCase();

        // 遍历网格中的每个位置作为起始点
        for (let row = 0; row < letterGrid.length; row++) {
            for (let col = 0; col < letterGrid[row].length; col++) {
                // 检查当前位置是否匹配单词的第一个字母
                if (letterGrid[row][col] === upperWord[0]) {
                    // 尝试从当前位置开始搜索单词
                    const foundPaths = this._searchWordFromPosition(upperWord, letterGrid, row, col);
                    paths.push(...foundPaths);
                }
            }
        }

        // 同时检查反向单词
        const reverseWord = upperWord.split('').reverse().join('');
        if (reverseWord !== upperWord && this._targetWords.has(reverseWord)) {
            for (let row = 0; row < letterGrid.length; row++) {
                for (let col = 0; col < letterGrid[row].length; col++) {
                    if (letterGrid[row][col] === reverseWord[0]) {
                        const foundPaths = this._searchWordFromPosition(reverseWord, letterGrid, row, col);
                        paths.push(...foundPaths);
                    }
                }
            }
        }

        return {
            found: paths.length > 0,
            paths: paths,
            reason: paths.length === 0 ? `单词 "${upperWord}" 在网格中未找到有效路径` : undefined
        };
    }

    /**
     * 从指定位置开始搜索单词的所有可能路径
     * @param word 目标单词
     * @param letterGrid 字母网格
     * @param startRow 起始行
     * @param startCol 起始列
     * @returns 找到的路径数组
     */
    private _searchWordFromPosition(word: string, letterGrid: string[][], startRow: number, startCol: number): LetterPosition[][] {
        const foundPaths: LetterPosition[][] = [];
        const visited = new Set<string>();
        const currentPath: LetterPosition[] = [];

        // 深度优先搜索
        this._dfsSearchWord(word, letterGrid, startRow, startCol, 0, currentPath, visited, foundPaths);

        return foundPaths;
    }

    /**
     * 深度优先搜索单词路径
     * @param word 目标单词
     * @param letterGrid 字母网格
     * @param row 当前行
     * @param col 当前列
     * @param wordIndex 当前单词字符索引
     * @param currentPath 当前路径
     * @param visited 已访问位置
     * @param foundPaths 找到的路径集合
     */
    private _dfsSearchWord(
        word: string,
        letterGrid: string[][],
        row: number,
        col: number,
        wordIndex: number,
        currentPath: LetterPosition[],
        visited: Set<string>,
        foundPaths: LetterPosition[][]
    ): void {
        // 边界检查
        if (row < 0 || row >= letterGrid.length || col < 0 || col >= letterGrid[0].length) {
            return;
        }

        // 检查是否已访问
        const posKey = `${row}-${col}`;
        if (visited.has(posKey)) {
            return;
        }

        // 检查字母是否匹配
        if (letterGrid[row][col] !== word[wordIndex]) {
            return;
        }

        // 添加当前位置到路径
        currentPath.push({ row, col });
        visited.add(posKey);

        // 检查是否完成单词
        if (wordIndex === word.length - 1) {
            // 找到完整单词，保存路径
            foundPaths.push([...currentPath]);
        } else {
            // 继续搜索下一个字符
            const directions = [
                [-1, -1], [-1, 0], [-1, 1],  // 上方三个位置
                [0, -1],           [0, 1],   // 左右两个位置
                [1, -1],  [1, 0],  [1, 1]    // 下方三个位置
            ];

            for (const [dr, dc] of directions) {
                this._dfsSearchWord(
                    word,
                    letterGrid,
                    row + dr,
                    col + dc,
                    wordIndex + 1,
                    currentPath,
                    visited,
                    foundPaths
                );
            }
        }

        // 回溯：移除当前位置
        currentPath.pop();
        visited.delete(posKey);
    }

    /**
     * 快速验证网格是否包含所有目标单词（仅检查字母存在性）
     * @param letterGrid 字母网格
     * @returns 是否包含所有必需字母
     */
    public quickValidateGridLetters(letterGrid: string[][]): boolean {
        if (!letterGrid || letterGrid.length !== 9 || letterGrid[0]?.length !== 8) {
            return false;
        }

        // 统计网格中的字母频率
        const gridLetterCount = new Map<string, number>();
        for (const row of letterGrid) {
            for (const letter of row) {
                gridLetterCount.set(letter, (gridLetterCount.get(letter) || 0) + 1);
            }
        }

        // 检查每个目标单词的字母是否都存在
        for (const [word] of this._targetWords) {
            const wordLetterCount = new Map<string, number>();
            for (const letter of word) {
                wordLetterCount.set(letter, (wordLetterCount.get(letter) || 0) + 1);
            }

            // 检查网格是否包含足够的字母
            for (const [letter, needed] of wordLetterCount) {
                const available = gridLetterCount.get(letter) || 0;
                if (available < needed) {
                    Logger.warn('WordValidator', `网格中字母 "${letter}" 不足：需要 ${needed} 个，只有 ${available} 个`);
                    return false;
                }
            }
        }

        return true;
    }
}

/**
 * 验证统计信息接口
 */
export interface ValidationStats {
    totalWords: number;
    foundWords: number;
    remainingWords: number;
    progress: number;
    isCompleted: boolean;
}

/**
 * 路径方向枚举
 */
export enum PathDirection {
    NONE = 'none',
    HORIZONTAL_LEFT = 'horizontal_left',
    HORIZONTAL_RIGHT = 'horizontal_right',
    VERTICAL_UP = 'vertical_up',
    VERTICAL_DOWN = 'vertical_down',
    DIAGONAL_UP_LEFT = 'diagonal_up_left',
    DIAGONAL_UP_RIGHT = 'diagonal_up_right',
    DIAGONAL_DOWN_LEFT = 'diagonal_down_left',
    DIAGONAL_DOWN_RIGHT = 'diagonal_down_right',
    IRREGULAR = 'irregular'
}

/**
 * 网格连接性验证结果接口
 */
export interface GridConnectivityResult {
    /** 是否所有单词都可连接 */
    isValid: boolean;
    /** 可连接的单词列表 */
    connectableWords: string[];
    /** 无法连接的单词列表 */
    unconnectableWords: string[];
    /** 总单词数 */
    totalWords: number;
    /** 可连接单词数 */
    connectableCount: number;
    /** 详细验证信息 */
    validationDetails: WordValidationDetail[];
}

/**
 * 单词验证详情接口
 */
export interface WordValidationDetail {
    /** 单词 */
    word: string;
    /** 是否可连接 */
    isConnectable: boolean;
    /** 找到的路径 */
    foundPaths: LetterPosition[][];
    /** 验证原因 */
    reason: string;
}

/**
 * 单词搜索结果接口
 */
export interface WordSearchResult {
    /** 是否找到 */
    found: boolean;
    /** 找到的路径 */
    paths: LetterPosition[][];
    /** 失败原因 */
    reason?: string;
}
